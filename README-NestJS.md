# AMAP Report Management Service - NestJS

A comprehensive assessment report management service built with NestJS, providing APIs for managing assessment reports, analytics, and candidate performance data.

## Features

- **Assessment Management**: Create, update, and retrieve assessment data
- **Candidate Analytics**: Track candidate performance and generate reports
- **Real-time Messaging**: Kafka integration for event-driven architecture
- **Caching**: Redis-based caching for improved performance
- **Security**: JWT-based authentication and authorization
- **Database**: AWS DynamoDB integration
- **API Documentation**: Swagger/OpenAPI documentation
- **Monitoring**: Health checks and logging

## Tech Stack

- **Framework**: NestJS with TypeScript
- **Database**: AWS DynamoDB
- **Cache**: Redis
- **Message Queue**: Apache Kafka
- **Authentication**: JWT with Passport.js
- **Documentation**: Swagger/OpenAPI
- **Testing**: Jest
- **Containerization**: Docker

## Getting Started

### Prerequisites

- Node.js 18+
- Docker and Docker Compose
- AWS CLI (for production)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd amap-report-management-service
```

2. Install dependencies:
```bash
npm install
```

3. Copy environment variables:
```bash
cp .env.example .env
```

4. Update the `.env` file with your configuration values.

### Development

#### Using Docker Compose (Recommended)

```bash
# Start all services (app, redis, kafka, dynamodb-local)
docker-compose -f docker-compose.nestjs.yml up

# Start in detached mode
docker-compose -f docker-compose.nestjs.yml up -d

# View logs
docker-compose -f docker-compose.nestjs.yml logs -f app
```

#### Local Development

```bash
# Start development server
npm run start:dev

# Start in debug mode
npm run start:debug
```

### Testing

```bash
# Run unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run test coverage
npm run test:cov

# Run e2e tests
npm run test:e2e
```

### Building for Production

```bash
# Build the application
npm run build

# Start production server
npm run start:prod
```

## API Documentation

Once the application is running, you can access the Swagger documentation at:
- Local: http://localhost:3000/api
- The API documentation provides detailed information about all available endpoints, request/response schemas, and authentication requirements.

## Project Structure

```
src/
├── auth/                 # Authentication module
├── common/              # Shared utilities, constants, DTOs
│   ├── constants/       # Application constants
│   ├── dto/            # Data Transfer Objects
│   ├── exceptions/     # Custom exceptions
│   ├── filters/        # Exception filters
│   ├── interceptors/   # Request/Response interceptors
│   └── utils/          # Utility functions
├── config/             # Configuration modules
│   ├── database/       # Database configuration
│   ├── kafka/          # Kafka configuration
│   └── redis/          # Redis configuration
├── entities/           # Database entities
├── modules/            # Feature modules
│   └── assessment/     # Assessment-related functionality
├── repositories/       # Data access layer
└── services/          # Business logic layer
```

## Environment Variables

Key environment variables (see `.env.example` for complete list):

- `NODE_ENV`: Application environment (development/production)
- `PORT`: Application port (default: 3000)
- `JWT_SECRET`: Secret key for JWT tokens
- `AWS_REGION`: AWS region for DynamoDB
- `REDIS_HOST`: Redis server host
- `KAFKA_BOOTSTRAP_SERVERS`: Kafka broker addresses

## API Endpoints

### Authentication
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh JWT token

### Assessments
- `GET /` - Welcome message
- `GET /health` - Health check
- `GET /organizations/{organizationId}/assessments/{assessmentId}/basic-metrics/general-details` - Get general assessment details
- `GET /organizations/{organizationId}/assessments/{assessmentId}/assessment-details` - Get detailed assessment information
- `GET /organizations/{organizationId}/assessments/{assessmentId}/candidate-metrics` - Get candidate metrics
- `GET /organizations/{organizationId}/assessments/{assessmentId}/comparative-analysis` - Get comparative analysis
- `GET /organizations/{organizationId}/assessments/{assessmentId}/feedbacks` - Get assessment feedbacks
- `GET /organizations/{organizationId}/assessments/{assessmentId}/all-details` - Get all assessment details

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
