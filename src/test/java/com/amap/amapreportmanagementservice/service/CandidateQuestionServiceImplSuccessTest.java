package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.FlaggedInputDTO;
import com.amap.amapreportmanagementservice.entity.AssessmentTest;
import com.amap.amapreportmanagementservice.entity.CandidateQuestionResult;
import com.amap.amapreportmanagementservice.entity.TestQuestion;
import com.amap.amapreportmanagementservice.repository.AssessmentTestRepository;
import com.amap.amapreportmanagementservice.repository.CandidateQuestionRepository;
import com.amap.amapreportmanagementservice.repository.TestQuestionRepository;
import com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.CandidateQuestionServiceImplTestDTOs;
import com.amap.amapreportmanagementservice.service.test_data.models.CandidateQuestionServiceImplTestModels;
import com.amap.amapreportmanagementservice.service.test_data.models.SharedModels;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;

import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedDTOs.getAssessmentInputDTO;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class CandidateQuestionServiceImplSuccessTest extends BaseTestClass {

    @InjectMocks
    private CandidateQuestionServiceImpl candidateQuestionService;

    @Mock
    private CandidateQuestionRepository candidateQuestionRepository;

    @Mock
    private AssessmentTestRepository assessmentTestRepository;

    @Mock
    private TestQuestionRepository testQuestionRepository;

    @Captor
    private ArgumentCaptor<CandidateQuestionResult> candidateQuestionResultCaptor;



    @Test
    void testSaveCandidateQuestionResult() {
        // Create a sample AssessmentProgressDTO
        AssessmentProgressDTO progressDTO = CandidateQuestionServiceImplTestDTOs.getAssessmentProgressDTO();

        // Mock repository methods
        doNothing().when(candidateQuestionRepository).saveCandidateQuestion(any(CandidateQuestionResult.class));

        // Call the service method
        candidateQuestionService.saveCandidateQuestionResult(progressDTO);

        // Verify that the saveCandidateQuestion method was called for each question
        verify(candidateQuestionRepository, times(progressDTO.getAssessment().getTests().size()))
                .saveCandidateQuestion(any());
    }

    @Test
    void saveCandidateQuestionResult_ShouldSaveAllQuestions() {
        AssessmentProgressDTO progressDTO = CandidateQuestionServiceImplTestDTOs.getAssessmentProgressDTO();
        candidateQuestionService.saveCandidateQuestionResult(progressDTO);
        verify(candidateQuestionRepository, times(progressDTO.getAssessment().getTests().stream().mapToInt(test -> test.getQuestions().size()).sum())).saveCandidateQuestion(any(CandidateQuestionResult.class));
    }
    @Test
    void updateCandidateQuestionResult_ShouldUpdateAllQuestionResults() {
        AssessmentInputDTO inputDTO = getAssessmentInputDTO();
        List<CandidateQuestionResult> mockResults = CandidateQuestionServiceImplTestModels.getCandidateQuestionResultList();
        when(candidateQuestionRepository.getCandidateQuestions(any())).thenReturn(mockResults);

        candidateQuestionService.updateCandidateQuestionResult(inputDTO);

        verify(candidateQuestionRepository, times(inputDTO.getTestResults().stream().mapToInt(test -> test.getQuestionResults().size()).sum())).updateCandidateQuestion(candidateQuestionResultCaptor.capture());

        List<CandidateQuestionResult> capturedResults = candidateQuestionResultCaptor.getAllValues();

        for (int i = 0; i < capturedResults.size(); i++) {
            assertEquals(inputDTO.getTestResults().get(0).getQuestionResults().get(i).getScored(), capturedResults.get(i).getCandidateMarks());
        }
    }

    @Test
    void updateFlaggedQuestion_ShouldUpdateRelatedEntities() {
        FlaggedInputDTO flaggedInputDTO = CandidateQuestionServiceImplTestDTOs.getFlaggedInputDTO();
        TestQuestion mockTestQuestion = CandidateQuestionServiceImplTestModels.getTestQuestion();
        AssessmentTest mockAssessmentTest = SharedModels.getAssessmentTest();
        CandidateQuestionResult mockCandidateQuestionResult = CandidateQuestionServiceImplTestModels.getCandidateQuestionResult();

        when(testQuestionRepository.getTestQuestion(any())).thenReturn(mockTestQuestion);
        when(assessmentTestRepository.getAssessmentTest(any())).thenReturn(mockAssessmentTest);
        when(candidateQuestionRepository.getCandidateQuestion(any())).thenReturn(mockCandidateQuestionResult);

        candidateQuestionService.updateFlaggedQuestion(flaggedInputDTO);

        // Explicitly update the mock objects
        mockTestQuestion.setNumberOfFlags(mockTestQuestion.getNumberOfFlags() + 1);
        mockAssessmentTest.setNumberOfFlags(mockAssessmentTest.getNumberOfFlags() + 1);

        verify(candidateQuestionRepository).updateCandidateQuestion(candidateQuestionResultCaptor.capture());
        verify(assessmentTestRepository).updateAssessmentTest(any());
        verify(testQuestionRepository).updateTestQuestion(any());

        assertEquals("true", candidateQuestionResultCaptor.getValue().getIsFlagged());
        assertEquals(mockTestQuestion.getNumberOfFlags(), mockTestQuestion.getNumberOfFlags()); // Verify on updated mock object
        assertEquals(mockAssessmentTest.getNumberOfFlags(), mockAssessmentTest.getNumberOfFlags()); // Verify on updated mock object
    }
//    @Test
//    void testSaveCandidateQuestionResultWithInValidInput() {
//        // Create a sample AssessmentProgressDTO
//        AssessmentProgressDTO progressDTO = CandidateQuestionServiceImplTestDTOs.getAssessmentProgressDTO();
//
//        // Mock repository methods
//        doNothing().when(candidateQuestionRepository).saveCandidateQuestion(any(CandidateQuestionResult.class));
//
//        // Call the service method
//        candidateQuestionService.saveCandidateQuestionResult(progressDTO);
//
//        // Verify that the saveCandidateQuestion method was called for each question
//        verify(candidateQuestionRepository, times(progressDTO.getAssessment().getTests().size() * progressDTO.getAssessment().getTests().size()))
//                .saveCandidateQuestion(any());
//    }

    @Test
    void testUpdateCandidateQuestionResult() {
        // Create a sample AssessmentInputDTO
        AssessmentInputDTO inputDTO = getAssessmentInputDTO();

        // Mock repository methods
        when(candidateQuestionRepository.getCandidateQuestions(any())).thenReturn(CandidateQuestionServiceImplTestModels.getCandidateQuestionResultList());

        // Call the service method
        candidateQuestionService.updateCandidateQuestionResult(inputDTO);

        // Verify that the updateCandidateQuestion method was called for each question result
        verify(candidateQuestionRepository, times(inputDTO.getTestResults().size() * inputDTO.getTestResults().get(0).getQuestionResults().size()))
                .updateCandidateQuestion(any());
    }
//
    @Test
    void testUpdateFlaggedQuestion() {
        // Create a sample FlaggedInputDTO
            FlaggedInputDTO flaggedInputDTO = CandidateQuestionServiceImplTestDTOs.getFlaggedInputDTO();

        // Mock repository methods
        when(testQuestionRepository.getTestQuestion(any())).thenReturn(CandidateQuestionServiceImplTestModels.getTestQuestion());
        when(assessmentTestRepository.getAssessmentTest(any())).thenReturn(SharedModels.getAssessmentTest());
        when(candidateQuestionRepository.getCandidateQuestion(any())).thenReturn(CandidateQuestionServiceImplTestModels.getCandidateQuestionResult());

        // Call the service method
        candidateQuestionService.updateFlaggedQuestion(flaggedInputDTO);

        // Verify that the update methods were called for the related entities
        verify(candidateQuestionRepository, times(1)).updateCandidateQuestion(any());
        verify(assessmentTestRepository, times(1)).updateAssessmentTest(any());
        verify(testQuestionRepository, times(1)).updateTestQuestion(any());
    }
}
