package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.entity.CandidateTest;
import com.amap.amapreportmanagementservice.repository.CandidateTestRepository;
import com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.CandidateTestServiceImplTestDTOs;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedDTOs.getAssessmentInputDTO;
import static org.mockito.Mockito.*;

class CandidateTestServiceImplSuccessTest extends BaseTestClass{

    @InjectMocks
    private CandidateTestServiceImpl candidateTestService;

    @Mock
    private CandidateTestRepository candidateTestRepository;

    @Test
    void saveCandidateTest() {
        // Prepare test data
        AssessmentProgressDTO assessmentProgressDTO = CandidateTestServiceImplTestDTOs.getAssessmentProgressDTO();
        // Set assessmentProgressDTO properties

        // Mock the behavior of candidateTestRepository
        doNothing().when(candidateTestRepository).saveCandidateTest(any(CandidateTest.class));

        // Call the method to be tested
        candidateTestService.saveCandidateTest(assessmentProgressDTO);

        // Verify that the repository method was called with the expected parameters
        verify(candidateTestRepository, times(assessmentProgressDTO.getAssessment().getTests().size()))
                .saveCandidateTest(any(CandidateTest.class));
    }

    @Test
    void updateCandidateTest() {
        // Prepare test data
        AssessmentInputDTO assessmentInputDTO = getAssessmentInputDTO();
        // Set assessmentInputDTO properties

        // Mock the behavior of candidateTestRepository
        when(candidateTestRepository.getSpecificCandidateTest(any())).thenReturn(new CandidateTest());
        doNothing().when(candidateTestRepository).updateCandidateTest(any(CandidateTest.class));

        // Call the method to be tested
        candidateTestService.updateCandidateTest(assessmentInputDTO);

        // Verify that the repository methods were called with the expected parameters
        verify(candidateTestRepository, times(assessmentInputDTO.getTestResults().size()))
                .getSpecificCandidateTest(any());
        verify(candidateTestRepository, times(assessmentInputDTO.getTestResults().size()))
                .updateCandidateTest(any(CandidateTest.class));
    }

    @Test
    void saveCandidateTest_ShouldSaveEachCandidateTest() {
        AssessmentProgressDTO assessmentProgressDTO = CandidateTestServiceImplTestDTOs.getAssessmentProgressDTO();

        doNothing().when(candidateTestRepository).saveCandidateTest(any(CandidateTest.class));

        candidateTestService.saveCandidateTest(assessmentProgressDTO);

        verify(candidateTestRepository, times(assessmentProgressDTO.getAssessment().getTests().size()))
                .saveCandidateTest(any(CandidateTest.class));
    }

    @Test
    void updateCandidateTest_ShouldUpdateCandidateTest() {
        AssessmentInputDTO assessmentInputDTO = getAssessmentInputDTO();
        CandidateTest mockCandidateTest = new CandidateTest();

        when(candidateTestRepository.getSpecificCandidateTest(any())).thenReturn(mockCandidateTest);
        doNothing().when(candidateTestRepository).updateCandidateTest(any(CandidateTest.class));

        candidateTestService.updateCandidateTest(assessmentInputDTO);

        verify(candidateTestRepository).getSpecificCandidateTest(any());
        verify(candidateTestRepository).updateCandidateTest(mockCandidateTest); //verify the mock object is the same one passed.
    }
}