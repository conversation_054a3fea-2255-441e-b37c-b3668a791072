package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.AverageTimesDTO;
import com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221.CompletionRateDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateAssessmentDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateTestDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.entity.Assessment;
import com.amap.amapreportmanagementservice.entity.CandidateAssessment;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.AssessmentRepository;
import com.amap.amapreportmanagementservice.repository.CandidateAssessmentRepositoryImpl;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class AssessmentServiceImplTest extends BaseTestClass{
    @Mock
    private  AssessmentRepository assessmentRepository;
    @InjectMocks
    private  AssessmentServiceImpl assessmentService;

    @Mock
    private CandidateAssessmentRepositoryImpl candidateAssessmentRepository;


    @Test
    void saveAssessment(){
        String pk = KeyBuilder.assessmentPK("1234", "1234");
        String sk = KeyBuilder.assessmentSK("1234", "1234");

        Assessment assessment =  Assessment.builder()
                .title("default title")
                .assessmentId("1234").
                build();

        AssessmentProgressDTO assessmentProgressDTO = new AssessmentProgressDTO();
        CandidateAssessmentDTO candidateAssessmentDTO = new CandidateAssessmentDTO();
        CandidateTestDTO candidateTestDTO = new CandidateTestDTO();
        List<CandidateTestDTO> testDTOS = new ArrayList<>();
        testDTOS.add(candidateTestDTO);
        candidateAssessmentDTO.setId("1234");
        candidateAssessmentDTO.setAssessmentDuration(10);
        candidateAssessmentDTO.setAssessmentStartTime("");
        candidateAssessmentDTO.setProctor("Default Level");
        candidateAssessmentDTO.setSystem("true");
        candidateAssessmentDTO.setInstructions("");
        candidateAssessmentDTO.setTests(testDTOS);


        assessmentProgressDTO.setAssessment(candidateAssessmentDTO);
        assessmentProgressDTO.setOrganizationId("1234");
        assessmentProgressDTO.setAssessmentId("1234");
        assessmentProgressDTO.setId("1234");

        List<String> testIds = new ArrayList<>();

        when(assessmentRepository.checkIfExist(pk, sk)).thenReturn(false);

        assessment.setPk(pk);
        assessment.setSk(sk);
        assessment.setOrganizationId("1234");
        assessment.setAssessmentId("1234");
        assessment.setTitle("Title");
        assessment.setSystem(true);
        assessment.setAssessmentDuration(1000);
        assessment.setTestsIds(testIds);
        assessment.setProgressCount(10);
        doNothing().when(assessmentRepository).saveAssessment(assessment);
        assessmentService.saveAssessment(assessmentProgressDTO);
        verify(assessmentRepository).saveAssessment(any(Assessment.class));



    }

    @Test
    void saveExistingAssessment(){
        String organizationId = "1234";
        String assessmentId = "1234";
        String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
        String sk = KeyBuilder.assessmentSK(organizationId, assessmentId);
        Key assessmentKey = Key.builder().partitionValue(pk).sortValue(sk).build();

        Assessment savedAssessment = new Assessment();
        savedAssessment.setPk(pk);
        savedAssessment.setSk(sk);
        savedAssessment.setOrganizationId(organizationId);
        savedAssessment.setAssessmentId(assessmentId);
        savedAssessment.setProgressCount(5); // Existing progress count

        AssessmentProgressDTO assessmentProgressDTO = new AssessmentProgressDTO();
        CandidateAssessmentDTO candidateAssessmentDTO = new CandidateAssessmentDTO();
        CandidateTestDTO candidateTestDTO = new CandidateTestDTO();
        List<CandidateTestDTO> testDTOS = new ArrayList<>();
        testDTOS.add(candidateTestDTO);
        candidateAssessmentDTO.setAssessmentDuration(10);
        candidateAssessmentDTO.setAssessmentStartTime("");
        candidateAssessmentDTO.setProctor("Default Level");
        candidateAssessmentDTO.setSystem("true");
        candidateAssessmentDTO.setInstructions("");
        candidateAssessmentDTO.setTests(testDTOS);


        assessmentProgressDTO.setAssessment(candidateAssessmentDTO);

        assessmentProgressDTO.setAssessment(candidateAssessmentDTO);

        // Mock the behavior of assessmentRepository
        when(assessmentRepository.checkIfExist(pk, sk)).thenReturn(true);
        when(assessmentRepository.getAssessment(assessmentKey)).thenReturn(savedAssessment);

        doNothing().when(assessmentRepository).updateAssessment(savedAssessment);
        assessmentRepository.updateAssessment(savedAssessment);
        verify(assessmentRepository).updateAssessment(savedAssessment);

    }

    @Test
    void updateAssessment(){
        String organizationId = "1234";
        String assessmentId = "1234";
        String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
        String sk = KeyBuilder.assessmentSK(organizationId, assessmentId);
        Key assessmentKey = Key.builder().partitionValue(pk).sortValue(sk).build();

        Assessment savedAssessment = new Assessment();
        savedAssessment.setPk(pk);
        savedAssessment.setSk(sk);
        savedAssessment.setOrganizationId(organizationId);
        savedAssessment.setAssessmentId(assessmentId);
        savedAssessment.setProgressCount(5); // Existing progress count

        AssessmentInputDTO assessmentInputDTO = new AssessmentInputDTO();
        assessmentInputDTO.setAssessmentId("1234");
        assessmentInputDTO.setOrganizationId("1234");
        assessmentInputDTO.setStatus("Completed");
        List<TestResultInputDTO> testResultInputDTOS = new ArrayList<>();

        TestResultInputDTO testResultInputDTO = new TestResultInputDTO();
        testResultInputDTO.setTestId("1");
        testResultInputDTO.setTotalScore(1);
        testResultInputDTOS.add(testResultInputDTO);

        assessmentInputDTO.setTestResults(testResultInputDTOS);

        CandidateAssessment candidateAssessment = new CandidateAssessment();
        candidateAssessment.setTitle("Test Title");
        candidateAssessment.setAssessmentId(assessmentId);
        candidateAssessment.setOrganizationId(organizationId);
        candidateAssessment.setProctorLevel("High");
        candidateAssessment.setAssessmentStartTime("12:00:00");
        candidateAssessment.setAssessmentEndTime("12:00:00");

        List<CandidateAssessment> candidateAssessments = new ArrayList<>();
        candidateAssessments.add(candidateAssessment);
        when(candidateAssessmentRepository.getCandidateAssessment(assessmentKey)).thenReturn(candidateAssessments);

        when(assessmentRepository.getAssessment(any(Key.class))).thenReturn(savedAssessment);

        doNothing().when(assessmentRepository).updateAssessment(savedAssessment);
        assessmentService.updateAssessment(assessmentInputDTO);
        verify(assessmentRepository).updateAssessment( savedAssessment);



    }
    @Test
    void saveAssessment_NullInput_ThrowsException() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> assessmentService.saveAssessment(null));
    }

    @Test
    void saveAssessment_EmptyTests_ThrowsException() {
        // Arrange
        AssessmentProgressDTO assessmentProgressDTO = new AssessmentProgressDTO();
        CandidateAssessmentDTO candidateAssessment = new CandidateAssessmentDTO();
        candidateAssessment.setTests(Collections.emptyList());

        assessmentProgressDTO.setAssessment(candidateAssessment);

        // Act & Assert
        assertThrows(ProcessFailedException.class, () -> assessmentService.saveAssessment(assessmentProgressDTO));
    }

    @Test
    void getAssignment(){
        String assessmentTitle = "Assessment Title";
        Assessment savedAssessment = new Assessment();
        String organizationId = "1234";
        String assessmentId = "1234";
        String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
        String sk = KeyBuilder.assessmentSK(organizationId, assessmentId);
        Key assessmentKey = Key.builder().partitionValue(pk).sortValue(sk).build();
        savedAssessment.setPk(pk);
        savedAssessment.setSk(sk);
        savedAssessment.setOrganizationId(organizationId);
        savedAssessment.setTitle("Assessment Title");
        savedAssessment.setAssessmentId(assessmentId);
        savedAssessment.setProgressCount(5);



        when(assessmentRepository.getAssessment(any(Key.class))).thenReturn(savedAssessment).thenReturn(savedAssessment);
        assertEquals(assessmentService.getAssignment(organizationId, assessmentId).getTitle(), assessmentTitle );
    }

    @Test
    void getAssignment_RepositoryThrowsException() {
        // Arrange
        String organizationId = "1234";
        String assessmentId = "1234";

        when(assessmentRepository.getAssessment(any(Key.class))).thenThrow(new RuntimeException("Repository error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> assessmentService.getAssignment(organizationId, assessmentId));
    }

    @Test
    void updateAssessment_NegativeProgressCount_ThrowsException() {
        // Arrange
        String organizationId = "1234";
        String assessmentId = "1234";
        Assessment savedAssessment = new Assessment();
        savedAssessment.setProgressCount(-1);

        when(assessmentRepository.getAssessment(any(Key.class))).thenReturn(savedAssessment);

        // Act & Assert
        assertThrows(ProcessFailedException.class, () -> assessmentService.updateAssessment(new AssessmentInputDTO()));
    }

    @Test
    void getAverageTimes(){
        List<CandidateAssessment> candidateAssessments = new ArrayList<>();

        CandidateAssessment candidateAssessment = new CandidateAssessment();
        candidateAssessment.setTitle("Test Title");
        candidateAssessment.setAssessmentId("1234");
        candidateAssessment.setOrganizationId("1234");
        candidateAssessment.setProctorLevel("High");
        candidateAssessment.setAssessmentStartTime("2023-09-20T11:51:11.219450482Z[Etc/UTC]");
        candidateAssessment.setAssessmentEndTime("2023-09-20T11:51:11.219450482Z[Etc/UTC]");

        candidateAssessments.add(candidateAssessment);

        when(candidateAssessmentRepository.getCandidateAssessment(any(Key.class))).thenReturn(candidateAssessments);

        AverageTimesDTO averageTimesDTO = new AverageTimesDTO();

        averageTimesDTO.setAverageTime(0);
        averageTimesDTO.setLongestTime(0);
        averageTimesDTO.setTotalCandidate(1L);
        averageTimesDTO.setShortestTime(0);

        assertEquals(assessmentService.getAverageTimes("1234", "1234"), averageTimesDTO);
    }

    @Test
    void getCompletionRateTest(){
        List<CandidateAssessment> candidateAssessments = new ArrayList<>();

        CandidateAssessment candidateAssessment = new CandidateAssessment();
        candidateAssessment.setTitle("Test Title");
        candidateAssessment.setAssessmentId("1234");
        candidateAssessment.setOrganizationId("1234");
        candidateAssessment.setProctorLevel("High");
        candidateAssessment.setAssessmentStartTime("2023-09-20T11:51:11.219450482Z[Etc/UTC]");
        candidateAssessment.setAssessmentEndTime("2023-09-20T11:51:11.219450482Z[Etc/UTC]");
        candidateAssessment.setStatus("Completed");

        candidateAssessments.add(candidateAssessment);
        CompletionRateDTO completionRateDTO = new CompletionRateDTO();
        completionRateDTO.setCompletionPercentage(100.00);
        completionRateDTO.setCandidateStart(1);
        completionRateDTO.setCandidateFinished(1);

        when(candidateAssessmentRepository.getCandidateAssessment(any(Key.class))).thenReturn(candidateAssessments);

        assertEquals(assessmentService.getCompletionRate("1234", "1234"), completionRateDTO);


    }

    @Test
    void getCompletionRateTestWithUncompletedAssessment(){
        List<CandidateAssessment> candidateAssessments = new ArrayList<>();

        CandidateAssessment candidateAssessment = new CandidateAssessment();
        candidateAssessment.setTitle("Test Title");
        candidateAssessment.setAssessmentId("1234");
        candidateAssessment.setOrganizationId("1234");
        candidateAssessment.setProctorLevel("High");
        candidateAssessment.setAssessmentStartTime("2023-09-20T11:51:11.219450482Z[Etc/UTC]");
        candidateAssessment.setAssessmentEndTime("2023-09-20T11:51:11.219450482Z[Etc/UTC]");


        candidateAssessments.add(candidateAssessment);
        CompletionRateDTO completionRateDTO = new CompletionRateDTO();
        completionRateDTO.setCompletionPercentage(0.0);
        completionRateDTO.setCandidateStart(1);
        completionRateDTO.setCandidateFinished(0);

        when(candidateAssessmentRepository.getCandidateAssessment(any(Key.class))).thenReturn(candidateAssessments);

        assertEquals(assessmentService.getCompletionRate("1234", "1234"), completionRateDTO);


    }
}
