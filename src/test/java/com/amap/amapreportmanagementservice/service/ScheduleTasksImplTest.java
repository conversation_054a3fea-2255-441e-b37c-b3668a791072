package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.AssessmentResultsDTO;
import com.amap.amapreportmanagementservice.entity.WebhookReportJob;
import com.amap.amapreportmanagementservice.exceptions.CandidateNotFoundException;
import com.amap.amapreportmanagementservice.repository.RepositoryBaseTestClass;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

class ScheduleTasksImplTest extends RepositoryBaseTestClass {
    @Mock
    private RestTemplate mockRestTemplate;

    @Mock
    private WebhookReportJobService mockWebhookReportJobService;

    @Mock
    private ResponseService mockResponseService;

    @InjectMocks
    private ScheduleTasksImpl scheduleTasks;

    @Test
    void autoDisptachReport_Success() {
        // Arrange
        WebhookReportJob job1 = new WebhookReportJob();
        job1.setAssessmentId("assessment1");
        job1.setCandidateId("candidate1");
        job1.setOrganizationId("org1");
        job1.setEmail("<EMAIL>");
        job1.setReportCallbackURL("http://callback-url1.com");

        WebhookReportJob job2 = new WebhookReportJob();
        job2.setAssessmentId("assessment2");
        job2.setCandidateId("candidate2");
        job2.setOrganizationId("org2");
        job2.setEmail("<EMAIL>");
        job2.setReportCallbackURL("http://callback-url2.com");

        List<WebhookReportJob> jobs = Arrays.asList(job1, job2);
        AssessmentResultsDTO result1 = new AssessmentResultsDTO();
        AssessmentResultsDTO result2 = new AssessmentResultsDTO();

        when(mockWebhookReportJobService.getWebhookReportJobs()).thenReturn(jobs);
        when(mockResponseService.getCandidateResults("org1", "assessment1", "candidate1", "<EMAIL>")).thenReturn(result1);
        when(mockResponseService.getCandidateResults("org2", "assessment2", "candidate2", "<EMAIL>")).thenReturn(result2);

        // Act
        scheduleTasks.autoDispatchReport();

        // Assert
        verify(mockRestTemplate, times(1)).postForObject(job1.getReportCallbackURL(), result1, String.class);
        verify(mockRestTemplate, times(1)).postForObject(job2.getReportCallbackURL(), result2, String.class);
        verify(mockWebhookReportJobService, times(1)).deleteWebhookReportJob(job1);
        verify(mockWebhookReportJobService, times(1)).deleteWebhookReportJob(job2);
    }

    @Test
    void autoDispatchReport_CandidateNotFound() {
        // Arrange
        WebhookReportJob job = new WebhookReportJob();
        job.setAssessmentId("assessment1");
        job.setCandidateId("candidate1");
        job.setOrganizationId("org1");
        job.setEmail("<EMAIL>");


        when(mockWebhookReportJobService.getWebhookReportJobs()).thenReturn(List.of(job));
        when(mockResponseService.getCandidateResults("org1", "assessment1", "candidate1", "<EMAIL>"))
                .thenThrow(CandidateNotFoundException.class);

        // Act
        scheduleTasks.autoDispatchReport();

        // Assert
        verify(mockWebhookReportJobService, times(1)).deleteWebhookReportJob(job);
        verify(mockRestTemplate, never()).postForObject(anyString(), any(), any());
    }
}
