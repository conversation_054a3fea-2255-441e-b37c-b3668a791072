package com.amap.amapreportmanagementservice.service;

import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.security.core.context.SecurityContextHolder;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

import java.net.URI;

import static org.mockito.Mockito.when;

public class BaseTestClass {
    @Mock
    private CacheManager cacheManager;

    @Mock
    private Cache cache;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // Mock the cache behavior
        when(cacheManager.getCache("getMissedQuestionsCache")).thenReturn(cache);

        AwsBasicCredentials credentialsProvider = AwsBasicCredentials.create(
                "FAKE", "FAKE"
        );

        var dynamoDbClient = DynamoDbClient.builder()
                .region(Region.US_EAST_2)
                .endpointOverride(URI.create("http://localhost:8001"))
                .credentialsProvider(() -> credentialsProvider).build();

        DynamoDbEnhancedClient.builder()
                .dynamoDbClient(dynamoDbClient)
                .build();

        SecurityContextHolder.clearContext();

    }

}
