package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.entity.AssessmentTest;
import com.amap.amapreportmanagementservice.entity.TestQuestion;
import com.amap.amapreportmanagementservice.repository.AssessmentRepository;
import com.amap.amapreportmanagementservice.repository.AssessmentTestRepository;
import com.amap.amapreportmanagementservice.repository.TestQuestionRepository;
import com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.TestQuestionServiceImplTestDTOs;
import com.amap.amapreportmanagementservice.service.test_data.models.SharedModels;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.ArrayList;
import java.util.List;

import static com.amap.amapreportmanagementservice.constants.DbKeysPrefixes.ASSESSMENT_PREFIX;
import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.*;
import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedDTOs.getAssessmentInputDTO;
import static com.amap.amapreportmanagementservice.service.test_data.models.TestQuestionServiceImplTestModels.getAssessment;
import static org.mockito.Mockito.*;

@Slf4j
class TestQuestionServiceImplSuccessTest extends BaseTestClass {

    @InjectMocks
    private TestQuestionServiceImpl testQuestionService;

    @Mock
    private TestQuestionRepository testQuestionRepository;

    @Mock
    private AssessmentTestRepository assessmentTestRepository;

    @Mock
    private AssessmentRepository assessmentRepository;


    @Test
    void testSaveTestQuestions() {

        // Set up your assessmentProgressDTO as needed
        AssessmentProgressDTO assessmentProgressDTO = TestQuestionServiceImplTestDTOs.getAssessmentProgressDTO();
        // Create mocks and expected behavior for testQuestionRepository methods
        when(testQuestionRepository.checkIfExist(anyString(), anyString(), anyString(), anyString())).thenReturn(false);

        // Call the method to be tested
        testQuestionService.saveTestQuestions(assessmentProgressDTO);

        // Verify that the necessary methods were called
        verify(testQuestionRepository, times(1)).saveTestQuestion(any(TestQuestion.class));
    }

    @Test
    void testUpdateTestQuestions() {
        // Create a sample AssessmentInputDTO
        AssessmentInputDTO assessmentInputDTO = getAssessmentInputDTO();
        // Set up your assessmentInputDTO as needed

        // Create mocks and expected behavior for testQuestionRepository methods
        when(testQuestionRepository.getTestQuestion(any())).thenReturn(TestQuestionServiceImplTestDTOs.getTestQuestion());

        // Call the method to be tested
        testQuestionService.updateTestQuestions(assessmentInputDTO);

        // Verify that the necessary methods were called
        verify(testQuestionRepository, times(1)).updateTestQuestion(any(TestQuestion.class));
    }

    @Test
    public void testGetMissedQuestions() {
        // Create mock data for assessment and testQuestion retrieval
        when(assessmentRepository.getAssessment(any())).thenReturn(getAssessment());
        List<AssessmentTest> assessmentTests = new ArrayList<>();
        assessmentTests.add(SharedModels.getAssessmentTest());
        when(assessmentTestRepository.getAssessmentTestsWithoutCandidateTest(any())).thenReturn(assessmentTests);
        List<TestQuestion> questions = new ArrayList<>();
        questions.add(TestQuestionServiceImplTestDTOs.getTestQuestion());
        when(testQuestionRepository.getTestQuestions(any())).thenReturn(questions);

        // Call the method to be tested
        testQuestionService.getMissedQuestions(organizationId, assessmentId);
        String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
        Key key = Key.builder().partitionValue(pk).sortValue(pk).build();
        Key assessmentTestKey = Key.builder().partitionValue(pk).sortValue(ASSESSMENT_PREFIX).build();
        String testQuestionPk = KeyBuilder.testQuestionPK(organizationId, assessmentId, testId);
        String testQuestionSk = KeyBuilder.testQuestionSK(questionId);
        Key testQuestionKey = Key.builder().partitionValue(testQuestionPk).sortValue(testQuestionSk).build();
        // Verify the behavior and assertions for the returned missedQuestions

        verify(assessmentRepository, times(1)).getAssessment(key);
        verify(assessmentTestRepository, times(1)).getAssessmentTestsWithoutCandidateTest(assessmentTestKey);
        verify(testQuestionRepository, times(1)).getTestQuestions(testQuestionKey);
    }
}
