package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.FeedbackResponseDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateFeedbackDTO;
import com.amap.amapreportmanagementservice.dto.feedback.FeedbackSurveyDTO;
import com.amap.amapreportmanagementservice.dto.feedback.FeedbackSurveyQuestionDTO;
import com.amap.amapreportmanagementservice.entity.CandidateFeedback;
import com.amap.amapreportmanagementservice.entity.Feedback;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.CandidateFeedbackRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.time.ZonedDateTime;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CandidateFeedbackServiceImplTest extends BaseTestClass{
    @Mock
    private CandidateFeedbackRepository candidateFeedbackRepository;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private AssessmentServiceImpl assessmentService;

    @InjectMocks
    private CandidateFeedbackServiceImpl candidateFeedbackService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void saveFeedback_Success() {
        FeedbackSurveyDTO feedbackSurveyDTO = createFeedbackSurveyDTO();
        CandidateFeedback candidateFeedback = new CandidateFeedback();
        List<Feedback> feedbacks = createFeedbackList();

        when(objectMapper.convertValue(any(FeedbackSurveyQuestionDTO.class), eq(Feedback.class)))
                .thenReturn(feedbacks.get(0), feedbacks.get(1), feedbacks.get(2));

        candidateFeedbackService.saveFeedback(feedbackSurveyDTO);

        verify(candidateFeedbackRepository).saveCandidateFeedback(any(CandidateFeedback.class));
        verify(assessmentService).updateAssessmentFeedback(feedbackSurveyDTO);
    }

    @Test
    void saveFeedback_NullInput() {
        assertThrows(ProcessFailedException.class, () -> candidateFeedbackService.saveFeedback(null));
    }

    @Test
    void saveFeedback_EmptyQuestionList() {
        FeedbackSurveyDTO feedbackSurveyDTO = createFeedbackSurveyDTO();
        feedbackSurveyDTO.setSurveyQuestions(new ArrayList<>());

        candidateFeedbackService.saveFeedback(feedbackSurveyDTO);

        verify(candidateFeedbackRepository).saveCandidateFeedback(any(CandidateFeedback.class));
        verify(assessmentService).updateAssessmentFeedback(feedbackSurveyDTO);
    }

    @Test
    void saveFeedback_RepositoryThrowsException() {
        FeedbackSurveyDTO feedbackSurveyDTO = createFeedbackSurveyDTO();
        doThrow(new RuntimeException("DB error")).when(candidateFeedbackRepository).saveCandidateFeedback(any(CandidateFeedback.class));

        assertThrows(ProcessFailedException.class, () -> candidateFeedbackService.saveFeedback(feedbackSurveyDTO));
    }

    @Test
    void saveFeedback_AssessmentServiceThrowsException() {
        FeedbackSurveyDTO feedbackSurveyDTO = createFeedbackSurveyDTO();
        doThrow(new RuntimeException("Assessment error")).when(assessmentService).updateAssessmentFeedback(feedbackSurveyDTO);

        assertThrows(ProcessFailedException.class, () -> candidateFeedbackService.saveFeedback(feedbackSurveyDTO));
    }

    @Test
    void getFeedback_Success() {
        FeedbackSurveyDTO feedbackSurveyDTO = createFeedbackSurveyDTO();
        CandidateFeedback candidateFeedback = createCandidateFeedback();
        CandidateFeedbackDTO expectedDTO = createCandidateFeedbackDTO();

        when(candidateFeedbackRepository.getCandidateFeedback(any(Key.class))).thenReturn(candidateFeedback);
        when(objectMapper.convertValue(candidateFeedback, CandidateFeedbackDTO.class)).thenReturn(expectedDTO);

        CandidateFeedbackDTO actualDTO = candidateFeedbackService.getFeedback(
                feedbackSurveyDTO.getOrganizationId(),
                feedbackSurveyDTO.getAssessmentId(),
                feedbackSurveyDTO.getTestTakerId()
        );

        assertEquals(expectedDTO, actualDTO);
    }

    @Disabled("Temporarily disabled for investigation or other reasons.")
    @Test
    void getFeedback_RepositoryReturnsNull() {
        FeedbackSurveyDTO feedbackSurveyDTO = createFeedbackSurveyDTO();
        String organizationId = feedbackSurveyDTO.getOrganizationId();
        String assessmentId = feedbackSurveyDTO.getAssessmentId();
        String candidateId = feedbackSurveyDTO.getTestTakerId();

        // Mock the repository to return null based on input parameters.
        Mockito.when(candidateFeedbackRepository.getCandidateFeedback(Mockito.any(Key.class))).thenReturn(null);

        Assertions.assertThrows(ProcessFailedException.class, () -> candidateFeedbackService.getFeedback(
                organizationId,
                assessmentId,
                candidateId
        ));
    }

    @Test
    void getAllFeedbacks_RepositoryReturnsEmptyList() {
        FeedbackSurveyDTO feedbackSurveyDTO = createFeedbackSurveyDTO();
        when(candidateFeedbackRepository.getAllFeedbacks(any(Key.class))).thenReturn(new ArrayList<>());

        List<FeedbackResponseDTO> actualDTOs = candidateFeedbackService.getAllFeedbacks(
                feedbackSurveyDTO.getOrganizationId(),
                feedbackSurveyDTO.getAssessmentId()
        );

        assertTrue(actualDTOs.isEmpty());
    }

    @Disabled("Temporarily disabled for investigation or other reasons.")
    @Test
    void getAllFeedbacks_RepositoryThrowsException() {
        FeedbackSurveyDTO feedbackSurveyDTO = createFeedbackSurveyDTO();
        doThrow(new RuntimeException("DB error")).when(candidateFeedbackRepository).getAllFeedbacks(any(Key.class));

        assertThrows(ProcessFailedException.class, () -> candidateFeedbackService.getAllFeedbacks(
                feedbackSurveyDTO.getOrganizationId(),
                feedbackSurveyDTO.getAssessmentId()
        ));
    }

    @Test
    void getAllFeedbacks_NumberFormatException() {
        FeedbackSurveyDTO feedbackSurveyDTO = createFeedbackSurveyDTO();
        CandidateFeedback candidateFeedback = createCandidateFeedback();
        candidateFeedback.getFeedbacks().get(0).setQuestionAnswer("not a number");
        List<CandidateFeedback> candidateFeedbacks = List.of(candidateFeedback);
        when(candidateFeedbackRepository.getAllFeedbacks(any(Key.class))).thenReturn(candidateFeedbacks);

        assertThrows(ProcessFailedException.class, () -> candidateFeedbackService.getAllFeedbacks(
                feedbackSurveyDTO.getOrganizationId(),
                feedbackSurveyDTO.getAssessmentId()
        ));
    }

    @Test
    void saveFeedback_validDate(){
        FeedbackSurveyDTO feedbackSurveyDTO = createFeedbackSurveyDTO();
        candidateFeedbackService.saveFeedback(feedbackSurveyDTO);
        verify(candidateFeedbackRepository).saveCandidateFeedback(argThat(feedback -> {
            try {
                ZonedDateTime.parse(feedback.getDate());
                return true;
            } catch (DateTimeParseException e) {
                return false;
            }
        }));
    }

    private FeedbackSurveyDTO createFeedbackSurveyDTO() {
        FeedbackSurveyDTO dto = new FeedbackSurveyDTO();
        dto.setOrganizationId("org123");
        dto.setAssessmentId("assess456");
        dto.setTestTakerId("taker789");
        dto.setTakerEmail("<EMAIL>");

        FeedbackSurveyQuestionDTO question1 = new FeedbackSurveyQuestionDTO("Overall Rating", "5", "Rating");
        FeedbackSurveyQuestionDTO question2 = new FeedbackSurveyQuestionDTO("Test Difficulty", "4", "Rating");
        FeedbackSurveyQuestionDTO question3 = new FeedbackSurveyQuestionDTO("Comments", "Good test", "Text");

        dto.setSurveyQuestions(List.of(question1, question2, question3));
        return dto;
    }

    private CandidateFeedback createCandidateFeedback() {
        CandidateFeedback feedback = new CandidateFeedback();
        feedback.setOrganizationId("org123");
        feedback.setAssessmentId("assess456");
        feedback.setCandidateId("taker789");
        feedback.setCandidateEmail("<EMAIL>");
        feedback.setFeedbacks(createFeedbackList());
        feedback.setDate(ZonedDateTime.now().toString());
        return feedback;
    }

    private List<Feedback> createFeedbackList() {
        Feedback feedback1 = new Feedback("Overall Rating", "5", "Rating");
        Feedback feedback2 = new Feedback("Test Difficulty", "4", "Rating");
        Feedback feedback3 = new Feedback("Comments", "Good test", "Text");
        return List.of(feedback1, feedback2, feedback3);
    }

    private CandidateFeedbackDTO createCandidateFeedbackDTO() {
        CandidateFeedbackDTO dto = new CandidateFeedbackDTO();
        dto.setFeedbacks(createFeedbackList());
        return dto;
    }

    private FeedbackResponseDTO createFeedbackResponseDTO() {
        FeedbackResponseDTO dto = new FeedbackResponseDTO();
        dto.setCandidateEmail("<EMAIL>");
        dto.setOverallRating(5.0);
        dto.setTestRating(4.0);
        dto.setComment("Good test");
        dto.setDate(ZonedDateTime.now().toString());
        return dto;
    }
}

