package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.FlaggedInputDTO;
import com.amap.amapreportmanagementservice.entity.AssessmentTest;
import com.amap.amapreportmanagementservice.entity.CandidateQuestionResult;
import com.amap.amapreportmanagementservice.entity.TestQuestion;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.AssessmentTestRepository;
import com.amap.amapreportmanagementservice.repository.CandidateQuestionRepository;
import com.amap.amapreportmanagementservice.repository.TestQuestionRepository;
import com.amap.amapreportmanagementservice.service.test_data.dtos.invalid_data.CandidateQuestionServiceImplTestInvalidDTOs;
import com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.CandidateQuestionServiceImplTestDTOs;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CandidateQuestionServiceImplFailureTest extends BaseTestClass {
    @InjectMocks
    private CandidateQuestionServiceImpl candidateQuestionService;

    @Mock
    private CandidateQuestionRepository candidateQuestionRepository;

    @Mock
    private AssessmentTestRepository assessmentTestRepository;

    @Mock
    private TestQuestionRepository testQuestionRepository;

    @Test
    void testSaveCandidateQuestionResult_Fail() {
        // Create a sample AssessmentProgressDTO
        AssessmentProgressDTO progressDTO = CandidateQuestionServiceImplTestInvalidDTOs.getAssessmentProgressDTO();

        // Mock repository methods
        doNothing().when(candidateQuestionRepository).saveCandidateQuestion(any(CandidateQuestionResult.class));

        // Call the service method
        try {

            candidateQuestionService.saveCandidateQuestionResult(progressDTO);
        }catch (Exception exception){
            assertTrue(exception.getMessage().contains("Cannot invoke \"com.amap.amapreportmanagementservice.dto.progress.CandidateAssessmentDTO.getTests()\" because the return value of \"com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO.getAssessment()\" is null"));
            assertTrue(exception instanceof ProcessFailedException);
            return;
        }

        // Expect the saveCandidateQuestion method to return false (which would indicate a failure)
        fail("ProcessFailedException was not thrown");
    }

    @Test
    void testUpdateCandidateQuestionResult_Fail() {
        // Create a sample AssessmentInputDTO
        AssessmentInputDTO inputDTO = CandidateQuestionServiceImplTestInvalidDTOs.getAssessmentInputDTO();

        // Mock repository methods
        when(candidateQuestionRepository.getCandidateQuestions(any())).thenReturn(null); // Simulate a failure to retrieve questions

        // Call the service method
        try {
            candidateQuestionService.updateCandidateQuestionResult(inputDTO);
        }catch (Exception exception){
            assertTrue(exception.getMessage().contains("Cannot invoke \"com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO.getTestId()\" because \"testResultInputDTO\" is null"));
            assertTrue(exception instanceof ProcessFailedException);
            return;
        }

        // Expect the updateCandidateQuestion method to be called but fail due to missing questions
        fail("ProcessFailedException was not thrown");
    }

    @Test
    void testUpdateFlaggedQuestion_Fail() {
        // Create a sample FlaggedInputDTO
        FlaggedInputDTO flaggedInputDTO = CandidateQuestionServiceImplTestDTOs.getFlaggedInputDTO();

        // Mock repository methods
        when(testQuestionRepository.getTestQuestion(any())).thenReturn(null); // Simulate a failure to retrieve the test question
        when(assessmentTestRepository.getAssessmentTest(any())).thenReturn(null); // Simulate a failure to retrieve the assessment test
        when(candidateQuestionRepository.getCandidateQuestion(any())).thenReturn(null); // Simulate a failure to retrieve the candidate question

        // Call the service method
        try {
            candidateQuestionService.updateFlaggedQuestion(flaggedInputDTO);
        }catch (Exception exception){
            assertTrue(exception.getMessage().contains("Cannot invoke \"com.amap.amapreportmanagementservice.entity.TestQuestion.getNumberOfFlags()\" because \"testQuestion1\" is null"));
            assertTrue(exception instanceof ProcessFailedException);
            return;
        }

        // Expect the update methods to be called but fail due to missing entities
        fail("ProcessFailedException was not thrown");
    }

    @Test
    void saveCandidateQuestionResult_WhenAssessmentIsNull_ShouldThrowProcessFailedException() {
        AssessmentProgressDTO progressDTO = CandidateQuestionServiceImplTestInvalidDTOs.getAssessmentProgressDTO();
        ProcessFailedException exception = assertThrows(ProcessFailedException.class, () -> candidateQuestionService.saveCandidateQuestionResult(progressDTO));
        assertTrue(exception.getMessage().contains("Cannot invoke \"com.amap.amapreportmanagementservice.dto.progress.CandidateAssessmentDTO.getTests()\" because the return value of \"com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO.getAssessment()\" is null"));
    }

    @Test
    void updateCandidateQuestionResult_WhenTestResultIsNull_ShouldThrowProcessFailedException() {
        AssessmentInputDTO inputDTO = CandidateQuestionServiceImplTestInvalidDTOs.getAssessmentInputDTO();
        ProcessFailedException exception = assertThrows(ProcessFailedException.class, () -> candidateQuestionService.updateCandidateQuestionResult(inputDTO));
        assertTrue(exception.getMessage().contains("Cannot invoke \"com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO.getTestId()\" because \"testResultInputDTO\" is null"));
    }

    @Test
    void updateFlaggedQuestion_WhenTestQuestionNotFound_ShouldThrowProcessFailedException() {
        FlaggedInputDTO flaggedInputDTO = CandidateQuestionServiceImplTestDTOs.getFlaggedInputDTO();
        when(testQuestionRepository.getTestQuestion(any())).thenReturn(null);
        ProcessFailedException exception = assertThrows(ProcessFailedException.class, () -> candidateQuestionService.updateFlaggedQuestion(flaggedInputDTO));
        assertTrue(exception.getMessage().contains("Cannot invoke \"com.amap.amapreportmanagementservice.entity.TestQuestion.getNumberOfFlags()\" because \"testQuestion1\" is null"));
    }

    @Test
    void updateFlaggedQuestion_WhenAssessmentTestNotFound_ShouldThrowProcessFailedException() {
        FlaggedInputDTO flaggedInputDTO = CandidateQuestionServiceImplTestDTOs.getFlaggedInputDTO();
        when(testQuestionRepository.getTestQuestion(any())).thenReturn(new TestQuestion());
        when(assessmentTestRepository.getAssessmentTest(any())).thenReturn(null);
        ProcessFailedException exception = assertThrows(ProcessFailedException.class, () -> candidateQuestionService.updateFlaggedQuestion(flaggedInputDTO));
        assertTrue(exception.getMessage().contains("Cannot invoke \"com.amap.amapreportmanagementservice.entity.AssessmentTest.getNumberOfFlags()\" because \"assessmentTest1\" is null"));
    }

    @Test
    void updateFlaggedQuestion_WhenCandidateQuestionNotFound_ShouldThrowProcessFailedException() {
        FlaggedInputDTO flaggedInputDTO = CandidateQuestionServiceImplTestDTOs.getFlaggedInputDTO();
        when(testQuestionRepository.getTestQuestion(any())).thenReturn(new TestQuestion());
        when(assessmentTestRepository.getAssessmentTest(any())).thenReturn(new AssessmentTest());
        when(candidateQuestionRepository.getCandidateQuestion(any())).thenReturn(null);
        ProcessFailedException exception = assertThrows(ProcessFailedException.class, () -> candidateQuestionService.updateFlaggedQuestion(flaggedInputDTO));
        assertTrue(exception.getMessage().contains("Cannot invoke \"com.amap.amapreportmanagementservice.entity.CandidateQuestionResult.setIsFlagged(String)\" because \"candidateQuestionResult1\" is null")); // Updated assertion
    }

}
