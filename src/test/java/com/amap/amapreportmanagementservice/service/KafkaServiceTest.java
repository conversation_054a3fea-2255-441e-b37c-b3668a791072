package com.amap.amapreportmanagementservice.service;

import  com.amap.amapreportmanagementservice.dto.feedback.FeedbackSurveyDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentTakerDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.FlaggedInputDTO;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.cache.CacheManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.amap.amapreportmanagementservice.TestConstants.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

class KafkaServiceTest extends  BaseTestClass{
    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private CandidateAssessmentService candidateAssessmentService;

    @Mock
    private CandidateQuestionService candidateQuestionService;

    @Mock
    private QuestionFlaggingService questionFlaggingService;

    @Mock
    private CandidateFeedbackService candidateFeedbackService;

    @Mock
    private CacheManager cacheManager;

    @InjectMocks
    private KafkaService kafkaService;

    @Test
    void consumeAssessmentResults_Success() throws Exception {
        // Arrange
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("{\"key\": \"value\"}");

        HashMap<String, Object> assessmentObject = new HashMap<>();
        assessmentObject.put("key", "value");
        AssessmentInputDTO assessmentDTO = new AssessmentInputDTO();
        assessmentDTO.setOrganizationId(ORGANIZATIONID);
        assessmentDTO.setTestTakerId(CANDIDATEID);

        when(objectMapper.readValue(eq(mockMessage.value()), any(TypeReference.class))).thenReturn(assessmentObject);
        when(objectMapper.convertValue(eq(assessmentObject), eq(AssessmentInputDTO.class))).thenReturn(assessmentDTO);

        doNothing().when(candidateAssessmentService).updateCandidateAssessment(assessmentDTO);
        // Act
        kafkaService.consumeAssessmentResults(mockMessage);

        // Assert
        verify(candidateAssessmentService, times(1)).updateCandidateAssessment(assessmentDTO);
    }
    @Test
    void consumeAssessmentResults_ExceptionHandled() throws Exception {
        // Arrange
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("{\"key\": \"value\"}");
        when(objectMapper.readValue(anyString(), any(TypeReference.class))).thenThrow(new RuntimeException("Test Exception"));

        // Act
        kafkaService.consumeAssessmentResults(mockMessage);

        // Assert
        verify(candidateAssessmentService, never()).updateCandidateAssessment(any());
    }

    @Test
    void consumeAssessmentProgress_Success() throws Exception {
        // Arrange
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("{\"key\": \"value\"}");

        HashMap<String, Object> progressObject = new HashMap<>();
        progressObject.put("key", "value");

        AssessmentProgressDTO assessmentProgressDTO= new AssessmentProgressDTO();
        assessmentProgressDTO.setId(CANDIDATEID);
        assessmentProgressDTO.setAssessmentId(ASSESSMENTID);
        assessmentProgressDTO.setOrganizationId(ORGANIZATIONID);

        AssessmentTakerDTO assessmentTakerDTO = new AssessmentTakerDTO();
        assessmentTakerDTO.setAssessmentTaker(assessmentProgressDTO);

        when(objectMapper.readValue(eq(mockMessage.value()), any(TypeReference.class))).thenReturn(progressObject);
        when(objectMapper.convertValue(progressObject, AssessmentTakerDTO.class)).thenReturn(assessmentTakerDTO);

        // Act
        kafkaService.consumeAssessmentProgress(mockMessage);

        // Assert
        verify(candidateAssessmentService, times(1)).saveCandidateAssessment(assessmentTakerDTO.getAssessmentTaker());
    }

    @Test
    void processFlaggedQuestion_Success() throws Exception {
        // Arrange
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("{\"key\": \"value\"}");

        HashMap<String, Object> flagObject = new HashMap<>();
        FlaggedInputDTO flaggedInputDTO = new FlaggedInputDTO();

        List<String> reasonForFlagging= new ArrayList<>();
        flaggedInputDTO.setReasonForFlagging(reasonForFlagging);

        when(objectMapper.readValue(eq(mockMessage.value()), any(TypeReference.class))).thenReturn(flagObject);
        when(objectMapper.convertValue(flagObject, FlaggedInputDTO.class)).thenReturn(flaggedInputDTO);

        doNothing().when(candidateQuestionService).updateFlaggedQuestion(flaggedInputDTO);
        doNothing().when(questionFlaggingService).saveQuestionFlagging(flaggedInputDTO);
        // Act
        kafkaService.processFlaggedQuestion(mockMessage);

        // Assert
        verify(candidateQuestionService, times(1)).updateFlaggedQuestion(flaggedInputDTO);
        verify(questionFlaggingService, times(1)).saveQuestionFlagging(flaggedInputDTO);
    }

    @Test
    void processFlaggedQuestion_ReasonForFlaggingNull_Success() throws Exception {
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("{\"key\": \"value\"}");

        HashMap<String, Object> flagObject = new HashMap<>();
        FlaggedInputDTO flaggedInputDTO = new FlaggedInputDTO();
        flaggedInputDTO.setReasonForFlagging(null);

        when(objectMapper.readValue(eq(mockMessage.value()), any(TypeReference.class))).thenReturn(flagObject);
        when(objectMapper.convertValue(flagObject, FlaggedInputDTO.class)).thenReturn(flaggedInputDTO);

        kafkaService.processFlaggedQuestion(mockMessage);

        verify(candidateQuestionService).updateFlaggedQuestion(flaggedInputDTO);
        verify(questionFlaggingService, never()).saveQuestionFlagging(flaggedInputDTO);
    }


    @Test
    void consumeSurveyFeedback_Success() throws Exception {
        // Arrange
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("{\"key\": \"value\"}");

        HashMap<String, Object> feedbackObject = new HashMap<>();
        FeedbackSurveyDTO feedbackSurveyDTO = new FeedbackSurveyDTO();
        feedbackSurveyDTO.setTestTakerId(CANDIDATEID);
        feedbackSurveyDTO.setTakerEmail(TESTEMAIL);
        feedbackSurveyDTO.setOrganizationId(ORGANIZATIONID);
        feedbackSurveyDTO.setAssessmentId(ASSESSMENTID);
        when(objectMapper.readValue(eq(mockMessage.value()), any(TypeReference.class))).thenReturn(feedbackObject);
        when(objectMapper.convertValue(feedbackObject, FeedbackSurveyDTO.class)).thenReturn(feedbackSurveyDTO);

        doNothing().when(candidateFeedbackService).saveFeedback(feedbackSurveyDTO);

        // Act
        kafkaService.consumeSurveyFeedback(mockMessage);

        // Assert
        verify(candidateFeedbackService, times(1)).saveFeedback(feedbackSurveyDTO);
    }


    @Test
    void consumeAssessmentProgress_NullMessageValue_ExceptionHandled() {
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn(null);

        assertThrows(ProcessFailedException.class, () -> kafkaService.consumeAssessmentProgress(mockMessage));
        verify(candidateAssessmentService, never()).saveCandidateAssessment(any());
    }

    @Test
    void consumeAssessmentResults_EmptyMessageValue_ExceptionHandled() throws Exception {
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("");

        when(objectMapper.readValue(eq(""), any(TypeReference.class))).thenThrow(new RuntimeException("Test Exception"));

        kafkaService.consumeAssessmentResults(mockMessage);

        // Verify that no service methods were called
        verify(candidateAssessmentService, never()).updateCandidateAssessment(any());
    }

    @Test
    void consumeAssessmentProgress_EmptyMessageValue_ExceptionHandled() throws Exception {
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("");
        when(objectMapper.readValue(eq(""), any(TypeReference.class))).thenThrow(new RuntimeException("Test Exception"));

        assertThrows(ProcessFailedException.class, () -> kafkaService.consumeAssessmentProgress(mockMessage));
        verify(candidateAssessmentService, never()).saveCandidateAssessment(any());
    }

    @Test
    void processFlaggedQuestion_EmptyMessageValue_ExceptionHandled() throws Exception {
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("");
        when(objectMapper.readValue(eq(""), any(TypeReference.class))).thenThrow(new RuntimeException("Test Exception"));

        assertThrows(ProcessFailedException.class, () -> kafkaService.processFlaggedQuestion(mockMessage));
        verify(candidateQuestionService, never()).updateFlaggedQuestion(any());
        verify(questionFlaggingService, never()).saveQuestionFlagging(any());
    }

    @Test
    void consumeSurveyFeedback_EmptyMessageValue_ExceptionHandled() throws Exception {
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("");
        when(objectMapper.readValue(eq(""), any(TypeReference.class))).thenThrow(new RuntimeException("Test Exception"));

        assertThrows(ProcessFailedException.class, () -> kafkaService.consumeSurveyFeedback(mockMessage));
        verify(candidateFeedbackService, never()).saveFeedback(any());
    }

    @Test
    void consumeAssessmentResults_InvalidJson_ExceptionHandled() throws Exception {
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("invalid json");

        when(objectMapper.readValue(eq("invalid json"), any(TypeReference.class))).thenThrow(new RuntimeException("Test Exception"));

        kafkaService.consumeAssessmentResults(mockMessage);

        // Verify that no service methods were called
        verify(candidateAssessmentService, never()).updateCandidateAssessment(any());
    }

    @Test
    void consumeAssessmentProgress_InvalidJson_ExceptionHandled() throws Exception {
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("invalid json");
        when(objectMapper.readValue(eq("invalid json"), any(TypeReference.class))).thenThrow(new RuntimeException("Test Exception"));

        assertThrows(ProcessFailedException.class, () -> kafkaService.consumeAssessmentProgress(mockMessage));
        verify(candidateAssessmentService, never()).saveCandidateAssessment(any());
    }

    @Test
    void processFlaggedQuestion_InvalidJson_ExceptionHandled() throws Exception  {
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("invalid json");
        when(objectMapper.readValue(eq("invalid json"), any(TypeReference.class))).thenThrow(new RuntimeException("Test Exception"));

        assertThrows(ProcessFailedException.class, () -> kafkaService.processFlaggedQuestion(mockMessage));
        verify(candidateQuestionService, never()).updateFlaggedQuestion(any());
        verify(questionFlaggingService, never()).saveQuestionFlagging(any());
    }

    @Test
    void consumeSurveyFeedback_InvalidJson_ExceptionHandled() throws Exception  {
        ConsumerRecord<String, String> mockMessage = mock(ConsumerRecord.class);
        when(mockMessage.value()).thenReturn("invalid json");
        when(objectMapper.readValue(eq("invalid json"), any(TypeReference.class))).thenThrow(new RuntimeException("Test Exception"));

        assertThrows(ProcessFailedException.class, () -> kafkaService.consumeSurveyFeedback(mockMessage));
        verify(candidateFeedbackService, never()).saveFeedback(any());
    }

}
