package com.amap.amapreportmanagementservice.service.test_data.models;

import com.amap.amapreportmanagementservice.entity.Assessment;

import java.util.ArrayList;
import java.util.List;

import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.assessmentId;
import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.testId;

public class TestQuestionServiceImplTestModels {
    public static Assessment getAssessment(){
        List<String> testIds = new ArrayList<>();
        testIds.add(testId);
        return Assessment.builder()
                .assessmentId(assessmentId)
                .assessmentDuration(100)
                .title("Hello")
                .averageTimeTaken(14)
                .averagePercentage(35.4)
                .numberOfTakers(22)
                .numberOfUniqueTakers(12)
                .totalScore(145)
                .testsIds(testIds)
                .progressCount(2)
                .completedCount(1)
                .build();
    }
}
