package com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.FlaggedInputDTO;

import java.util.List;
import java.util.UUID;

import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.*;
import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedDTOs.getCandidateAssessmentDTO;

public class CandidateQuestionServiceImplTestDTOs {
    public static AssessmentProgressDTO getAssessmentProgressDTO() {
        return AssessmentProgressDTO.builder()
                .assessment(getCandidateAssessmentDTO())
                .assessmentId(assessmentId)
                .id(UUID.randomUUID().toString())
                .organizationId(organizationId)
                .email("<EMAIL>")
                .status("in progress")
                .proctor("Level 1")
                .build();
    }
    public static FlaggedInputDTO getFlaggedInputDTO(){
        return FlaggedInputDTO.builder()
                .organizationId(organizationId)
                .id(genericId)
                .assessmentId(assessmentId)
                .testId(testId)
                .questionId(questionId)
                .questionText("Cruises response prototype shadows allowed lonely featured, egg formerly order bride parliamentary mile telling, terrorism. ")
                .testTakerId(testTakerId)
                .testTakerEmail("<EMAIL>")
                .reasonForFlagging(List.of("Maintenance surprise identified xanax. "))
                .build();
    }

}
