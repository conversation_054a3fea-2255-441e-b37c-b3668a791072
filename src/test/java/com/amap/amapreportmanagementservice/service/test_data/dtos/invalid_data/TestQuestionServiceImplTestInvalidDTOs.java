package com.amap.amapreportmanagementservice.service.test_data.dtos.invalid_data;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.entity.Assessment;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.assessmentId;
import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.testId;


public class TestQuestionServiceImplTestInvalidDTOs {
    public static AssessmentProgressDTO getAssessmentProgressDTO() {
        return AssessmentProgressDTO.builder()
                .assessment(null)
                .assessmentId(UUID.randomUUID().toString())
                .id(UUID.randomUUID().toString())
                .organizationId(UUID.randomUUID().toString())
                .email(null)
                .status(null)
                .proctor(null)
                .build();
    }
    public static AssessmentInputDTO getAssessmentInputDTO() {
        List<TestResultInputDTO> testResultInputDTOS = new ArrayList<>();
        List<String> windowViolation = new ArrayList<>();
        windowViolation.add("opened extra tab");
        List<String> intervalScreenshots = new ArrayList<>();
        intervalScreenshots.add("screenshot 1");
        testResultInputDTOS.add(null);
        return AssessmentInputDTO.builder()
                .organizationId(UUID.randomUUID().toString())
                .testTakerId(UUID.randomUUID().toString())
                .email("<EMAIL>")
                .assessmentId(UUID.randomUUID().toString())
                .startTime(LocalDateTime.now().toString())
                .assessmentEndTime(LocalDateTime.now().toString())
                .testCount(1)
                .totalFiveToStart("tots")
                .testResults(testResultInputDTOS)
                .windowViolation(windowViolation)
                .intervalScreenshots(intervalScreenshots)
                .identity(null)
                .status("in progress")
                .build();
    }

    public static Assessment getAssessment(){
        List<String> testIds = new ArrayList<>();
        testIds.add(testId);
        return Assessment.builder()
                .assessmentId(assessmentId)
                .assessmentDuration(100)
                .title("Hello")
                .averageTimeTaken(14)
                .averagePercentage(35.4)
                .numberOfTakers(22)
                .numberOfUniqueTakers(12)
                .totalScore(145)
                .testsIds(testIds)
                .progressCount(2)
                .completedCount(1)
                .build();
    }
}
