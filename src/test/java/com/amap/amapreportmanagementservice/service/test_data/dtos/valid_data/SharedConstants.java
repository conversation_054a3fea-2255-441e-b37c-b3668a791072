package com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data;

import java.util.UUID;

public class SharedConstants {
    public static final String genericId = UUID.randomUUID().toString();
    public static final String organizationId = UUID.randomUUID().toString();
    public static final String candidateId = UUID.randomUUID().toString();
    public static final String assessmentId = UUID.randomUUID().toString();
    public static final String testTakerId = UUID.randomUUID().toString();
    public static final String testId = UUID.randomUUID().toString();
    public static final String questionId = UUID.randomUUID().toString();
    public static final String domainId = UUID.randomUUID().toString();
    public static final String categoryId = UUID.randomUUID().toString();
}
