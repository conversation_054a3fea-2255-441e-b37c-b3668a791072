package com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateQuestionDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateTestDTO;
import com.amap.amapreportmanagementservice.entity.TestQuestion;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.*;
import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedDTOs.getCandidateAssessmentDTO;
import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedDTOs.getCandidateQuestionDTO;

@Data
public class TestQuestionServiceImplTestDTOs {

    public static CandidateTestDTO getCandidateTestDTO() {
        List<CandidateQuestionDTO> candidateQuestionDTOS = new ArrayList<>();
        candidateQuestionDTOS.add(getCandidateQuestionDTO());
        return CandidateTestDTO.builder()
                .id(UUID.randomUUID().toString())
                .title("Physics")
                .isActivated(true)
                .difficultyLevel("easy")
                .domainId(UUID.randomUUID().toString())
                .duration(124)
                .questions(candidateQuestionDTOS)
                .build();
    }
    public static AssessmentProgressDTO getAssessmentProgressDTO() {
        return AssessmentProgressDTO.builder()
                .assessment(getCandidateAssessmentDTO())
                .assessmentId(assessmentId)
                .id(UUID.randomUUID().toString())
                .organizationId(organizationId)
                .email("<EMAIL>")
                .status("in progress")
                .proctor("Level 1")
                .build();
    }

    public static TestQuestion getTestQuestion(){
        List<String> questionIds = new ArrayList<>();
        questionIds.add(UUID.randomUUID().toString());
        questionIds.add(UUID.randomUUID().toString());
        questionIds.add(UUID.randomUUID().toString());
        questionIds.add(UUID.randomUUID().toString());
        questionIds.add(UUID.randomUUID().toString());
        return TestQuestion.builder()
                .assessmentId(assessmentId)
                .testId(testId)
                .questionId(questionId)
                .questionText("What is Spring Boot?")
                .numberOfTimesMissed(2)
                .numberOfTimesAnswered(1)
                .title("Physics")
                .duration(3)
                .difficultyLevel("easy")
                .domainId(UUID.randomUUID().toString())
                .categoryId(UUID.randomUUID().toString())
                .numberOfFlags(2)
                .questionIds(questionIds)
                .totalScore(5)
                .build();

    }
}
