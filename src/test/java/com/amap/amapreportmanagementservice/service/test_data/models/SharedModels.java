package com.amap.amapreportmanagementservice.service.test_data.models;

import com.amap.amapreportmanagementservice.entity.AssessmentTest;

import java.util.ArrayList;
import java.util.List;

import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.*;

public class SharedModels {

    public static AssessmentTest getAssessmentTest(){
        List<String> questionIds = new ArrayList<>();
        questionIds.add(questionId);
        return AssessmentTest.builder()
                .assessmentId(assessmentId)
                .title("Hello")
                .testId(testId)
                .totalScore(3)
                .averageScore(2)
                .duration(14)
                .totalCandidateMarks(2)
                .numberOfTimesAnswered(1)
                .numberOfFlags(1)
                .questionIds(questionIds)
                .build();
    }
}
