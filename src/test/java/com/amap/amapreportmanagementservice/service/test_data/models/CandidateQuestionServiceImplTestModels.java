package com.amap.amapreportmanagementservice.service.test_data.models;

import com.amap.amapreportmanagementservice.entity.CandidateQuestionResult;
import com.amap.amapreportmanagementservice.entity.TestQuestion;

import java.util.ArrayList;
import java.util.List;

import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.*;

public class CandidateQuestionServiceImplTestModels {
    public static CandidateQuestionResult getCandidateQuestionResult(){
        return CandidateQuestionResult.builder()
                .candidateId(candidateId)
                .candidateEmail("<EMAIL>")
                .totalScore(29)
                .candidateMarks(20)
                .timeLimit(60)
                .isFlagged("true")
                .build();
    }
    public static List<CandidateQuestionResult> getCandidateQuestionResultList(){
        List<CandidateQuestionResult> candidateQuestionResults = new ArrayList<>();
        candidateQuestionResults.add(getCandidateQuestionResult());
        return candidateQuestionResults;
    }
    public static TestQuestion getTestQuestion(){
        List<String> questionIds = new ArrayList<>();
        questionIds.add(questionId);
        return TestQuestion.builder()
                .assessmentId(assessmentId)
                .testId(testId)
                .questionId(questionId)
                .questionText("Mental dj dance interested juvenile tahoe operations, remedies sold specialties united caps specially shops, attending meal potter. ")
                .numberOfTimesMissed(2)
                .numberOfTimesAnswered(10)
                .title("thing")
                .duration(30)
                .difficultyLevel("easy")
                .domainId(domainId)
                .categoryId(categoryId)
                .numberOfFlags(2)
                .questionIds(questionIds)
                .totalScore(12.98)
                .build();
    }
}
