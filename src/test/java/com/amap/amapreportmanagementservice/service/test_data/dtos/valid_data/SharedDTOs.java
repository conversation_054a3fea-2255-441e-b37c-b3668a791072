package com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data;

import com.amap.amapreportmanagementservice.dto.progress.*;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.IdentityInputDTO;
import com.amap.amapreportmanagementservice.dto.results.QuestionResultInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.*;

public class SharedDTOs {
    public static CandidateAssessmentDTO getCandidateAssessmentDTO() {
        List<CandidateTestDTO> testDTOS = new ArrayList<>();
        testDTOS.add(getCandidateTestDTO());
        return CandidateAssessmentDTO.builder()
                .id(UUID.randomUUID().toString())
                .title("Blah Blah Blah")
                .instructions("Start now")
                .system("true")
                .proctor("Level 1")
                .assessmentDuration(3)
                .assessmentStartTime(LocalDateTime.now().toString())
                .tests(testDTOS)
                .build();
    }
    public static CandidateTestDTO getCandidateTestDTO() {
        List<CandidateQuestionDTO> candidateQuestionDTOS = new ArrayList<>();
        candidateQuestionDTOS.add(getCandidateQuestionDTO());
        return CandidateTestDTO.builder()
                .id(UUID.randomUUID().toString())
                .title("Physics")
                .isActivated(true)
                .difficultyLevel("easy")
                .domainId(UUID.randomUUID().toString())
                .duration(124)
                .questions(candidateQuestionDTOS)
                .build();
    }
    public static CandidateQuestionDTO getCandidateQuestionDTO() {
        return CandidateQuestionDTO.builder()
                .id(questionId)
                .questionText("What is Black Body Radiation?")
                .questionType("Multiple Answers")
                .score(1)
                .timeLimit(12)
                .difficultyLevel("easy")
                .domainId(UUID.randomUUID().toString())
                .categoryId(UUID.randomUUID().toString())
                .multipleSelectAnswer(getMultipleAnswerDTO())
                .build();
    }
    public static MultipleAnswerDTO getMultipleAnswerDTO() {
        List<String> options = new ArrayList<>();
        options.add("A");
        options.add("B");
        options.add("C");
        options.add("D");

        List<String> answer = new ArrayList<>();
        answer.add("A");

        return MultipleAnswerDTO.builder()
                .id(UUID.randomUUID().toString())
                .questionId(questionId)
                .options(options)
                .answer(answer)
                .build();
    }
    public static TestResultInputDTO getTestResultInputDTO() {
        List<QuestionResultInputDTO> questionResultInputDTOS = new ArrayList<>();
        questionResultInputDTOS.add(getQuestionResultInputDTO());
        return TestResultInputDTO.builder()
                .testId(testId)
                .questionResults(questionResultInputDTOS)
                .numberOfQuestions(3)
                .numberOfQuestionsFailed(2)
                .numberOfQuestionsPassed(1)
                .totalScore(3)
                .totalPassedScore(1)
                .build();
    }
    public static QuestionResultInputDTO getQuestionResultInputDTO() {

        TestTakerCodeAnswerDTO testTakerCodeAnswerDTO = new TestTakerCodeAnswerDTO();
        return QuestionResultInputDTO.builder()
                .questionId(questionId)
                .questionText("What is SpringBoot")
                .testTakerCode(testTakerCodeAnswerDTO)
                .idleTime(2)
                .scored(0)
                .score(1)
                .isAnswerCorrect("true")
                .build();
    }
    public static IdentityInputDTO getIdentityInputDTO() {
        return IdentityInputDTO.builder()
                .linkId(UUID.randomUUID().toString())
                .linkHead("Headie-One")
                .build();
    }
    public static AssessmentInputDTO getAssessmentInputDTO() {
        List<TestResultInputDTO> testResultInputDTOS = new ArrayList<>();
        List<String> windowViolation = new ArrayList<>();
        windowViolation.add("opened extra tab");
        List<String> intervalScreenshots = new ArrayList<>();
        intervalScreenshots.add("screenshot 1");
        testResultInputDTOS.add(getTestResultInputDTO());
        return AssessmentInputDTO.builder()
                .organizationId(organizationId)
                .testTakerId(testTakerId)
                .email("<EMAIL>")
                .assessmentId(assessmentId)
                .startTime(LocalDateTime.now().toString())
                .assessmentEndTime(LocalDateTime.now().toString())
                .testCount(1)
                .totalFiveToStart("tots")
                .testResults(testResultInputDTOS)
                .windowViolation(windowViolation)
                .intervalScreenshots(intervalScreenshots)
                .identity(getIdentityInputDTO())
                .status("in progress")
                .build();
    }
}
