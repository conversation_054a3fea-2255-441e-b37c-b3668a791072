package com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;

import java.util.UUID;

import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.assessmentId;
import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.organizationId;
import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedDTOs.getCandidateAssessmentDTO;

public class CandidateTestServiceImplTestDTOs {
    public static AssessmentProgressDTO getAssessmentProgressDTO() {
        return AssessmentProgressDTO.builder()
                .assessment(getCandidateAssessmentDTO())
                .assessmentId(assessmentId)
                .id(UUID.randomUUID().toString())
                .organizationId(organizationId)
                .email("<EMAIL>")
                .status("in progress")
                .proctor("Level 1")
                .build();
    }
}
