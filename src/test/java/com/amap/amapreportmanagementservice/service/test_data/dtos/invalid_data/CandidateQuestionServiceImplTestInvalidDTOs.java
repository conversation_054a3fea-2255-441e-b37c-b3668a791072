package com.amap.amapreportmanagementservice.service.test_data.dtos.invalid_data;

import com.amap.amapreportmanagementservice.dto.progress.*;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.FlaggedInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.*;

public class CandidateQuestionServiceImplTestInvalidDTOs {

    public static AssessmentProgressDTO getAssessmentProgressDTO() {
        return AssessmentProgressDTO.builder()
                .assessment(null)
                .assessmentId(assessmentId)
                .id(UUID.randomUUID().toString())
                .organizationId(organizationId)
                .email(null)
                .status(null)
                .proctor(null)
                .build();
    }
    public static AssessmentInputDTO getAssessmentInputDTO() {
        List<TestResultInputDTO> testResultInputDTOS = new ArrayList<>();
        List<String> windowViolation = new ArrayList<>();
        windowViolation.add("opened extra tab");
        List<String> intervalScreenshots = new ArrayList<>();
        intervalScreenshots.add("screenshot 1");
        testResultInputDTOS.add(null);
        return AssessmentInputDTO.builder()
                .organizationId(organizationId)
                .testTakerId(testTakerId)
                .email(null)
                .assessmentId(assessmentId)
                .startTime(LocalDateTime.now().toString())
                .assessmentEndTime(LocalDateTime.now().toString())
                .testCount(1)
                .totalFiveToStart("tots")
                .testResults(testResultInputDTOS)
                .windowViolation(windowViolation)
                .intervalScreenshots(intervalScreenshots)
                .identity(null)
                .status("in progress")
                .build();
    }

    public static FlaggedInputDTO getFlaggedInputDTO(){
        return FlaggedInputDTO.builder()
                .organizationId(organizationId)
                .id(UUID.randomUUID().toString())
                .assessmentId(assessmentId)
                .testId(testId)
                .questionId(questionId)
                .questionText(null)
                .testTakerId(testTakerId)
                .testTakerEmail(null)
                .reasonForFlagging(null)
                .build();
    }
}
