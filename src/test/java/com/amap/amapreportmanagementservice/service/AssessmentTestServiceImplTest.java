package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.TestInfoDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateAssessmentDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateQuestionDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateTestDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.entity.Assessment;
import com.amap.amapreportmanagementservice.entity.AssessmentTest;
import com.amap.amapreportmanagementservice.entity.CandidateTest;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.AssessmentRepository;
import com.amap.amapreportmanagementservice.repository.AssessmentTestRepository;
import com.amap.amapreportmanagementservice.repository.CandidateTestRepository;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


class AssessmentTestServiceImplTest extends BaseTestClass {
    @Mock
    private AssessmentTestRepository assessmentTestRepository;

    @Mock
    private AssessmentRepository assessmentRepository;

    @Mock
    private CandidateTestRepository candidateTestRepository;

    @InjectMocks
    private AssessmentTestServiceImpl assessmentTestService;

    @Test
    void saveAssessmentTest(){
        String pk = KeyBuilder.assessmentTestPK("1234", "1234");
        String sk = KeyBuilder.assessmentTestSK("1234", "12");

        AssessmentProgressDTO assessmentProgressDTO = new AssessmentProgressDTO();
        CandidateAssessmentDTO candidateAssessmentDTO = new CandidateAssessmentDTO();
        CandidateTestDTO candidateTestDTO = new CandidateTestDTO();
        candidateTestDTO.setId("1234");
        List<CandidateTestDTO> testDTOS = new ArrayList<>();
        testDTOS.add(candidateTestDTO);
        candidateAssessmentDTO.setAssessmentDuration(10);
        candidateAssessmentDTO.setAssessmentStartTime("");
        candidateAssessmentDTO.setProctor("Default Level");
        candidateAssessmentDTO.setSystem("true");
        candidateAssessmentDTO.setInstructions("");
        candidateAssessmentDTO.setTests(testDTOS);

        assessmentProgressDTO.setAssessmentId("1234");
        assessmentProgressDTO.setOrganizationId("1234");

        CandidateQuestionDTO candidateQuestionDTO = new CandidateQuestionDTO();
        List<CandidateQuestionDTO> questions = new ArrayList<>();
        questions.add(candidateQuestionDTO);
        candidateTestDTO.setQuestions(questions);

        when(assessmentTestRepository.checkIfExists(
                assessmentProgressDTO.getOrganizationId(),
                assessmentProgressDTO.getAssessmentId(),
                candidateTestDTO.getId())
        ).thenReturn(false);

        AssessmentTest assessmentTest = new AssessmentTest();
        assessmentProgressDTO.setAssessment(candidateAssessmentDTO);

        assessmentTest.setPk(pk);
        assessmentTest.setSk(sk);
        doNothing().when(assessmentTestRepository).saveAssessmentTest(assessmentTest);
        assessmentTestService.saveAssessmentTest(assessmentProgressDTO);
        verify(assessmentTestRepository).saveAssessmentTest(any(AssessmentTest.class));



    }

    @Test
    void saveAssessmentTest_NewTest() {
        AssessmentProgressDTO assessmentProgressDTO = createAssessmentProgressDTO();
        CandidateTestDTO candidateTestDTO = assessmentProgressDTO.getAssessment().getTests().get(0);

        when(assessmentTestRepository.checkIfExists(anyString(), anyString(), anyString())).thenReturn(false);

        assessmentTestService.saveAssessmentTest(assessmentProgressDTO);

        verify(assessmentTestRepository).saveAssessmentTest(any(AssessmentTest.class));
    }

    @Test
    void saveExistingAssessmentTest(){
        String pk = KeyBuilder.assessmentTestPK("1234", "1234");
        String sk = KeyBuilder.assessmentTestSK("1234", "12");


        AssessmentProgressDTO assessmentProgressDTO = new AssessmentProgressDTO();
        CandidateAssessmentDTO candidateAssessmentDTO = new CandidateAssessmentDTO();
        CandidateTestDTO candidateTestDTO = new CandidateTestDTO();
        candidateTestDTO.setId("1234");

        List<CandidateTestDTO> testDTOS = new ArrayList<>();
        testDTOS.add(candidateTestDTO);
        candidateAssessmentDTO.setAssessmentDuration(10);
        candidateAssessmentDTO.setAssessmentStartTime("");
        candidateAssessmentDTO.setProctor("Default Level");
        candidateAssessmentDTO.setSystem("true");
        candidateAssessmentDTO.setInstructions("");
        candidateAssessmentDTO.setTests(testDTOS);

        AssessmentTest assessmentTest = new AssessmentTest();
        assessmentTest.setQuestionIds(List.of("1234", "5678"));

        assessmentProgressDTO.setAssessment(candidateAssessmentDTO);
        assessmentProgressDTO.setAssessmentId("1234");
        assessmentProgressDTO.setOrganizationId("1234");


        CandidateQuestionDTO candidateQuestionDTO = new CandidateQuestionDTO();
        candidateQuestionDTO.setId("2344");
        List<CandidateQuestionDTO> questions = new ArrayList<>();
        questions.add(candidateQuestionDTO);
        candidateTestDTO.setQuestions(questions);
        assessmentProgressDTO.setAssessment(candidateAssessmentDTO);


        when(assessmentTestRepository.checkIfExists(
                assessmentProgressDTO.getOrganizationId(),
                assessmentProgressDTO.getAssessmentId(),
                candidateTestDTO.getId())
        ).thenReturn(true);

    when(assessmentTestRepository.getAssessmentTest(any(Key.class))).thenReturn(assessmentTest);


        // Call the method under test
        assessmentTestService.saveAssessmentTest(assessmentProgressDTO);

        // Verify that assessmentTestRepository.saveAssessmentTest was never called
        verify(assessmentTestRepository, times(1)).saveAssessmentTest(any(AssessmentTest.class));



    }

    @Test
    void updateAssessmentTest(){
        String pk = KeyBuilder.assessmentPK("1234", "1234");

        AssessmentInputDTO assessmentInputDTO = new AssessmentInputDTO();
        CandidateAssessmentDTO candidateAssessmentDTO = new CandidateAssessmentDTO();
        CandidateTestDTO candidateTestDTO = new CandidateTestDTO();
        candidateTestDTO.setId("1234");

        List<CandidateTestDTO> testDTOS = new ArrayList<>();
        testDTOS.add(candidateTestDTO);

        candidateAssessmentDTO.setAssessmentDuration(10);
        candidateAssessmentDTO.setAssessmentStartTime("");
        candidateAssessmentDTO.setProctor("Default Level");
        candidateAssessmentDTO.setSystem("true");
        candidateAssessmentDTO.setInstructions("");
        candidateAssessmentDTO.setTests(testDTOS);

        List<TestResultInputDTO> testResults = new ArrayList<>();
        TestResultInputDTO testResultInputDTO = new TestResultInputDTO();
        testResultInputDTO.setStatus("SUBMITTED");
        testResults.add(testResultInputDTO);
        assessmentInputDTO.setTestResults(testResults);

        AssessmentTest assessmentTest = new AssessmentTest();
        for(TestResultInputDTO testResultInputDTO1 : testResults){
            when(assessmentTestRepository.getAssessmentTest(any(Key.class))).thenReturn(assessmentTest);

            doNothing().when(assessmentTestRepository).updateAssessmentTest(assessmentTest);
            assessmentTestService.updateAssessmentTest(assessmentInputDTO);
            verify(assessmentTestRepository).updateAssessmentTest(assessmentTest);

        }
        
    }

    @Test
    void getAssessmentTestInfoWithNonAnsweredTest(){
        String pk = KeyBuilder.assessmentPK("1234", "1234");
        String sk = KeyBuilder.assessmentSK("1234", "1234");

        List<Assessment> assessments = new ArrayList<>();
        List<String> testIds = new ArrayList<>();
        testIds.add("1234");


        Assessment savedAssessment = new Assessment();
        savedAssessment.setTestsIds(testIds);
        savedAssessment.setPk(pk);
        savedAssessment.setSk(sk);
        savedAssessment.setOrganizationId("1234");
        savedAssessment.setAssessmentId("1234");
        savedAssessment.setProgressCount(5);
        assessments.add(savedAssessment);

        AssessmentTest assessmentTest = new AssessmentTest();

        when(assessmentRepository.getAssessments(any(Key.class))).thenReturn(assessments);
        when(assessmentTestRepository.getAssessmentTest(any(Key.class))).thenReturn(assessmentTest);

        List<TestInfoDTO> testInfoDTOS = new ArrayList<>();


        assertEquals(assessmentTestService.getAssessmentTestInfo("1234", "1234"), testInfoDTOS);
    }

    @Test
    void getAssessmentTestInfo(){
        String pk = KeyBuilder.assessmentPK("1234", "1234");
        String sk = KeyBuilder.assessmentSK("1234", "1234");

        List<Assessment> assessments = new ArrayList<>();
        List<String> testIds = new ArrayList<>();
        testIds.add("1234");

        List<String> questionIds = new ArrayList<>();
        questionIds.add("1234");


        Assessment savedAssessment = new Assessment();
        savedAssessment.setTestsIds(testIds);
        savedAssessment.setPk(pk);
        savedAssessment.setSk(sk);
        savedAssessment.setOrganizationId("1234");
        savedAssessment.setAssessmentId("1234");
        savedAssessment.setProgressCount(5);
        assessments.add(savedAssessment);

        AssessmentTest assessmentTest = new AssessmentTest();
        assessmentTest.setNumberOfTimesAnswered(1);
        assessmentTest.setQuestionIds(questionIds);
        assessmentTest.setAverageScore(0);
        assessmentTest.setTotalScore(10);

        when(assessmentRepository.getAssessments(any(Key.class))).thenReturn(assessments);
        when(assessmentTestRepository.getAssessmentTest(any(Key.class))).thenReturn(assessmentTest);

        List<TestInfoDTO> testInfoDTOS = new ArrayList<>();
        TestInfoDTO testInfoDTO = new TestInfoDTO();
        testInfoDTO.setCategory("Easiest");
        testInfoDTO.setNumberOfQuestions(1);
        testInfoDTO.setNumberOfCandidates(1);
        testInfoDTO.setTestId("1234");
        testInfoDTOS.add(testInfoDTO);

        assertEquals(assessmentTestService.getAssessmentTestInfo("1234", "1234"), testInfoDTOS);
    }


    @Test
    void saveAssessmentTest_ExistingTest() {
        AssessmentProgressDTO assessmentProgressDTO = createAssessmentProgressDTO();
        CandidateTestDTO candidateTestDTO = assessmentProgressDTO.getAssessment().getTests().get(0);
        AssessmentTest existingTest = new AssessmentTest();
        existingTest.setQuestionIds(List.of("1"));

        when(assessmentTestRepository.checkIfExists(anyString(), anyString(), anyString())).thenReturn(true);
        when(assessmentTestRepository.getAssessmentTest(any(Key.class))).thenReturn(existingTest);

        assessmentTestService.saveAssessmentTest(assessmentProgressDTO);

        verify(assessmentTestRepository).saveAssessmentTest(any(AssessmentTest.class));
    }

    @Test
    void saveAssessmentTest_NullAssessmentProgressDTO() {
        assertThrows(ProcessFailedException.class, () -> assessmentTestService.saveAssessmentTest(null));
    }

    @Test
    void saveAssessmentTest_EmptyTestList() {
        AssessmentProgressDTO assessmentProgressDTO = createAssessmentProgressDTO();
        assessmentProgressDTO.getAssessment().setTests(new ArrayList<>());
        assessmentTestService.saveAssessmentTest(assessmentProgressDTO);
        verify(assessmentTestRepository, never()).saveAssessmentTest(any(AssessmentTest.class));
    }

    @Test
    void updateAssessmentTest_SubmittedStatus() {
        AssessmentInputDTO assessmentInputDTO = createAssessmentInputDTO("SUBMITTED");
        AssessmentTest assessmentTest = new AssessmentTest();
        when(assessmentTestRepository.getAssessmentTest(any(Key.class))).thenReturn(assessmentTest);

        assessmentTestService.updateAssessmentTest(assessmentInputDTO);

        verify(assessmentTestRepository).updateAssessmentTest(any(AssessmentTest.class));
    }

    @Test
    void updateAssessmentTest_NotSubmittedStatus() {
        AssessmentInputDTO assessmentInputDTO = createAssessmentInputDTO("PENDING");

        assessmentTestService.updateAssessmentTest(assessmentInputDTO);

        verify(assessmentTestRepository, never()).updateAssessmentTest(any(AssessmentTest.class));
    }

    @Test
    void updateAssessmentTest_NullInput() {
        assertThrows(ProcessFailedException.class, () -> assessmentTestService.updateAssessmentTest(null));
    }

    @Test
    void getAssessmentTestInfo_NonAnsweredTest() {
        when(assessmentRepository.getAssessments(any(Key.class))).thenReturn(createAssessmentList());
        when(assessmentTestRepository.getAssessmentTest(any(Key.class))).thenReturn(new AssessmentTest());

        List<TestInfoDTO> result = assessmentTestService.getAssessmentTestInfo("123", "456");

        assertTrue(result.isEmpty());
    }

    @Test
    void getAssessmentTestInfo_AnsweredTest() {
        when(assessmentRepository.getAssessments(any(Key.class))).thenReturn(createAssessmentList());
        AssessmentTest answeredTest = new AssessmentTest();
        answeredTest.setNumberOfTimesAnswered(1);
        answeredTest.setQuestionIds(List.of("1"));
        answeredTest.setAverageScore(5.0);
        answeredTest.setTotalScore(10);
        when(assessmentTestRepository.getAssessmentTest(any(Key.class))).thenReturn(answeredTest);

        List<TestInfoDTO> result = assessmentTestService.getAssessmentTestInfo("123", "456");

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
    }


    @Test
    void getAssessmentTestAverages_WithCandidateTests() {
        when(assessmentRepository.getAssessment(any(Key.class))).thenReturn(createAssessment());
        when(assessmentTestRepository.getAssessmentTest(any(Key.class))).thenReturn(new AssessmentTest());
        when(candidateTestRepository.getCandidateTests(any(Key.class))).thenReturn(createCandidateTestList());

        assessmentTestService.getAssessmentTestAverages("123", "456");

        verify(assessmentTestRepository).updateAssessmentTest(any(AssessmentTest.class));
    }

    @Test
    void getAssessmentTestAverages_NoCandidateTests() {
        when(assessmentRepository.getAssessment(any(Key.class))).thenReturn(createAssessment());
        when(assessmentTestRepository.getAssessmentTest(any(Key.class))).thenReturn(new AssessmentTest());
        when(candidateTestRepository.getCandidateTests(any(Key.class))).thenReturn(new ArrayList<>());

        assessmentTestService.getAssessmentTestAverages("123", "456");

        verify(assessmentTestRepository).updateAssessmentTest(any(AssessmentTest.class));
    }


    @Test
    void mergeUnique_ValidLists() {
        List<String> list1 = List.of("1", "2", "3");
        List<String> list2 = List.of("3", "4", "5");

        List<String> result = AssessmentTestServiceImpl.mergeUnique(list1, list2);

        assertEquals(List.of("1", "2", "3", "4", "5"), result);
    }
    @Test
    void mergeUnique_emptyList() {
        List<String> list1 = new ArrayList<>();
        List<String> list2 = List.of("3", "4", "5");

        List<String> result = AssessmentTestServiceImpl.mergeUnique(list1, list2);

        assertEquals(List.of("3", "4", "5"), result);
    }


    @Test
    void getAssessmentTestAverages(){


        List<String> testIds = new ArrayList<>();
        Assessment assessment = new Assessment();
        testIds.add("1234");
        assessment.setTestsIds(testIds);

        List<CandidateTest> candidateTests = new ArrayList<>();
        List<CandidateTest> answeredCandidateTests = new ArrayList<>();

        CandidateTest candidateTest = new CandidateTest();

        candidateTest.setTotalScore(0);
        candidateTest.setCandidateMarks(0);
        candidateTests.add(candidateTest);
        answeredCandidateTests.add(candidateTest);

        when(assessmentRepository.getAssessment(any(Key.class))).thenReturn(assessment);

        AssessmentTest assessmentTest = new AssessmentTest();

        when(assessmentTestRepository.getAssessmentTest(any(Key.class))).thenReturn(assessmentTest);

        assessmentTest.setAverageScore(0);

        when(candidateTestRepository.getCandidateTests(any(Key.class))).thenReturn(candidateTests);
        assessmentTestService.getAssessmentTestAverages("1234","1234");
        doNothing().when(assessmentTestRepository).updateAssessmentTest(assessmentTest);
        verify(assessmentTestRepository).updateAssessmentTest(assessmentTest);



        }

    @Test
    void getAssessmentTestAverageswithZeroCandidateTest(){


        List<String> testIds = new ArrayList<>();
        Assessment assessment = new Assessment();
        testIds.add("1234");
        assessment.setTestsIds(testIds);

        List<CandidateTest> candidateTests = new ArrayList<>();
        List<CandidateTest> answeredCandidateTests = new ArrayList<>();

        CandidateTest candidateTest = new CandidateTest();

        candidateTest.setTotalScore(1);
        candidateTest.setCandidateMarks(0);
        candidateTests.add(candidateTest);
        answeredCandidateTests.add(candidateTest);

        when(assessmentRepository.getAssessment(any(Key.class))).thenReturn(assessment);

        AssessmentTest assessmentTest = new AssessmentTest();

        when(assessmentTestRepository.getAssessmentTest(any(Key.class))).thenReturn(assessmentTest);

        assessmentTest.setAverageScore(0);

        when(candidateTestRepository.getCandidateTests(any(Key.class))).thenReturn(candidateTests);
        assessmentTestService.getAssessmentTestAverages("1234","1234");
        doNothing().when(assessmentTestRepository).updateAssessmentTest(assessmentTest);
        verify(assessmentTestRepository).updateAssessmentTest(assessmentTest);



    }

//helper functions
    private AssessmentProgressDTO createAssessmentProgressDTO() {
        AssessmentProgressDTO dto = new AssessmentProgressDTO();
        dto.setOrganizationId("123");
        dto.setAssessmentId("456");

        CandidateAssessmentDTO assessmentDTO = new CandidateAssessmentDTO();
        assessmentDTO.setAssessmentDuration(60);
        assessmentDTO.setAssessmentStartTime("2023-10-27T10:00:00Z");
        assessmentDTO.setProctor("John Doe");
        assessmentDTO.setSystem("Online");
        assessmentDTO.setInstructions("Follow the instructions carefully.");

        CandidateTestDTO testDTO = new CandidateTestDTO();
        testDTO.setId("789");
        testDTO.setDuration(30);
        testDTO.setTitle("Java Basics");
        testDTO.setPassage("Read the passage and answer the questions.");
        testDTO.setDomain("Programming");

        CandidateQuestionDTO questionDTO1 = new CandidateQuestionDTO();
        questionDTO1.setId("1001");
        CandidateQuestionDTO questionDTO2 = new CandidateQuestionDTO();
        questionDTO2.setId("1002");

        testDTO.setQuestions(List.of(questionDTO1, questionDTO2));

        assessmentDTO.setTests(List.of(testDTO));
        dto.setAssessment(assessmentDTO);

        return dto;
    }

    private AssessmentInputDTO createAssessmentInputDTO(String status) {
        AssessmentInputDTO dto = new AssessmentInputDTO();
        dto.setOrganizationId("123");
        dto.setAssessmentId("456");

        TestResultInputDTO resultDTO = new TestResultInputDTO();
        resultDTO.setTestId("789");
        resultDTO.setStatus(status);
        resultDTO.setTotalScore(100);
        resultDTO.setTotalPassedScore(75.0);
        resultDTO.setTestPercentage(75);

        dto.setTestResults(List.of(resultDTO));

        return dto;
    }

    private List<Assessment> createAssessmentList() {
        Assessment assessment = new Assessment();
        assessment.setOrganizationId("123");
        assessment.setAssessmentId("456");
        assessment.setTestsIds(List.of("789"));
        return List.of(assessment);
    }

    private Assessment createAssessment(){
        Assessment assessment = new Assessment();
        assessment.setOrganizationId("123");
        assessment.setAssessmentId("456");
        assessment.setTestsIds(List.of("789"));
        return assessment;
    }

    private List<CandidateTest> createCandidateTestList() {
        CandidateTest test1 = new CandidateTest();
        test1.setCandidateMarks(80.0);
        test1.setTotalScore(100);

        CandidateTest test2 = new CandidateTest();
        test2.setCandidateMarks(90.0);
        test2.setTotalScore(100);

        return List.of(test1, test2);
    }





}
