package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.AssessmentScoreMetricsDTO;
import com.amap.amapreportmanagementservice.dto.AverageTimesDTO;
import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.AssessmentResponseDTO;
import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.AssessmentsDetails;
import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.MissedQuestionDTO;
import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.TestInfoDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.*;
import com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221.CompletionRateDTO;
import com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221.GeneralDetailsDTO;
import com.amap.amapreportmanagementservice.exceptions.EntityNotFoundException;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.AssessmentRepository;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.ArrayList;
import java.util.List;

import static com.amap.amapreportmanagementservice.TestConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ResponseServiceImplTest extends  BaseTestClass{

    @InjectMocks
    private ResponseServiceImpl responseService;

    @Mock
    private AssessmentRepository assessmentRepository;

    @Mock
    private AssessmentService assessmentService;

    @Mock
    private CandidateAssessmentService candidateAssessmentService;

    @Mock
    private CandidateFeedbackService candidateFeedbackService;

    @Mock
    private TestQuestionService testQuestionService;

    @Mock
    private AssessmentTestService assessmentTestService;

    @Test
    void getGeneralDetails_Success() {
        // Mock input parameters
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;

        // Mock behavior
        when(assessmentRepository.checkIfExist(organizationId, assessmentId)).thenReturn(true);

        AverageTimesDTO mockAverageTimes = new AverageTimesDTO();

        when(assessmentService.getAverageTimes(organizationId, assessmentId)).thenReturn(mockAverageTimes);

        CompletionRateDTO mockCompletionRate = new CompletionRateDTO();
        when(assessmentService.getCompletionRate(organizationId, assessmentId)).thenReturn(mockCompletionRate);

        AssessmentScoreMetricsDTO mockAverageScore = new AssessmentScoreMetricsDTO(85.0, 95.0, 50.0);
        when(candidateAssessmentService.averageScoreMetrics(organizationId, assessmentId)).thenReturn(mockAverageScore);

        List<Double> mockScoreDistribution = List.of(20.0, 30.0, 50.0);
        when(candidateAssessmentService.getPercentageScoreDistribution(organizationId, assessmentId))
                .thenReturn(mockScoreDistribution);

        // Execute the method under test
        GeneralDetailsDTO result = responseService.getGeneralDetails(organizationId, assessmentId);

        // Assertions
        assertNotNull(result);
        assertInstanceOf(GeneralDetailsDTO.class, result);

        verify(assessmentService, times(1)).getAverageTimes(organizationId, assessmentId);
        verify(assessmentService, times(1)).getCompletionRate(organizationId, assessmentId);
        verify(candidateAssessmentService, times(1)).averageScoreMetrics(organizationId, assessmentId);
        verify(candidateAssessmentService, times(1)).getPercentageScoreDistribution(organizationId, assessmentId);
    }

    @Test
    void getGeneralDetails_AssessmentNotFound_ThrowsException() {
        // Mock input parameters
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;

        // Mock behavior
        when(assessmentRepository.checkIfExist(organizationId, assessmentId)).thenReturn(false);

        // Execute and verify exception
        assertThrows(
                EntityNotFoundException.class,
                () -> responseService.getGeneralDetails(organizationId, assessmentId)
        );
    }

    @Test
    void getGeneralDetails_ProcessFailedException_ThrowsException() {
        // Mock input parameters
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;

        // Mock behavior
        when(assessmentRepository.checkIfExist(organizationId, assessmentId)).thenReturn(true);
        when(assessmentService.getAverageTimes(organizationId, assessmentId))
                .thenThrow(new RuntimeException("Some processing error"));

        // Execute and verify exception
        assertThrows(
                ProcessFailedException.class,
                () -> responseService.getGeneralDetails(organizationId, assessmentId)
        );
    }

    @Test
    void getAssessmentDetails_Success() {
        // Mock input parameters
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;

        // Mock behavior
        when(assessmentRepository.checkIfExist(organizationId, assessmentId)).thenReturn(true);

        List<MissedQuestionDTO> mockMissedQuestions = new ArrayList<>();
        when(testQuestionService.getMissedQuestions(organizationId, assessmentId)).thenReturn(mockMissedQuestions);

        List<TestInfoDTO> mockTestAverages = new ArrayList<>();
        when(assessmentTestService.getAssessmentTestInfo(organizationId, assessmentId)).thenReturn(mockTestAverages);

        AssessmentResponseDTO mockAssessment = new AssessmentResponseDTO();
        when(assessmentService.getAssignment(organizationId, assessmentId)).thenReturn(mockAssessment);

        // Execute the method under test
        AssessmentsDetails result = responseService.getAssessmentDetails(organizationId, assessmentId);

        // Assertions
        assertInstanceOf(AssessmentsDetails.class, result);
        verify(testQuestionService, times(1)).getMissedQuestions(organizationId, assessmentId);
        verify(assessmentTestService, times(1)).getAssessmentTestInfo(organizationId, assessmentId);
        verify(assessmentService, times(1)).getAssignment(organizationId, assessmentId);
    }

    @Test
    void getAssessmentDetails_AssessmentNotFound_ThrowsException() {
        // Mock input parameters
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;

        // Mock behavior
        when(assessmentRepository.checkIfExist(organizationId, assessmentId)).thenReturn(false);

        // Execute and verify exception
        assertThrows(
                EntityNotFoundException.class,
                () -> responseService.getAssessmentDetails(organizationId, assessmentId)
        );
    }

    void getAssessmentDetails_ProcessFailedException_ThrowsException() {
        // Mock input parameters
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;

        // Mock behavior
        when(assessmentRepository.checkIfExist(organizationId, assessmentId)).thenReturn(true);
        when(testQuestionService.getMissedQuestions(organizationId, assessmentId))
                .thenThrow(new RuntimeException("Test question service failed"));

        // Execute and verify exception
        assertThrows(
                ProcessFailedException.class,
                () -> responseService.getAssessmentDetails(organizationId, assessmentId)
        );
    }

    @Test
    void getCandidateMetrics_Success() {
        // Mock input parameters
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;

        // Mock behavior
        when(assessmentRepository.checkIfExist(organizationId, assessmentId)).thenReturn(true);

        List<CandidateScoreMetricsDTO> mockCandidateScoreMetrics = new ArrayList<>();
        when(candidateAssessmentService.getCandidateScoreMetrics(organizationId, assessmentId))
                .thenReturn(mockCandidateScoreMetrics);

        // Execute the method under test
        CandidateMetricsDTO result = responseService.getCandidateMetrics(organizationId, assessmentId);

        // Assertions
        assertNotNull(result);
        assertEquals(mockCandidateScoreMetrics, result.getCandidateScoreMetrics());
        verify(assessmentRepository, times(1)).checkIfExist(organizationId,assessmentId);
        verify(candidateAssessmentService, times(1)).getCandidateScoreMetrics(organizationId,assessmentId);
    }

    @Test
    void getCandidateMetrics_AssessmentNotFound_ThrowsException() {
        // Mock input parameters
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;

        // Mock behavior
        when(assessmentRepository.checkIfExist(organizationId, assessmentId)).thenReturn(false);

        // Execute and verify exception
        assertThrows(
                EntityNotFoundException.class,
                () -> responseService.getCandidateMetrics(organizationId, assessmentId)
        );
    }

    @Test
    void getCandidateMetrics_ProcessFailedException_ThrowsException() {
        // Mock input parameters
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;

        // Mock behavior
        when(assessmentRepository.checkIfExist(organizationId, assessmentId)).thenReturn(true);
        when(candidateAssessmentService.getCandidateScoreMetrics(organizationId, assessmentId))
                .thenThrow(new RuntimeException("Failed to fetch candidate metrics"));

        // Execute and verify exception
        assertThrows(
                ProcessFailedException.class,
                () -> responseService.getCandidateMetrics(organizationId, assessmentId)
        );
    }

    @Test
    void getComparableMetrics_Success() {
        // Mock input parameters
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String candidateEmail = TESTEMAIL;
        String candidateId = CANDIDATEID;

        // Mock behavior
        when(assessmentRepository.checkIfExist(organizationId, assessmentId)).thenReturn(true);

        List<ComparativeAnalysisDTO> mockComparativeAnalysis = new ArrayList<>();
        when(candidateAssessmentService.getComparedInfo(organizationId, assessmentId, candidateEmail, candidateId))
                .thenReturn(mockComparativeAnalysis);

        List<CandidateTrajectoryDTO> mockCandidateTrajectory = new ArrayList<>();
        when(candidateAssessmentService.getCandidateTrajectory(organizationId, assessmentId, candidateEmail, candidateId))
                .thenReturn(mockCandidateTrajectory);

        CandidateFeedbackDTO mockCandidateFeedback = new CandidateFeedbackDTO();
        when(candidateFeedbackService.getFeedback(organizationId, assessmentId, candidateId))
                .thenReturn(mockCandidateFeedback);

        // Execute the method under test
        ComparativeTestAnalysisDTO result = responseService.getComparableMetrics(
                organizationId, assessmentId, candidateEmail, candidateId
        );

        // Assertions
        assertNotNull(result);
        assertEquals(mockComparativeAnalysis, result.getComparativeAnalysis());
        assertEquals(mockCandidateTrajectory, result.getCandidateTrajectory());
        assertEquals(mockCandidateFeedback, result.getCandidateFeedback());
    }

    @Test
    void getComparableMetrics_AssessmentNotFound_ThrowsException() {
        // Mock input parameters
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String candidateEmail = TESTEMAIL;
        String candidateId = CANDIDATEID;

        // Mock behavior
        when(assessmentRepository.checkIfExist(organizationId, assessmentId)).thenReturn(false);

        // Execute and verify exception
        assertThrows(
                EntityNotFoundException.class,
                () -> responseService.getComparableMetrics(organizationId, assessmentId, candidateEmail, candidateId)
        );
    }

    @Test
    void getComparableMetrics_ProcessFailedException_ThrowsException() {
        // Mock input parameters
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String candidateEmail = TESTEMAIL;
        String candidateId = CANDIDATEID;

        // Mock behavior
        when(assessmentRepository.checkIfExist(organizationId, assessmentId)).thenReturn(true);
        when(candidateAssessmentService.getComparedInfo(organizationId, assessmentId, candidateEmail, candidateId))
                .thenThrow(new RuntimeException("Failed to fetch comparative analysis"));

        // Execute and verify exception
        assertThrows(
                ProcessFailedException.class,
                () -> responseService.getComparableMetrics(organizationId, assessmentId, candidateEmail, candidateId)
        );
    }

}
