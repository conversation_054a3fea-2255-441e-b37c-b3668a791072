package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.entity.CandidateTest;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.CandidateTestRepository;
import com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.CandidateTestServiceImplTestDTOs;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedDTOs.getAssessmentInputDTO;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class CandidateTestServiceImplFailureTest extends BaseTestClass {
    @InjectMocks
    private CandidateTestServiceImpl candidateTestService;

    @Mock
    private CandidateTestRepository candidateTestRepository;

    @Test
    void saveCandidateTest_Fail() {
        // Prepare test data
        AssessmentProgressDTO assessmentProgressDTO = CandidateTestServiceImplTestDTOs.getAssessmentProgressDTO();
        // Set assessmentProgressDTO properties

        // Mock the behavior of candidateTestRepository
        doThrow(new UnsupportedOperationException("Bob Marley And The Three Wailers")).when(candidateTestRepository).saveCandidateTest(any(CandidateTest.class));

        // Call the method to be tested
//        candidateTestService.saveCandidateTest(assessmentProgressDTO);
        assertThatThrownBy(() -> candidateTestService.saveCandidateTest(assessmentProgressDTO))
                .isInstanceOf(ProcessFailedException.class)
                .hasMessage("Bob Marley And The Three Wailers");

        // Verify that the repository method was called with the expected parameters
        verify(candidateTestRepository, times(assessmentProgressDTO.getAssessment().getTests().size()))
                .saveCandidateTest(any(CandidateTest.class));
    }

    @Test
    void updateCandidateTest_Fail() {
        AssessmentInputDTO assessmentInputDTO = getAssessmentInputDTO();
        // Set assessmentInputDTO properties

        // Mock the behavior of candidateTestRepository
        when(candidateTestRepository.getSpecificCandidateTest(any())).thenReturn(null);
        doNothing().when(candidateTestRepository).updateCandidateTest(any(CandidateTest.class));

        // Call the method to be tested
//        candidateTestService.updateCandidateTest(assessmentInputDTO);
        assertThatThrownBy(() -> candidateTestService.updateCandidateTest(assessmentInputDTO))
                .hasMessage("Cannot invoke \"com.amap.amapreportmanagementservice.entity.CandidateTest.setCandidateMarks(double)\" because \"candidateTest\" is null")
                .isInstanceOf(ProcessFailedException.class);

        // Verify that the repository methods were called with the expected parameters
        verify(candidateTestRepository, times(assessmentInputDTO.getTestResults().size()))
                .getSpecificCandidateTest(any());
        verify(candidateTestRepository, never()).updateCandidateTest(any(CandidateTest.class));
    }


    @Test
    void saveCandidateTest_AssessmentProgressDTOIsNull_ShouldThrowProcessFailedException() {
        assertThrows(ProcessFailedException.class, () -> candidateTestService.saveCandidateTest(null));
    }


    @Test
    void updateCandidateTest_AssessmentInputDTOIsNull_ShouldThrowProcessFailedException() {
        assertThrows(ProcessFailedException.class, () -> candidateTestService.updateCandidateTest(null));
    }
}
