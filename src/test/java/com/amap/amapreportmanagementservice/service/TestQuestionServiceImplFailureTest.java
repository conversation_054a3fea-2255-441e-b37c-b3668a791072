package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.entity.AssessmentTest;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.AssessmentRepository;
import com.amap.amapreportmanagementservice.repository.AssessmentTestRepository;
import com.amap.amapreportmanagementservice.repository.TestQuestionRepository;
import com.amap.amapreportmanagementservice.service.test_data.dtos.invalid_data.TestQuestionServiceImplTestInvalidDTOs;
import com.amap.amapreportmanagementservice.service.test_data.models.SharedModels;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.ArrayList;
import java.util.List;

import static com.amap.amapreportmanagementservice.constants.DbKeysPrefixes.ASSESSMENT_PREFIX;
import static com.amap.amapreportmanagementservice.service.test_data.dtos.valid_data.SharedConstants.*;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class TestQuestionServiceImplFailureTest extends BaseTestClass {
    @InjectMocks
    private TestQuestionServiceImpl testQuestionService;

    @Mock
    private TestQuestionRepository testQuestionRepository;

    @Mock
    private AssessmentTestRepository assessmentTestRepository;

    @Mock
    private AssessmentRepository assessmentRepository;

    @Test
    void testSaveTestQuestions_Fail() {
        // Set up your assessmentProgressDTO as needed
        AssessmentProgressDTO assessmentProgressDTO = TestQuestionServiceImplTestInvalidDTOs.getAssessmentProgressDTO();
        // Create mocks and expected behavior for testQuestionRepository methods
        when(testQuestionRepository.checkIfExist(anyString(), anyString(), anyString(), anyString())).thenReturn(false);

        assertThatThrownBy(() -> testQuestionService.saveTestQuestions(assessmentProgressDTO))
                .isInstanceOf(ProcessFailedException.class)
                .hasMessage("Cannot invoke \"com.amap.amapreportmanagementservice.dto.progress.CandidateAssessmentDTO.getTests()\" because the return value of \"com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO.getAssessment()\" is null");

        verify(testQuestionRepository, never()).saveTestQuestion(any());
    }

    @Test
    void testUpdateTestQuestions_Fail() {
        // Create a sample AssessmentInputDTO
        AssessmentInputDTO assessmentInputDTO = TestQuestionServiceImplTestInvalidDTOs.getAssessmentInputDTO();
        // Set up your assessmentInputDTO as needed

        // Create mocks and expected behavior for testQuestionRepository methods
        when(testQuestionRepository.getTestQuestion(any())).thenReturn(null);

        // Call the method to be tested
        assertThatThrownBy(() -> testQuestionService.updateTestQuestions(assessmentInputDTO))
                .isInstanceOf(ProcessFailedException.class)
                .hasMessage("Cannot invoke \"com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO.getTestId()\" because \"testResultInputDTO\" is null");

        verify(testQuestionRepository, never()).updateTestQuestion(any());
    }

    @Test
    void testGetMissedQuestions_Fail() {
        when(assessmentRepository.getAssessment(any())).thenReturn(TestQuestionServiceImplTestInvalidDTOs.getAssessment());
        List<AssessmentTest> assessmentTests = new ArrayList<>();
        assessmentTests.add(SharedModels.getAssessmentTest());
        when(assessmentTestRepository.getAssessmentTestsWithoutCandidateTest(any())).thenReturn(assessmentTests);
        doThrow(new NullPointerException("Cannot invoke \"java.util.List.stream()\" because \"testQuestions\" is null")).when(testQuestionRepository).getTestQuestions(any());

        // Call the method to be tested
        Key key = Key.builder().partitionValue(KeyBuilder.assessmentPK(organizationId, assessmentId)).sortValue(KeyBuilder.assessmentPK(organizationId, assessmentId)).build();

        // Verify the behavior and assertions for the returned missedQuestions
        assertThatThrownBy(() -> testQuestionService.getMissedQuestions(organizationId, assessmentId))
                .isInstanceOf(ProcessFailedException.class)
                .hasMessage("Cannot invoke \"java.util.List.stream()\" because \"testQuestions\" is null");
        verify(assessmentRepository, times(1)).getAssessment(key);
        verify(assessmentTestRepository, times(1)).getAssessmentTestsWithoutCandidateTest(Key.builder().partitionValue(KeyBuilder.assessmentPK(organizationId, assessmentId)).sortValue(ASSESSMENT_PREFIX).build());
    }
}
