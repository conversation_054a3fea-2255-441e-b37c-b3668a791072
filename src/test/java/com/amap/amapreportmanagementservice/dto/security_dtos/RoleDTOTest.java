package com.amap.amapreportmanagementservice.dto.security_dtos;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class RoleDTOTest {

    @Test
    void testRoleDTO_GetterSetter_ShouldWork() {
        RoleDTO dto = new RoleDTO();
        List<String> permissions = Arrays.asList("permission1", "permission2");
        LocalDateTime now = LocalDateTime.now();

        dto.setId(1);
        dto.setRoleName("Admin");
        dto.setDescription("Administrator role");
        dto.setPermissions(permissions);
        dto.setOrganizationId("org123");
        dto.setSystem(true);
        dto.setUpdatedAt(now);
        dto.setCreatedAt(now);
        dto.setNumberOfUsers(5);

        assertEquals(1, dto.getId());
        assertEquals("Admin", dto.getRoleName());
        assertEquals("Administrator role", dto.getDescription());
        assertEquals(permissions, dto.getPermissions());
        assertEquals("org123", dto.getOrganizationId());
        assertTrue(dto.isSystem());
        assertEquals(now, dto.getUpdatedAt());
        assertEquals(now, dto.getCreatedAt());
        assertEquals(5, dto.getNumberOfUsers());
    }

    @Test
    void testRoleDTO_AllArgsConstructor_ShouldCreateObject() {
        List<String> permissions = Arrays.asList("permission1", "permission2");
        LocalDateTime now = LocalDateTime.now();

        RoleDTO dto = new RoleDTO(1, "Admin", "Administrator role", permissions, "org123", true, now, now, 5);

        assertEquals(1, dto.getId());
        assertEquals("Admin", dto.getRoleName());
        assertEquals("Administrator role", dto.getDescription());
        assertEquals(permissions, dto.getPermissions());
        assertEquals("org123", dto.getOrganizationId());
        assertTrue(dto.isSystem());
        assertEquals(now, dto.getUpdatedAt());
        assertEquals(now, dto.getCreatedAt());
        assertEquals(5, dto.getNumberOfUsers());
    }

    @Test
    void testRoleDTO_NoArgsConstructor_ShouldCreateObject() {
        RoleDTO dto = new RoleDTO();
        assertNotNull(dto);
    }

    @Test
    void testRoleDTO_Builder_ShouldCreateObject() {
        List<String> permissions = Arrays.asList("permission1", "permission2");
        LocalDateTime now = LocalDateTime.now();

        RoleDTO dto = RoleDTO.builder()
                .id(1)
                .roleName("Admin")
                .description("Administrator role")
                .permissions(permissions)
                .organizationId("org123")
                .isSystem(true)
                .updatedAt(now)
                .createdAt(now)
                .numberOfUsers(5)
                .build();

        assertEquals(1, dto.getId());
        assertEquals("Admin", dto.getRoleName());
        assertEquals("Administrator role", dto.getDescription());
        assertEquals(permissions, dto.getPermissions());
        assertEquals("org123", dto.getOrganizationId());
        assertTrue(dto.isSystem());
        assertEquals(now, dto.getUpdatedAt());
        assertEquals(now, dto.getCreatedAt());
        assertEquals(5, dto.getNumberOfUsers());
    }

    @Test
    void testRoleDTO_EqualsAndHashCode_ShouldWork() {
        List<String> permissions1 = Arrays.asList("p1", "p2");
        List<String> permissions2 = Arrays.asList("p3", "p4");
        LocalDateTime now = LocalDateTime.now();

        RoleDTO dto1 = RoleDTO.builder().id(1).roleName("Admin").permissions(permissions1).build();
        RoleDTO dto2 = RoleDTO.builder().id(1).roleName("Admin").permissions(permissions1).build();
        RoleDTO dto3 = RoleDTO.builder().id(2).roleName("User").permissions(permissions2).build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testRoleDTO_ToString_ShouldNotReturnNull() {
        RoleDTO dto = RoleDTO.builder().id(1).roleName("Admin").build();
        assertNotNull(dto.toString());
    }
}