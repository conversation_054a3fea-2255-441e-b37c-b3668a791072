package com.amap.amapreportmanagementservice.dto.feedback;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class FeedbackSurveyDTOTest {

    @Test
    void testFeedbackSurveyDTO_GetterSetter_ShouldWork() {
        FeedbackSurveyDTO dto = new FeedbackSurveyDTO();
        List<FeedbackSurveyQuestionDTO> questions = Arrays.asList(new FeedbackSurveyQuestionDTO());

        dto.setTakerEmail("<EMAIL>");
        dto.setTestTakerId("123");
        dto.setOrganizationId("456");
        dto.setAssessmentId("789");
        dto.setSurveyQuestions(questions);

        assertEquals("<EMAIL>", dto.getTakerEmail());
        assertEquals("123", dto.getTestTakerId());
        assertEquals("456", dto.getOrganizationId());
        assertEquals("789", dto.getAssessmentId());
        assertEquals(questions, dto.getSurveyQuestions());
    }

    @Test
    void testFeedbackSurveyDTO_AllArgsConstructor_ShouldCreateObject() {
        List<FeedbackSurveyQuestionDTO> questions = Arrays.asList(new FeedbackSurveyQuestionDTO());
        FeedbackSurveyDTO dto = new FeedbackSurveyDTO("<EMAIL>", "123", "456", "789", questions);

        assertEquals("<EMAIL>", dto.getTakerEmail());
        assertEquals("123", dto.getTestTakerId());
        assertEquals("456", dto.getOrganizationId());
        assertEquals("789", dto.getAssessmentId());
        assertEquals(questions, dto.getSurveyQuestions());
    }

    @Test
    void testFeedbackSurveyDTO_NoArgsConstructor_ShouldCreateObject() {
        FeedbackSurveyDTO dto = new FeedbackSurveyDTO();
        assertNotNull(dto);
    }

    @Test
    void testFeedbackSurveyDTO_NonNullFields_ShouldNotBeNull() {
        assertThrows(NullPointerException.class, () -> new FeedbackSurveyDTO("<EMAIL>", null, "456", "789", Arrays.asList(new FeedbackSurveyQuestionDTO())));
        assertThrows(NullPointerException.class, () -> new FeedbackSurveyDTO("<EMAIL>", "123", null, "789", Arrays.asList(new FeedbackSurveyQuestionDTO())));
        assertThrows(NullPointerException.class, () -> new FeedbackSurveyDTO("<EMAIL>", "123", "456", null, Arrays.asList(new FeedbackSurveyQuestionDTO())));
    }

    @Test
    void testFeedbackSurveyDTO_EqualsAndHashCode_ShouldWork() {
        List<FeedbackSurveyQuestionDTO> questions = Arrays.asList(new FeedbackSurveyQuestionDTO());
        FeedbackSurveyDTO dto1 = new FeedbackSurveyDTO("<EMAIL>", "123", "456", "789", questions);
        FeedbackSurveyDTO dto2 = new FeedbackSurveyDTO("<EMAIL>", "123", "456", "789", questions);
        FeedbackSurveyDTO dto3 = new FeedbackSurveyDTO("<EMAIL>", "123", "456", "789", questions);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testFeedbackSurveyDTO_ToString_ShouldNotReturnNull() {
        List<FeedbackSurveyQuestionDTO> questions = Arrays.asList(new FeedbackSurveyQuestionDTO());
        FeedbackSurveyDTO dto = new FeedbackSurveyDTO("<EMAIL>", "123", "456", "789", questions);
        assertNotNull(dto.toString());
    }
}