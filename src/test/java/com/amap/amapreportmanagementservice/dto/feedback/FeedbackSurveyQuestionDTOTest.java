package com.amap.amapreportmanagementservice.dto.feedback;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class FeedbackSurveyQuestionDTOTest {

    @Test
    void testFeedbackSurveyQuestionDTO_GetterSetter_ShouldWork() {
        FeedbackSurveyQuestionDTO dto = new FeedbackSurveyQuestionDTO();

        dto.setQuestionText("How satisfied are you?");
        dto.setQuestionAnswer("Very satisfied");
        dto.setType("rating");

        assertEquals("How satisfied are you?", dto.getQuestionText());
        assertEquals("Very satisfied", dto.getQuestionAnswer());
        assertEquals("rating", dto.getType());
    }

    @Test
    void testFeedbackSurveyQuestionDTO_AllArgsConstructor_ShouldCreateObject() {
        FeedbackSurveyQuestionDTO dto = new FeedbackSurveyQuestionDTO("How satisfied are you?", "Very satisfied", "rating");

        assertEquals("How satisfied are you?", dto.getQuestionText());
        assertEquals("Very satisfied", dto.getQuestionAnswer());
        assertEquals("rating", dto.getType());
    }

    @Test
    void testFeedbackSurveyQuestionDTO_NoArgsConstructor_ShouldCreateObject() {
        FeedbackSurveyQuestionDTO dto = new FeedbackSurveyQuestionDTO();
        assertNotNull(dto);
    }

    @Test
    void testFeedbackSurveyQuestionDTO_EqualsAndHashCode_ShouldWork() {
        FeedbackSurveyQuestionDTO dto1 = new FeedbackSurveyQuestionDTO("How satisfied are you?", "Very satisfied", "rating");
        FeedbackSurveyQuestionDTO dto2 = new FeedbackSurveyQuestionDTO("How satisfied are you?", "Very satisfied", "rating");
        FeedbackSurveyQuestionDTO dto3 = new FeedbackSurveyQuestionDTO("Any comments?", "None", "text");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testFeedbackSurveyQuestionDTO_ToString_ShouldNotReturnNull() {
        FeedbackSurveyQuestionDTO dto = new FeedbackSurveyQuestionDTO("How satisfied are you?", "Very satisfied", "rating");
        assertNotNull(dto.toString());
    }
}