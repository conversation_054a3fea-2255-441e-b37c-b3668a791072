package com.amap.amapreportmanagementservice.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class AssessmentScoreMetricsDTOTest {

    @Test
    void testAssessmentScoreMetricsDTO_GetterSetter_ShouldWork() {
        AssessmentScoreMetricsDTO dto = new AssessmentScoreMetricsDTO();
        dto.setAverageScore(85.5);
        dto.setHighestScore(98.0);
        dto.setLowestScore(60.0);

        assertEquals(85.5, dto.getAverageScore());
        assertEquals(98.0, dto.getHighestScore());
        assertEquals(60.0, dto.getLowestScore());
    }

    @Test
    void testAssessmentScoreMetricsDTO_AllArgsConstructor_ShouldCreateObject() {
        AssessmentScoreMetricsDTO dto = new AssessmentScoreMetricsDTO(85.5, 98.0, 60.0);

        assertEquals(85.5, dto.getAverageScore());
        assertEquals(98.0, dto.getHighestScore());
        assertEquals(60.0, dto.getLowestScore());
    }

    @Test
    void testAssessmentScoreMetricsDTO_NoArgsConstructor_ShouldCreateObject() {
        AssessmentScoreMetricsDTO dto = new AssessmentScoreMetricsDTO();
        assertNotNull(dto);
    }

    @Test
    void testAssessmentScoreMetricsDTO_EqualsAndHashCode_ShouldWork() {
        AssessmentScoreMetricsDTO dto1 = new AssessmentScoreMetricsDTO(80.0, 95.0, 65.0);
        AssessmentScoreMetricsDTO dto2 = new AssessmentScoreMetricsDTO(80.0, 95.0, 65.0);
        AssessmentScoreMetricsDTO dto3 = new AssessmentScoreMetricsDTO(85.0, 98.0, 60.0);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAssessmentScoreMetricsDTO_ToString_ShouldNotReturnNull() {
        AssessmentScoreMetricsDTO dto = new AssessmentScoreMetricsDTO(80.0, 95.0, 65.0);
        assertNotNull(dto.toString());
    }
}