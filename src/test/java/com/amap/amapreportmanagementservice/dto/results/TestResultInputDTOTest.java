package com.amap.amapreportmanagementservice.dto.results;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class TestResultInputDTOTest {

    @Test
    void testTestResultInputDTO_GetterSetter_ShouldWork() {
        TestResultInputDTO dto = new TestResultInputDTO();
        List<QuestionResultInputDTO> questionResults = Arrays.asList(new QuestionResultInputDTO());

        dto.setTestId("test123");
        dto.setQuestionResults(questionResults);
        dto.setNumberOfQuestions(10);
        dto.setNumberOfQuestionsFailed(2);
        dto.setNumberOfQuestionsAnswered(8);
        dto.setNumberOfQuestionsPassed(6);
        dto.setTotalScore(80);
        dto.setTotalPassedScore(75.5);
        dto.setTestPercentage(80.0f);
        dto.setTestWindowViolationDuration(120);
        dto.setTestWindowViolationCount(3);
        dto.setTestTakerShotCount(15);
        dto.setTestTakerViolationShotCount(1);
        dto.setTestWindowShotCount(20);
        dto.setTestWindowViolationShotCount(2);
        dto.setStartTime("2023-10-27T10:00:00Z");
        dto.setFinnishTime("2023-10-27T11:00:00Z");
        dto.setStatus("Completed");
        dto.setPassStatus("Passed");

        assertEquals("test123", dto.getTestId());
        assertEquals(questionResults, dto.getQuestionResults());
        assertEquals(10, dto.getNumberOfQuestions());
        assertEquals(2, dto.getNumberOfQuestionsFailed());
        assertEquals(8, dto.getNumberOfQuestionsAnswered());
        assertEquals(6, dto.getNumberOfQuestionsPassed());
        assertEquals(80, dto.getTotalScore());
        assertEquals(75.5, dto.getTotalPassedScore());
        assertEquals(80.0f, dto.getTestPercentage());
        assertEquals(120, dto.getTestWindowViolationDuration());
        assertEquals(3, dto.getTestWindowViolationCount());
        assertEquals(15, dto.getTestTakerShotCount());
        assertEquals(1, dto.getTestTakerViolationShotCount());
        assertEquals(20, dto.getTestWindowShotCount());
        assertEquals(2, dto.getTestWindowViolationShotCount());
        assertEquals("2023-10-27T10:00:00Z", dto.getStartTime());
        assertEquals("2023-10-27T11:00:00Z", dto.getFinnishTime());
        assertEquals("Completed", dto.getStatus());
        assertEquals("Passed", dto.getPassStatus());
    }

    @Test
    void testTestResultInputDTO_AllArgsConstructor_ShouldCreateObject() {
        List<QuestionResultInputDTO> questionResults = Arrays.asList(new QuestionResultInputDTO());

        TestResultInputDTO dto = new TestResultInputDTO("test123", questionResults, 10, 2, 8, 6, 80, 75.5, 80.0f, 120, 3, 15, 1, 20, 2, "2023-10-27T10:00:00Z", "2023-10-27T11:00:00Z", "Completed", "Passed");

        assertEquals("test123", dto.getTestId());
        assertEquals(questionResults, dto.getQuestionResults());
        assertEquals(10, dto.getNumberOfQuestions());
        assertEquals(2, dto.getNumberOfQuestionsFailed());
        assertEquals(8, dto.getNumberOfQuestionsAnswered());
        assertEquals(6, dto.getNumberOfQuestionsPassed());
        assertEquals(80, dto.getTotalScore());
        assertEquals(75.5, dto.getTotalPassedScore());
        assertEquals(80.0f, dto.getTestPercentage());
        assertEquals(120, dto.getTestWindowViolationDuration());
        assertEquals(3, dto.getTestWindowViolationCount());
        assertEquals(15, dto.getTestTakerShotCount());
        assertEquals(1, dto.getTestTakerViolationShotCount());
        assertEquals(20, dto.getTestWindowShotCount());
        assertEquals(2, dto.getTestWindowViolationShotCount());
        assertEquals("2023-10-27T10:00:00Z", dto.getStartTime());
        assertEquals("2023-10-27T11:00:00Z", dto.getFinnishTime());
        assertEquals("Completed", dto.getStatus());
        assertEquals("Passed", dto.getPassStatus());
    }

    @Test
    void testTestResultInputDTO_NoArgsConstructor_ShouldCreateObject() {
        TestResultInputDTO dto = new TestResultInputDTO();
        assertNotNull(dto);
    }

    @Test
    void testTestResultInputDTO_Builder_ShouldCreateObject() {
        List<QuestionResultInputDTO> questionResults = Arrays.asList(new QuestionResultInputDTO());

        TestResultInputDTO dto = TestResultInputDTO.builder()
                .testId("test123")
                .questionResults(questionResults)
                .numberOfQuestions(10)
                .numberOfQuestionsFailed(2)
                .numberOfQuestionsAnswered(8)
                .numberOfQuestionsPassed(6)
                .totalScore(80)
                .totalPassedScore(75.5)
                .testPercentage(80.0f)
                .testWindowViolationDuration(120)
                .testWindowViolationCount(3)
                .testTakerShotCount(15)
                .testTakerViolationShotCount(1)
                .testWindowShotCount(20)
                .testWindowViolationShotCount(2)
                .StartTime("2023-10-27T10:00:00Z")
                .finnishTime("2023-10-27T11:00:00Z")
                .status("Completed")
                .passStatus("Passed")
                .build();

        assertEquals("test123", dto.getTestId());
        assertEquals(questionResults, dto.getQuestionResults());
        assertEquals(10, dto.getNumberOfQuestions());
        assertEquals(2, dto.getNumberOfQuestionsFailed());
        assertEquals(8, dto.getNumberOfQuestionsAnswered());
        assertEquals(6, dto.getNumberOfQuestionsPassed());
        assertEquals(80, dto.getTotalScore());
        assertEquals(75.5, dto.getTotalPassedScore());
        assertEquals(80.0f, dto.getTestPercentage());
        assertEquals(120, dto.getTestWindowViolationDuration());
        assertEquals(3, dto.getTestWindowViolationCount());
        assertEquals(15, dto.getTestTakerShotCount());
        assertEquals(1, dto.getTestTakerViolationShotCount());
        assertEquals(20, dto.getTestWindowShotCount());
        assertEquals(2, dto.getTestWindowViolationShotCount());
        assertEquals("2023-10-27T10:00:00Z", dto.getStartTime());
        assertEquals("2023-10-27T11:00:00Z", dto.getFinnishTime());
        assertEquals("Completed", dto.getStatus());
        assertEquals("Passed", dto.getPassStatus());
    }

    @Test
    void testTestResultInputDTO_NonNullFields_ShouldNotBeNull() {
        assertThrows(NullPointerException.class, () -> TestResultInputDTO.builder().build());
    }

    @Test
    void testTestResultInputDTO_EqualsAndHashCode_ShouldWork() {
        TestResultInputDTO dto1 = TestResultInputDTO.builder().testId("test1").numberOfQuestions(10).build();
        TestResultInputDTO dto2 = TestResultInputDTO.builder().testId("test1").numberOfQuestions(10).build();
        TestResultInputDTO dto3 = TestResultInputDTO.builder().testId("test2").numberOfQuestions(20).build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testTestResultInputDTO_ToString_ShouldNotReturnNull() {
        TestResultInputDTO dto = TestResultInputDTO.builder().testId("test1").build();
        assertNotNull(dto.toString());
        assertTrue(dto.toString().contains("test1"));
    }

    @Test
    void testTestResultInputDTO_NonNullFieldExplicitTest() {
        // Testing that the testId field cannot be null
        NullPointerException exception = assertThrows(NullPointerException.class, () -> {
            new TestResultInputDTO(null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, "", "", "", "");
        });
        assertTrue(exception.getMessage().contains("testId"));

        // Testing with builder
        exception = assertThrows(NullPointerException.class, () -> {
            TestResultInputDTO.builder()
                    .numberOfQuestions(10)
                    .numberOfQuestionsAnswered(10)
                    .build();
        });
        assertTrue(exception.getMessage().contains("testId"));
    }

    @Test
    void testTestResultInputDTO_EqualsWithDifferentFieldValues() {
        TestResultInputDTO dto1 = TestResultInputDTO.builder()
                .testId("test1")
                .numberOfQuestions(10)
                .status("Completed")
                .build();

        TestResultInputDTO dto2 = TestResultInputDTO.builder()
                .testId("test1")
                .numberOfQuestions(10)
                .status("Failed")
                .build();

        TestResultInputDTO dto3 = TestResultInputDTO.builder()
                .testId("test1")
                .numberOfQuestions(20)
                .status("Completed")
                .build();

        // Same testId but different status - should not be equal
        assertNotEquals(dto1, dto2);

        // Same testId but different numberOfQuestions - should not be equal
        assertNotEquals(dto1, dto3);
    }

    @Test
    void testTestResultInputDTO_EqualsWithNull() {
        TestResultInputDTO dto = TestResultInputDTO.builder()
                .testId("test1")
                .build();

        assertNotEquals(null, dto);
        assertNotEquals(dto, null);
    }

    @Test
    void testTestResultInputDTO_EqualsWithDifferentType() {
        TestResultInputDTO dto = TestResultInputDTO.builder()
                .testId("test1")
                .build();

        assertNotEquals(dto, "test1");
        assertNotEquals(dto, 123);
    }

    @Test
    void testTestResultInputDTO_ReflexiveAndSymmetric() {
        TestResultInputDTO dto1 = TestResultInputDTO.builder()
                .testId("test1")
                .numberOfQuestions(10)
                .build();

        TestResultInputDTO dto2 = TestResultInputDTO.builder()
                .testId("test1")
                .numberOfQuestions(10)
                .build();

        // Reflexive: an object should equal itself
        assertEquals(dto1, dto1);

        // Symmetric: if a equals b, then b equals a
        assertEquals(dto1, dto2);
        assertEquals(dto2, dto1);
    }

    @Test
    void testTestResultInputDTO_TransitiveProperty() {
        TestResultInputDTO dto1 = TestResultInputDTO.builder()
                .testId("test1")
                .numberOfQuestions(10)
                .build();

        TestResultInputDTO dto2 = TestResultInputDTO.builder()
                .testId("test1")
                .numberOfQuestions(10)
                .build();

        TestResultInputDTO dto3 = TestResultInputDTO.builder()
                .testId("test1")
                .numberOfQuestions(10)
                .build();

        // Transitive: if a equals b and b equals c, then a equals c
        assertEquals(dto1, dto2);
        assertEquals(dto2, dto3);
        assertEquals(dto1, dto3);
    }

    @Test
    void testTestResultInputDTO_FieldCaseCorrection() {
        // Since you have 'StartTime' with a capital S in your class definition,
        // this test verifies the builder properly sets and gets this field
        TestResultInputDTO dto = TestResultInputDTO.builder()
                .testId("test1")
                .StartTime("2023-11-01T10:00:00Z")
                .build();

        assertEquals("2023-11-01T10:00:00Z", dto.getStartTime());
    }
}