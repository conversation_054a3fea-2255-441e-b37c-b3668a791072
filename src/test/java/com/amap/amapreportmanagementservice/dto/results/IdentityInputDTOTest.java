package com.amap.amapreportmanagementservice.dto.results;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class IdentityInputDTOTest {

    @Test
    void testIdentityInputDTO_GetterSetter_ShouldWork() {
        IdentityInputDTO dto = new IdentityInputDTO();
        dto.setLinkId("link123");
        dto.setLinkHead("head456");

        assertEquals("link123", dto.getLinkId());
        assertEquals("head456", dto.getLinkHead());
    }

    @Test
    void testIdentityInputDTO_AllArgsConstructor_ShouldCreateObject() {
        IdentityInputDTO dto = new IdentityInputDTO("link123", "head456");

        assertEquals("link123", dto.getLinkId());
        assertEquals("head456", dto.getLinkHead());
    }

    @Test
    void testIdentityInputDTO_NoArgsConstructor_ShouldCreateObject() {
        IdentityInputDTO dto = new IdentityInputDTO();
        assertNotNull(dto);
    }

    @Test
    void testIdentityInputDTO_Builder_ShouldCreateObject() {
        IdentityInputDTO dto = IdentityInputDTO.builder()
                .linkId("link123")
                .linkHead("head456")
                .build();

        assertEquals("link123", dto.getLinkId());
        assertEquals("head456", dto.getLinkHead());
    }

    @Test
    void testIdentityInputDTO_EqualsAndHashCode_ShouldWork() {
        IdentityInputDTO dto1 = IdentityInputDTO.builder().linkId("link1").linkHead("head1").build();
        IdentityInputDTO dto2 = IdentityInputDTO.builder().linkId("link1").linkHead("head1").build();
        IdentityInputDTO dto3 = IdentityInputDTO.builder().linkId("link2").linkHead("head2").build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testIdentityInputDTO_ToString_ShouldNotReturnNull() {
        IdentityInputDTO dto = IdentityInputDTO.builder().linkId("link1").build();
        assertNotNull(dto.toString());
    }
}