package com.amap.amapreportmanagementservice.dto.results;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class FlaggedInputDTOTest {

    @Test
    void testFlaggedInputDTO_GetterSetter_ShouldWork() {
        FlaggedInputDTO dto = new FlaggedInputDTO();
        List<String> reasons = Arrays.asList("Reason 1", "Reason 2");

        dto.setId("flag123");
        dto.setAssessmentId("assess456");
        dto.setOrganizationId("org789");
        dto.setTestId("test101");
        dto.setQuestionId("question202");
        dto.setQuestionText("What is the capital?");
        dto.setTestTakerId("taker303");
        dto.setTestTakerEmail("<EMAIL>");
        dto.setReasonForFlagging(reasons);

        assertEquals("flag123", dto.getId());
        assertEquals("assess456", dto.getAssessmentId());
        assertEquals("org789", dto.getOrganizationId());
        assertEquals("test101", dto.getTestId());
        assertEquals("question202", dto.getQuestionId());
        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals("taker303", dto.getTestTakerId());
        assertEquals("<EMAIL>", dto.getTestTakerEmail());
        assertEquals(reasons, dto.getReasonForFlagging());
    }

    @Test
    void testFlaggedInputDTO_AllArgsConstructor_ShouldCreateObject() {
        List<String> reasons = Arrays.asList("Reason 1", "Reason 2");

        FlaggedInputDTO dto = new FlaggedInputDTO("flag123", "assess456", "org789", "test101", "question202", "What is the capital?", "taker303", "<EMAIL>", reasons);

        assertEquals("flag123", dto.getId());
        assertEquals("assess456", dto.getAssessmentId());
        assertEquals("org789", dto.getOrganizationId());
        assertEquals("test101", dto.getTestId());
        assertEquals("question202", dto.getQuestionId());
        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals("taker303", dto.getTestTakerId());
        assertEquals("<EMAIL>", dto.getTestTakerEmail());
        assertEquals(reasons, dto.getReasonForFlagging());
    }

    @Test
    void testFlaggedInputDTO_NoArgsConstructor_ShouldCreateObject() {
        FlaggedInputDTO dto = new FlaggedInputDTO();
        assertNotNull(dto);
    }

    @Test
    void testFlaggedInputDTO_Builder_ShouldCreateObject() {
        List<String> reasons = Arrays.asList("Reason 1", "Reason 2");

        FlaggedInputDTO dto = FlaggedInputDTO.builder()
                .id("flag123")
                .assessmentId("assess456")
                .organizationId("org789")
                .testId("test101")
                .questionId("question202")
                .questionText("What is the capital?")
                .testTakerId("taker303")
                .testTakerEmail("<EMAIL>")
                .reasonForFlagging(reasons)
                .build();

        assertEquals("flag123", dto.getId());
        assertEquals("assess456", dto.getAssessmentId());
        assertEquals("org789", dto.getOrganizationId());
        assertEquals("test101", dto.getTestId());
        assertEquals("question202", dto.getQuestionId());
        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals("taker303", dto.getTestTakerId());
        assertEquals("<EMAIL>", dto.getTestTakerEmail());
        assertEquals(reasons, dto.getReasonForFlagging());
    }

    @Test
    void testFlaggedInputDTO_NonNullFields_ShouldNotBeNull() {
        List<String> reasons = Arrays.asList("Reason 1", "Reason 2");
        assertThrows(NullPointerException.class, () -> new FlaggedInputDTO(null, "assess456", "org789", "test101", "question202", "What is the capital?", "taker303", "<EMAIL>", reasons));
        assertThrows(NullPointerException.class, () -> new FlaggedInputDTO("flag123", null, "org789", "test101", "question202", "What is the capital?", "taker303", "<EMAIL>", reasons));
        assertThrows(NullPointerException.class, () -> new FlaggedInputDTO("flag123", "assess456", null, "test101", "question202", "What is the capital?", "taker303", "<EMAIL>", reasons));
        assertThrows(NullPointerException.class, () -> new FlaggedInputDTO("flag123", "assess456", "org789", null, "question202", "What is the capital?", "taker303", "<EMAIL>", reasons));
        assertThrows(NullPointerException.class, () -> new FlaggedInputDTO("flag123", "assess456", "org789", "test101", null, "What is the capital?", "taker303", "<EMAIL>", reasons));
    }

    @Test
    void testFlaggedInputDTO_EqualsAndHashCode_ShouldWork() {
        List<String> reasons1 = Arrays.asList("Reason 1", "Reason 2");
        List<String> reasons2 = Arrays.asList("Reason 3", "Reason 4");

        FlaggedInputDTO dto1 = FlaggedInputDTO.builder().id("1").assessmentId("a1").organizationId("o1").testId("t1").questionId("q1").reasonForFlagging(reasons1).build();
        FlaggedInputDTO dto2 = FlaggedInputDTO.builder().id("1").assessmentId("a1").organizationId("o1").testId("t1").questionId("q1").reasonForFlagging(reasons1).build();
        FlaggedInputDTO dto3 = FlaggedInputDTO.builder().id("2").assessmentId("a2").organizationId("o2").testId("t2").questionId("q2").reasonForFlagging(reasons2).build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testFlaggedInputDTO_ToString_ShouldNotReturnNull() {
        FlaggedInputDTO dto = FlaggedInputDTO.builder()
                .id("1")
                .assessmentId("a1") //Provide a value
                .organizationId("o1") //Provide a value
                .testId("t1") //Provide a value
                .questionId("q1") //Provide a value
                .build();
        assertNotNull(dto.toString());
    }
}