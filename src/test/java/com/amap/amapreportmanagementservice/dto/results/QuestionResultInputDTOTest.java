package com.amap.amapreportmanagementservice.dto.results;

import com.amap.amapreportmanagementservice.dto.progress.*;
import com.amap.amapreportmanagementservice.entity.ReferenceSolution;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class QuestionResultInputDTOTest {

    @Test
    void testQuestionResultInputDTO_GetterSetter_ShouldWork() {
        QuestionResultInputDTO dto = new QuestionResultInputDTO();
        TestTakerCodeAnswerDTO answers = new TestTakerCodeAnswerDTO();
        MultipleAnswerDTO multipleSelect = new MultipleAnswerDTO();
        MultipleChoiceAnswerDTO multipleChoice = new MultipleChoiceAnswerDTO();
        TrueOrFalseAnswerDTO trueOrFalse = new TrueOrFalseAnswerDTO();
        FillInAnswerDTO fillIn = new FillInAnswerDTO();
        MatchMatrixAnswerDTO matchMatrix = new MatchMatrixAnswerDTO();
        EssayAnswerDTO essay = new EssayAnswerDTO();

        dto.setQuestionId("q123");
        dto.setQuestionText("What is the capital?");
        dto.setQuestionType("Multiple Choice");
        dto.setIsAnswerCorrect("true");
        dto.setIsComprehension("false");
        dto.setDifficultyLevel("Easy");
        dto.setTestTakerCode(answers);
        dto.setMultipleSelectAnswer(multipleSelect);
        dto.setMultipleChoiceAnswer(multipleChoice);
        dto.setTrueOrFalseAnswer(trueOrFalse);
        dto.setFillInAnswer(fillIn);
        dto.setMatchMatrixAnswer(matchMatrix);
        dto.setEssayAnswer(essay);
        dto.setIdleTime(10);
        dto.setScored(9.5);
        dto.setScore(9);

        assertEquals("q123", dto.getQuestionId());
        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals("Multiple Choice", dto.getQuestionType());
        assertEquals("true", dto.getIsAnswerCorrect());
        assertEquals("false", dto.getIsComprehension());
        assertEquals("Easy", dto.getDifficultyLevel());
        assertEquals(answers, dto.getTestTakerCode());
        assertEquals(multipleSelect, dto.getMultipleSelectAnswer());
        assertEquals(multipleChoice, dto.getMultipleChoiceAnswer());
        assertEquals(trueOrFalse, dto.getTrueOrFalseAnswer());
        assertEquals(fillIn, dto.getFillInAnswer());
        assertEquals(matchMatrix, dto.getMatchMatrixAnswer());
        assertEquals(essay, dto.getEssayAnswer());
        assertEquals(10, dto.getIdleTime());
        assertEquals(9.5, dto.getScored());
        assertEquals(9, dto.getScore());
    }

    @Test
    void testQuestionResultInputDTO_AllArgsConstructor_ShouldCreateObject() {
        // Create test data
        List<String> testTakerAnswers = Arrays.asList("answer1", "answer2");
        MultipleAnswerDTO multipleSelect = new MultipleAnswerDTO();
        MultipleChoiceAnswerDTO multipleChoice = new MultipleChoiceAnswerDTO();
        TrueOrFalseAnswerDTO trueOrFalse = new TrueOrFalseAnswerDTO();
        FillInAnswerDTO fillIn = new FillInAnswerDTO();
        MatchMatrixAnswerDTO matchMatrix = new MatchMatrixAnswerDTO();
        EssayAnswerDTO essay = new EssayAnswerDTO();
        TestTakerCodeAnswerDTO answers = new TestTakerCodeAnswerDTO();
        List<CodeTemplateDTO> codeTemplate = new ArrayList<>();
        List<CodeResultDTO> codeResults = new ArrayList<>();
        List<ReferenceSolutionDTO> referenceSolutions = new ArrayList<>();
        int idleTime = 10;
        double scored = 9.5;
        int score = 9;
        CodeExecutionSummaryDTO codeExecutionSummary = new CodeExecutionSummaryDTO();
        CodeConstraintsDTO codeConstraint = new CodeConstraintsDTO();


        // Create DTO using all args constructor
        QuestionResultInputDTO dto = new QuestionResultInputDTO(
                "q123",
                "What is the capital?",
                "Multiple Choice",
                "true",
                "false",
                "Easy",
                testTakerAnswers,
                multipleSelect,
                multipleChoice,
                trueOrFalse,
                fillIn,
                matchMatrix,
                essay,
                answers,
                codeTemplate,
                codeResults,
                idleTime,
                scored,
                score,
                "codeReview",
                codeExecutionSummary,
                codeConstraint,
                referenceSolutions
        );

        // Verify all fields
        assertEquals("q123", dto.getQuestionId());
        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals("Multiple Choice", dto.getQuestionType());
        assertEquals("true", dto.getIsAnswerCorrect());
        assertEquals("false", dto.getIsComprehension());
        assertEquals("Easy", dto.getDifficultyLevel());
        assertEquals(2, dto.getTestTakerAnswers().size());
        assertEquals(multipleSelect, dto.getMultipleSelectAnswer());
        assertEquals(multipleChoice, dto.getMultipleChoiceAnswer());
        assertEquals(trueOrFalse, dto.getTrueOrFalseAnswer());
        assertEquals(fillIn, dto.getFillInAnswer());
        assertEquals(matchMatrix, dto.getMatchMatrixAnswer());
        assertEquals(essay, dto.getEssayAnswer());
        assertEquals(codeTemplate, dto.getCodeTemplates());
        assertEquals(codeResults, dto.getCodeResults());
        assertEquals(idleTime, dto.getIdleTime());
        assertEquals(scored, dto.getScored());
        assertEquals(score, dto.getScore());
        assertEquals(codeExecutionSummary, dto.getCodeExecutionSummary());
        assertEquals(codeConstraint, dto.getCodeConstraint());
    }

    @Test
    void testQuestionResultInputDTO_NoArgsConstructor_ShouldCreateObject() {
        QuestionResultInputDTO dto = new QuestionResultInputDTO();
        assertNotNull(dto);
    }

    @Test
    void testQuestionResultInputDTO_Builder_ShouldCreateObject() {
        List<String> answers = Arrays.asList("answer1", "answer2");
        MultipleAnswerDTO multipleSelect = new MultipleAnswerDTO();
        MultipleChoiceAnswerDTO multipleChoice = new MultipleChoiceAnswerDTO();
        TrueOrFalseAnswerDTO trueOrFalse = new TrueOrFalseAnswerDTO();
        FillInAnswerDTO fillIn = new FillInAnswerDTO();
        MatchMatrixAnswerDTO matchMatrix = new MatchMatrixAnswerDTO();
        EssayAnswerDTO essay = new EssayAnswerDTO();

        QuestionResultInputDTO dto = QuestionResultInputDTO.builder()
                .questionId("q123")
                .questionText("What is the capital?")
                .questionType("Multiple Choice")
                .isAnswerCorrect("true")
                .isComprehension("false")
                .difficultyLevel("Easy")
                .rawTestTakerAnswersData(answers)
                .multipleSelectAnswer(multipleSelect)
                .multipleChoiceAnswer(multipleChoice)
                .trueOrFalseAnswer(trueOrFalse)
                .fillInAnswer(fillIn)
                .matchMatrixAnswer(matchMatrix)
                .essayAnswer(essay)
                .idleTime(10)
                .scored(9.5)
                .score(9)
                .build();

        assertEquals("q123", dto.getQuestionId());
        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals("Multiple Choice", dto.getQuestionType());
        assertEquals("true", dto.getIsAnswerCorrect());
        assertEquals("false", dto.getIsComprehension());
        assertEquals("Easy", dto.getDifficultyLevel());
        assertEquals(answers, dto.getTestTakerAnswers());
        assertEquals(multipleSelect, dto.getMultipleSelectAnswer());
        assertEquals(multipleChoice, dto.getMultipleChoiceAnswer());
        assertEquals(trueOrFalse, dto.getTrueOrFalseAnswer());
        assertEquals(fillIn, dto.getFillInAnswer());
        assertEquals(matchMatrix, dto.getMatchMatrixAnswer());
        assertEquals(essay, dto.getEssayAnswer());
        assertEquals(10, dto.getIdleTime());
        assertEquals(9.5, dto.getScored());
        assertEquals(9, dto.getScore());
    }

    @Test
    void testQuestionResultInputDTO_NonNullFields_ShouldNotBeNull() {
        assertThrows(NullPointerException.class, () -> QuestionResultInputDTO.builder().build());
    }

    @Test
    void testQuestionResultInputDTO_EqualsAndHashCode_ShouldWork() {
        QuestionResultInputDTO dto1 = QuestionResultInputDTO.builder().questionId("q1").questionText("text1").build();
        QuestionResultInputDTO dto2 = QuestionResultInputDTO.builder().questionId("q1").questionText("text1").build();
        QuestionResultInputDTO dto3 = QuestionResultInputDTO.builder().questionId("q2").questionText("text2").build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testQuestionResultInputDTO_ToString_ShouldNotReturnNull() {
        QuestionResultInputDTO dto = QuestionResultInputDTO.builder().questionId("q1").build();
        assertNotNull(dto.toString());
    }
}