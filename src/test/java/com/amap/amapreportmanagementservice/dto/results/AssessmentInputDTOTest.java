package com.amap.amapreportmanagementservice.dto.results;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class AssessmentInputDTOTest {

    @Test
    void testAssessmentInputDTO_GetterSetter_ShouldWork() {
        AssessmentInputDTO dto = new AssessmentInputDTO();
        List<String> testList = Arrays.asList("test1", "test2");
        List<String> submittedTests = Arrays.asList("submitted1", "submitted2");
        List<TestResultInputDTO> testResults = Arrays.asList(new TestResultInputDTO());
        List<String> windowViolation = Arrays.asList("violation1", "violation2");
        List<String> intervalScreenshots = Arrays.asList("screenshot1", "screenshot2");
        IdentityInputDTO identity = new IdentityInputDTO();

        dto.setOrganizationId("org123");
        dto.setTestTakerId("taker456");
        dto.setEmail("<EMAIL>");
        dto.setAssessmentId("assess789");
        dto.setStartTime("2023-10-27T10:00:00Z");
        dto.setProctorLevel("Level 1");
        dto.setProctor("proctor1");
        dto.setAssessmentEndTime("2023-10-27T11:00:00Z");
        dto.setAssessmentDuration(3600);
        dto.setTestTakerDurationSeconds(3500);
        dto.setAssessmentWindowViolationCount(5);
        dto.setAssessmentWindowViolationDuration(100);
        dto.setAssessmentTakerShotCount(10);
        dto.setAssessmentTakerViolationShotCount(2);
        dto.setWindowShotCount(15);
        dto.setWindowViolationShotCount(3);
        dto.setTestCount(2);
        dto.setTestList(testList);
        dto.setSubmittedTests(submittedTests);
        dto.setTotalFiveToStart("5");
        dto.setTestResults(testResults);
        dto.setWindowViolation(windowViolation);
        dto.setIntervalScreenshots(intervalScreenshots);
        dto.setIdentity(identity);
        dto.setStatus("Completed");
        dto.setReportCallbackURL("http://example.com/callback");

        assertEquals("org123", dto.getOrganizationId());
        assertEquals("taker456", dto.getTestTakerId());
        assertEquals("<EMAIL>", dto.getEmail());
        assertEquals("assess789", dto.getAssessmentId());
        assertEquals("2023-10-27T10:00:00Z", dto.getStartTime());
        assertEquals("Level 1", dto.getProctorLevel());
        assertEquals("proctor1", dto.getProctor());
        assertEquals("2023-10-27T11:00:00Z", dto.getAssessmentEndTime());
        assertEquals(3600, dto.getAssessmentDuration());
        assertEquals(3500, dto.getTestTakerDurationSeconds());
        assertEquals(5, dto.getAssessmentWindowViolationCount());
        assertEquals(100, dto.getAssessmentWindowViolationDuration());
        assertEquals(10, dto.getAssessmentTakerShotCount());
        assertEquals(2, dto.getAssessmentTakerViolationShotCount());
        assertEquals(15, dto.getWindowShotCount());
        assertEquals(3, dto.getWindowViolationShotCount());
        assertEquals(2, dto.getTestCount());
        assertEquals(testList, dto.getTestList());
        assertEquals(submittedTests, dto.getSubmittedTests());
        assertEquals("5", dto.getTotalFiveToStart());
        assertEquals(testResults, dto.getTestResults());
        assertEquals(windowViolation, dto.getWindowViolation());
        assertEquals(intervalScreenshots, dto.getIntervalScreenshots());
        assertEquals(identity, dto.getIdentity());
        assertEquals("Completed", dto.getStatus());
        assertEquals("http://example.com/callback", dto.getReportCallbackURL());
    }

    @Test
    void testAssessmentInputDTO_AllArgsConstructor_ShouldCreateObject() {
        List<String> testList = Arrays.asList("test1", "test2");
        List<String> submittedTests = Arrays.asList("submitted1", "submitted2");
        List<TestResultInputDTO> testResults = Arrays.asList(new TestResultInputDTO());
        List<String> windowViolation = Arrays.asList("violation1", "violation2");
        List<String> intervalScreenshots = Arrays.asList("screenshot1", "screenshot2");
        IdentityInputDTO identity = new IdentityInputDTO();

        AssessmentInputDTO dto = new AssessmentInputDTO("org123", "taker456", "<EMAIL>", "assess789", "2023-10-27T10:00:00Z", "Level 1", "proctor1", "2023-10-27T11:00:00Z", 3600, 3500, 5, 100, 10, 2, 15, 3, 2, testList, submittedTests, "5", testResults, windowViolation, intervalScreenshots, identity, "Completed", "http://example.com/callback");

        assertEquals("org123", dto.getOrganizationId());
        assertEquals("taker456", dto.getTestTakerId());
        assertEquals("<EMAIL>", dto.getEmail());
        assertEquals("assess789", dto.getAssessmentId());
        assertEquals("2023-10-27T10:00:00Z", dto.getStartTime());
        assertEquals("Level 1", dto.getProctorLevel());
        assertEquals("proctor1", dto.getProctor());
        assertEquals("2023-10-27T11:00:00Z", dto.getAssessmentEndTime());
        assertEquals(3600, dto.getAssessmentDuration());
        assertEquals(3500, dto.getTestTakerDurationSeconds());
        assertEquals(5, dto.getAssessmentWindowViolationCount());
        assertEquals(100, dto.getAssessmentWindowViolationDuration());
        assertEquals(10, dto.getAssessmentTakerShotCount());
        assertEquals(2, dto.getAssessmentTakerViolationShotCount());
        assertEquals(15, dto.getWindowShotCount());
        assertEquals(3, dto.getWindowViolationShotCount());
        assertEquals(2, dto.getTestCount());
        assertEquals(testList, dto.getTestList());
        assertEquals(submittedTests, dto.getSubmittedTests());
        assertEquals("5", dto.getTotalFiveToStart());
        assertEquals(testResults, dto.getTestResults());
        assertEquals(windowViolation, dto.getWindowViolation());
        assertEquals(intervalScreenshots, dto.getIntervalScreenshots());
        assertEquals(identity, dto.getIdentity());
        assertEquals("Completed", dto.getStatus());
        assertEquals("http://example.com/callback", dto.getReportCallbackURL());
    }

    @Test
    void testAssessmentInputDTO_NoArgsConstructor_ShouldCreateObject() {
        AssessmentInputDTO dto = new AssessmentInputDTO();
        assertNotNull(dto);
    }

    @Test
    void testAssessmentInputDTO_Builder_ShouldCreateObject() {
        List<String> testList = Arrays.asList("test1", "test2");
        List<String> submittedTests = Arrays.asList("submitted1", "submitted2");
        List<TestResultInputDTO> testResults = Arrays.asList(new TestResultInputDTO());
        List<String> windowViolation = Arrays.asList("violation1", "violation2");
        List<String> intervalScreenshots = Arrays.asList("screenshot1", "screenshot2");
        IdentityInputDTO identity = new IdentityInputDTO();

        AssessmentInputDTO dto = AssessmentInputDTO.builder()
                .organizationId("org123")
                .testTakerId("taker456")
                .email("<EMAIL>")
                .assessmentId("assess789")
                .startTime("2023-10-27T10:00:00Z")
                .proctorLevel("Level 1")
                .proctor("proctor1")
                .assessmentEndTime("2023-10-27T11:00:00Z")
                .assessmentDuration(3600)
                .testTakerDurationSeconds(3500)
                .assessmentWindowViolationCount(5)
                .assessmentWindowViolationDuration(100)
                .assessmentTakerShotCount(10)
                .assessmentTakerViolationShotCount(2)
                .windowShotCount(15)
                .windowViolationShotCount(3)
                .testCount(2)
                .testList(testList)
                .submittedTests(submittedTests)
                .totalFiveToStart("5")
                .testResults(testResults)
                .windowViolation(windowViolation)
                .intervalScreenshots(intervalScreenshots)
                .identity(identity)
                .status("Completed")
                .reportCallbackURL("http://example.com/callback")
                .build();

        assertEquals("org123", dto.getOrganizationId());
        assertEquals("taker456", dto.getTestTakerId());
        assertEquals("<EMAIL>", dto.getEmail());
        assertEquals("assess789", dto.getAssessmentId());
        assertEquals("2023-10-27T10:00:00Z", dto.getStartTime());
        assertEquals("Level 1", dto.getProctorLevel());
        assertEquals("proctor1", dto.getProctor());
        assertEquals("2023-10-27T11:00:00Z", dto.getAssessmentEndTime());
        assertEquals(3600, dto.getAssessmentDuration());
        assertEquals(3500, dto.getTestTakerDurationSeconds());
        assertEquals(5, dto.getAssessmentWindowViolationCount());
        assertEquals(100, dto.getAssessmentWindowViolationDuration());
        assertEquals(10, dto.getAssessmentTakerShotCount());
        assertEquals(2, dto.getAssessmentTakerViolationShotCount());
        assertEquals(15, dto.getWindowShotCount());
        assertEquals(3, dto.getWindowViolationShotCount());
        assertEquals(2, dto.getTestCount());
        assertEquals(testList, dto.getTestList());
        assertEquals(submittedTests, dto.getSubmittedTests());
        assertEquals("5", dto.getTotalFiveToStart());
        assertEquals(testResults, dto.getTestResults());
        assertEquals(windowViolation, dto.getWindowViolation());
        assertEquals(intervalScreenshots, dto.getIntervalScreenshots());
        assertEquals(identity, dto.getIdentity());
        assertEquals("Completed", dto.getStatus());
        assertEquals("http://example.com/callback", dto.getReportCallbackURL());
    }

    @Test
    void testAssessmentInputDTO_NonNullFields_ShouldNotBeNull() {
        // Test that organizationId cannot be null
        assertThrows(NullPointerException.class, () -> {
            AssessmentInputDTO.builder()
                    .testTakerId("taker456")
                    .build();
        });

        // Test that testTakerId cannot be null
        assertThrows(NullPointerException.class, () -> {
            AssessmentInputDTO.builder()
                    .organizationId("org123")
                    .build();
        });

        // Test that both non-null fields are required
        assertThrows(NullPointerException.class, () -> {
            AssessmentInputDTO.builder().build();
        });
    }

    @Test
    void testAssessmentInputDTO_EqualsAndHashCode_ShouldWork() {
        AssessmentInputDTO dto1 = AssessmentInputDTO.builder()
                .organizationId("org123")
                .testTakerId("taker456")
                .build();

        AssessmentInputDTO dto2 = AssessmentInputDTO.builder()
                .organizationId("org123")
                .testTakerId("taker456")
                .build();

        AssessmentInputDTO dto3 = AssessmentInputDTO.builder()
                .organizationId("org789")
                .testTakerId("taker123")
                .build();

        assertEquals(dto1, dto2);
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto2, dto3);

        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1.hashCode(), dto3.hashCode());

        // Test with null
        AssessmentInputDTO dto4 = null;
        assertNotEquals(dto1, dto4);

        // Test with different object type
        assertNotEquals(dto1, "Not a DTO");

        // Test reflexivity
        assertEquals(dto1, dto1);

        // Test symmetry
        assertEquals(dto2, dto1);
    }
}