package com.amap.amapreportmanagementservice.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class AssessmentScoreMetricsDistributionDTOTest {

    @Test
    void testAssessmentScoreMetricsDistributionDTO_GetterSetter_ShouldWork() {
        AssessmentScoreMetricsDistributionDTO dto = new AssessmentScoreMetricsDistributionDTO();
        dto.setGraph("graphData");

        assertEquals("graphData", dto.getGraph());
    }

    @Test
    void testAssessmentScoreMetricsDistributionDTO_AllArgsConstructor_ShouldCreateObject() {
        AssessmentScoreMetricsDistributionDTO dto = new AssessmentScoreMetricsDistributionDTO("graphData");

        assertEquals("graphData", dto.getGraph());
    }

    @Test
    void testAssessmentScoreMetricsDistributionDTO_NoArgsConstructor_ShouldCreateObject() {
        AssessmentScoreMetricsDistributionDTO dto = new AssessmentScoreMetricsDistributionDTO();
        assertNotNull(dto);
    }

    @Test
    void testAssessmentScoreMetricsDistributionDTO_EqualsAndHashCode_ShouldWork() {
        AssessmentScoreMetricsDistributionDTO dto1 = new AssessmentScoreMetricsDistributionDTO("graph1");
        AssessmentScoreMetricsDistributionDTO dto2 = new AssessmentScoreMetricsDistributionDTO("graph1");
        AssessmentScoreMetricsDistributionDTO dto3 = new AssessmentScoreMetricsDistributionDTO("graph2");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAssessmentScoreMetricsDistributionDTO_ToString_ShouldNotReturnNull() {
        AssessmentScoreMetricsDistributionDTO dto = new AssessmentScoreMetricsDistributionDTO("graph1");
        assertNotNull(dto.toString());
    }
}