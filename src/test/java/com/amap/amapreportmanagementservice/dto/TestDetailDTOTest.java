package com.amap.amapreportmanagementservice.dto;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class TestDetailDTOTest {

    @Test
    void testTestDetailDTO_GetterSetter_ShouldWork() {
        TestDetailDTO dto = new TestDetailDTO();
        List<FlaggedQuestionDTO> flaggedQuestions = Arrays.asList(new FlaggedQuestionDTO());

        dto.setTestTitle("Test 1");
        dto.setNumberOfQuestions(10);
        dto.setAverageScore(85.5);
        dto.setNumberOfFlaggedQuestions(2);
        dto.setFlaggedQuestions(flaggedQuestions);
        dto.setEasiest(true);
        dto.setMostChallenging(false);

        assertEquals("Test 1", dto.getTestTitle());
        assertEquals(10, dto.getNumberOfQuestions());
        assertEquals(85.5, dto.getAverageScore());
        assertEquals(2, dto.getNumberOfFlaggedQuestions());
        assertEquals(flaggedQuestions, dto.getFlaggedQuestions());
        assertTrue(dto.getEasiest());
        assertFalse(dto.getMostChallenging());
    }

    @Test
    void testTestDetailDTO_AllArgsConstructor_ShouldCreateObject() {
        List<FlaggedQuestionDTO> flaggedQuestions = Arrays.asList(new FlaggedQuestionDTO());
        TestDetailDTO dto = new TestDetailDTO("Test 1", 10, 85.5, 2, flaggedQuestions, true, false);

        assertEquals("Test 1", dto.getTestTitle());
        assertEquals(10, dto.getNumberOfQuestions());
        assertEquals(85.5, dto.getAverageScore());
        assertEquals(2, dto.getNumberOfFlaggedQuestions());
        assertEquals(flaggedQuestions, dto.getFlaggedQuestions());
        assertTrue(dto.getEasiest());
        assertFalse(dto.getMostChallenging());
    }

    @Test
    void testTestDetailDTO_NoArgsConstructor_ShouldCreateObject() {
        TestDetailDTO dto = new TestDetailDTO();
        assertNotNull(dto);
    }

    @Test
    void testTestDetailDTO_StringConstructor_ShouldCreateObject() {
        TestDetailDTO dto = new TestDetailDTO("Test A");
        assertNotNull(dto);
    }

    @Test
    void testTestDetailDTO_EqualsAndHashCode_ShouldWork() {
        List<FlaggedQuestionDTO> flaggedQuestions1 = Arrays.asList(new FlaggedQuestionDTO("Q1", 1));
        List<FlaggedQuestionDTO> flaggedQuestions2 = Arrays.asList(new FlaggedQuestionDTO("Q1", 1));
        List<FlaggedQuestionDTO> flaggedQuestions3 = Arrays.asList(new FlaggedQuestionDTO("Q2", 2));

        TestDetailDTO dto1 = new TestDetailDTO("Test A", 10, 80.0, 1, flaggedQuestions1, true, false);
        TestDetailDTO dto2 = new TestDetailDTO("Test A", 10, 80.0, 1, flaggedQuestions2, true, false);
        TestDetailDTO dto3 = new TestDetailDTO("Test B", 15, 90.0, 2, flaggedQuestions3, false, true);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testTestDetailDTO_ToString_ShouldNotReturnNull() {
        TestDetailDTO dto = new TestDetailDTO("Test A", 10, 80.0, 1, Arrays.asList(new FlaggedQuestionDTO("Q1", 1)), true, false);
        assertNotNull(dto.toString());
    }
}