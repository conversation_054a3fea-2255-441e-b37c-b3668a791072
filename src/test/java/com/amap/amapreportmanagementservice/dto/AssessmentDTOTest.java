package com.amap.amapreportmanagementservice.dto;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

class AssessmentDTOTest {

    @Test
    void testAssessmentDTO_GetterSetter_ShouldWork() {
        AssessmentDTO dto = new AssessmentDTO();
        LocalDateTime createdAt = LocalDateTime.of(2023, 10, 27, 10, 0, 0);
        LocalDateTime updatedAt = LocalDateTime.of(2023, 10, 27, 11, 0, 0);

        dto.setAssessmentId("assess123");
        dto.setTitle("Test Assessment");
        dto.setInstructions("Follow the instructions carefully.");
        dto.setAverageScore(85.5);
        dto.setCreatedAt(createdAt.toString());
        dto.setUpdatedAt(updatedAt.toString());
        dto.setNumberOfCandidates(50);
        dto.setNumberOfSections(5);
        dto.setNumberOfFeedbacks(10);
        dto.setNumberOfCompleted(40);
        dto.setNumberOfIncomplete(10);

        assertEquals("assess123", dto.getAssessmentId());
        assertEquals("Test Assessment", dto.getTitle());
        assertEquals("Follow the instructions carefully.", dto.getInstructions());
        assertEquals(85.5, dto.getAverageScore());
        assertEquals(createdAt.toString(), dto.getCreatedAt());
        assertEquals(updatedAt.toString(), dto.getUpdatedAt());
        assertEquals(50, dto.getNumberOfCandidates());
        assertEquals(5, dto.getNumberOfSections());
        assertEquals(10, dto.getNumberOfFeedbacks());
        assertEquals(40, dto.getNumberOfCompleted());
        assertEquals(10, dto.getNumberOfIncomplete());
    }

    @Test
    void testAssessmentDTO_AllArgsConstructor_ShouldCreateObject() {
        LocalDateTime createdAt = LocalDateTime.of(2023, 10, 27, 10, 0, 0);
        LocalDateTime updatedAt = LocalDateTime.of(2023, 10, 27, 11, 0, 0);

        AssessmentDTO dto = new AssessmentDTO("assess123", "Test Assessment", "Follow the instructions carefully.", 85.5, createdAt.toString(), updatedAt.toString(), 50, 5, 10, 40, 10);

        assertEquals("assess123", dto.getAssessmentId());
        assertEquals("Test Assessment", dto.getTitle());
        assertEquals("Follow the instructions carefully.", dto.getInstructions());
        assertEquals(85.5, dto.getAverageScore());
        assertEquals(createdAt.toString(), dto.getCreatedAt());
        assertEquals(updatedAt.toString(), dto.getUpdatedAt());
        assertEquals(50, dto.getNumberOfCandidates());
        assertEquals(5, dto.getNumberOfSections());
        assertEquals(10, dto.getNumberOfFeedbacks());
        assertEquals(40, dto.getNumberOfCompleted());
        assertEquals(10, dto.getNumberOfIncomplete());
    }

    @Test
    void testAssessmentDTO_NoArgsConstructor_ShouldCreateObject() {
        AssessmentDTO dto = new AssessmentDTO();
        assertNotNull(dto);
    }

    @Test
    void testAssessmentDTO_Builder_ShouldCreateObject() {
        LocalDateTime createdAt = LocalDateTime.of(2023, 10, 27, 10, 0, 0);
        LocalDateTime updatedAt = LocalDateTime.of(2023, 10, 27, 11, 0, 0);

        AssessmentDTO dto = AssessmentDTO.builder()
                .assessmentId("assess123")
                .title("Test Assessment")
                .instructions("Follow the instructions carefully.")
                .averageScore(85.5)
                .createdAt(createdAt.toString())
                .updatedAt(updatedAt.toString())
                .numberOfCandidates(50)
                .numberOfSections(5)
                .numberOfFeedbacks(10)
                .numberOfCompleted(40)
                .numberOfIncomplete(10)
                .build();

        assertEquals("assess123", dto.getAssessmentId());
        assertEquals("Test Assessment", dto.getTitle());
        assertEquals("Follow the instructions carefully.", dto.getInstructions());
        assertEquals(85.5, dto.getAverageScore());
        assertEquals(createdAt.toString(), dto.getCreatedAt());
        assertEquals(updatedAt.toString(), dto.getUpdatedAt());
        assertEquals(50, dto.getNumberOfCandidates());
        assertEquals(5, dto.getNumberOfSections());
        assertEquals(10, dto.getNumberOfFeedbacks());
        assertEquals(40, dto.getNumberOfCompleted());
        assertEquals(10, dto.getNumberOfIncomplete());
    }

    @Test
    void testAssessmentDTO_EqualsAndHashCode_ShouldWork() {
        AssessmentDTO dto1 = AssessmentDTO.builder().assessmentId("1").title("A").build();
        AssessmentDTO dto2 = AssessmentDTO.builder().assessmentId("1").title("A").build();
        AssessmentDTO dto3 = AssessmentDTO.builder().assessmentId("2").title("B").build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAssessmentDTO_ToString_ShouldNotReturnNull() {
        AssessmentDTO dto = AssessmentDTO.builder().assessmentId("1").build();
        assertNotNull(dto.toString());
    }
}