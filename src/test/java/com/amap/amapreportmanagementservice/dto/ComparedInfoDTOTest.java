package com.amap.amapreportmanagementservice.dto;

import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.ComparativeAnalysisDTO;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class ComparedInfoDTOTest {

    @Test
    void testComparedInfoDTO_GetterSetter_ShouldWork() {
        ComparedInfoDTO dto = new ComparedInfoDTO();
        List<ComparativeAnalysisDTO> analysis = Arrays.asList(new ComparativeAnalysisDTO());

        dto.setCandidateEmail("<EMAIL>");
        dto.setCandidateId("candidate123");
        dto.setComparativeAnalysis(analysis);

        assertEquals("<EMAIL>", dto.getCandidateEmail());
        assertEquals("candidate123", dto.getCandidateId());
        assertEquals(analysis, dto.getComparativeAnalysis());
    }

    @Test
    void testComparedInfoDTO_AllArgsConstructor_ShouldCreateObject() {
        List<ComparativeAnalysisDTO> analysis = Arrays.asList(new ComparativeAnalysisDTO());
        ComparedInfoDTO dto = new ComparedInfoDTO("<EMAIL>", "candidate123", analysis);

        assertEquals("<EMAIL>", dto.getCandidateEmail());
        assertEquals("candidate123", dto.getCandidateId());
        assertEquals(analysis, dto.getComparativeAnalysis());
    }

    @Test
    void testComparedInfoDTO_NoArgsConstructor_ShouldCreateObject() {
        ComparedInfoDTO dto = new ComparedInfoDTO();
        assertNotNull(dto);
    }

    @Test
    void testComparedInfoDTO_EqualsAndHashCode_ShouldWork() {
        List<ComparativeAnalysisDTO> analysis1 = Arrays.asList(new ComparativeAnalysisDTO("A"));
        List<ComparativeAnalysisDTO> analysis2 = Arrays.asList(new ComparativeAnalysisDTO("A"));
        List<ComparativeAnalysisDTO> analysis3 = Arrays.asList(new ComparativeAnalysisDTO("B"));

        ComparedInfoDTO dto1 = new ComparedInfoDTO("<EMAIL>", "1", analysis1);
        ComparedInfoDTO dto2 = new ComparedInfoDTO("<EMAIL>", "1", analysis2);
        ComparedInfoDTO dto3 = new ComparedInfoDTO("<EMAIL>", "2", analysis3);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testComparedInfoDTO_ToString_ShouldNotReturnNull() {
        ComparedInfoDTO dto = new ComparedInfoDTO("<EMAIL>", "1", Arrays.asList(new ComparativeAnalysisDTO("A")));
        assertNotNull(dto.toString());
    }
}