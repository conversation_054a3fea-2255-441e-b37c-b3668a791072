package com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class AssessmentResponseDTOTest {

    @Test
    void assessmentResponseDTO_GetterSetter_ShouldWork() {
        AssessmentResponseDTO dto = new AssessmentResponseDTO();

        dto.setAssessmentId("assess123");
        dto.setAssessmentDuration(120);
        dto.setTitle("Java Assessment");
        dto.setAverageTimeTaken(60.5);
        dto.setSystem(true);
        dto.setNumberOfTakers(50);
        dto.setNumberOfUniqueTakers(45);
        dto.setTotalScore(100);
        dto.setTestsIds(Arrays.asList("test1", "test2"));
        dto.setProgressCount(20);
        dto.setCompletedCount(30);
        dto.setAveragePercentage(75.0);
        dto.setInstruction("Follow instructions.");

        assertEquals("assess123", dto.getAssessmentId());
        assertEquals(120, dto.getAssessmentDuration());
        assertEquals("Java Assessment", dto.getTitle());
        assertEquals(60.5, dto.getAverageTimeTaken());
        assertTrue(dto.isSystem());
        assertEquals(50, dto.getNumberOfTakers());
        assertEquals(45, dto.getNumberOfUniqueTakers());
        assertEquals(100, dto.getTotalScore());
        assertEquals(Arrays.asList("test1", "test2"), dto.getTestsIds());
        assertEquals(20, dto.getProgressCount());
        assertEquals(30, dto.getCompletedCount());
        assertEquals(75.0, dto.getAveragePercentage());
        assertEquals("Follow instructions.", dto.getInstruction());
    }

    @Test
    void assessmentResponseDTO_AllArgsConstructor_ShouldCreateObject() {
        List<String> testsIds = Arrays.asList("test1", "test2");
        AssessmentResponseDTO dto = new AssessmentResponseDTO(
                "assess123", 120, "Java Assessment", 60.5, true, 50, 45, 100, testsIds, 20, 30, 75.0, "Follow instructions."
        );

        assertEquals("assess123", dto.getAssessmentId());
        assertEquals(120, dto.getAssessmentDuration());
        assertEquals("Java Assessment", dto.getTitle());
        assertEquals(60.5, dto.getAverageTimeTaken());
        assertTrue(dto.isSystem());
        assertEquals(50, dto.getNumberOfTakers());
        assertEquals(45, dto.getNumberOfUniqueTakers());
        assertEquals(100, dto.getTotalScore());
        assertEquals(testsIds, dto.getTestsIds());
        assertEquals(20, dto.getProgressCount());
        assertEquals(30, dto.getCompletedCount());
        assertEquals(75.0, dto.getAveragePercentage());
        assertEquals("Follow instructions.", dto.getInstruction());
    }

    @Test
    void assessmentResponseDTO_NoArgsConstructor_ShouldCreateObject() {
        AssessmentResponseDTO dto = new AssessmentResponseDTO();
        assertNotNull(dto);
    }

    @Test
    void assessmentResponseDTO_EqualsAndHashCode_ShouldWork() {
        List<String> testsIds1 = Arrays.asList("test1", "test2");
        List<String> testsIds2 = Arrays.asList("test1", "test2");

        AssessmentResponseDTO dto1 = new AssessmentResponseDTO(
                "assess123", 120, "Java Assessment", 60.5, true, 50, 45, 100, testsIds1, 20, 30, 75.0, "Follow instructions."
        );

        AssessmentResponseDTO dto2 = new AssessmentResponseDTO(
                "assess123", 120, "Java Assessment", 60.5, true, 50, 45, 100, testsIds2, 20, 30, 75.0, "Follow instructions."
        );

        AssessmentResponseDTO dto3 = new AssessmentResponseDTO(
                "assess456", 120, "Java Assessment", 60.5, true, 50, 45, 100, testsIds1, 20, 30, 75.0, "Follow instructions."
        );

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void assessmentResponseDTO_ToString_ShouldNotReturnNull() {
        AssessmentResponseDTO dto = new AssessmentResponseDTO(
                "assess123", 120, "Java Assessment", 60.5, true, 50, 45, 100, Arrays.asList("test1", "test2"), 20, 30, 75.0, "Follow instructions."
        );
        assertNotNull(dto.toString());
    }

    @Test
    void assessmentResponseDTO_List_NullTest() {
        AssessmentResponseDTO dto = new AssessmentResponseDTO();
        dto.setTestsIds(null);
        assertNull(dto.getTestsIds());

        AssessmentResponseDTO dto2 = new AssessmentResponseDTO(
                "assess123", 120, "Java Assessment", 60.5, true, 50, 45, 100, null, 20, 30, 75.0, "Follow instructions."
        );
        assertNull(dto2.getTestsIds());
    }

    @Test
    void assessmentResponseDTO_Boolean_FalseTest() {
        AssessmentResponseDTO dto = new AssessmentResponseDTO();
        dto.setSystem(false);
        assertFalse(dto.isSystem());

        AssessmentResponseDTO dto2 = new AssessmentResponseDTO(
                "assess123", 120, "Java Assessment", 60.5, false, 50, 45, 100, Arrays.asList("test1", "test2"), 20, 30, 75.0, "Follow instructions."
        );

        assertFalse(dto2.isSystem());
    }
}