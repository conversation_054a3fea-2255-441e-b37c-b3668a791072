package com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class MissedQuestionDTOTest {

    @Test
    void missedQuestionDTO_GetterSetter_ShouldWork() {
        MissedQuestionDTO dto = new MissedQuestionDTO();

        dto.setTestId("test123");
        dto.setQuestionText("What is the capital?");
        dto.setPercentageOfCandidates(75.5);

        assertEquals("test123", dto.getTestId());
        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals(75.5, dto.getPercentageOfCandidates());
    }

    @Test
    void missedQuestionDTO_AllArgsConstructor_ShouldCreateObject() {
        MissedQuestionDTO dto = new MissedQuestionDTO("test123", "What is the capital?", 75.5);

        assertEquals("test123", dto.getTestId());
        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals(75.5, dto.getPercentageOfCandidates());
    }

    @Test
    void missedQuestionDTO_NoArgsConstructor_ShouldCreateObject() {
        MissedQuestionDTO dto = new MissedQuestionDTO();
        assertNotNull(dto);
    }

    @Test
    void missedQuestionDTO_EqualsAndHashCode_ShouldWork() {
        MissedQuestionDTO dto1 = new MissedQuestionDTO("test123", "What is the capital?", 75.5);
        MissedQuestionDTO dto2 = new MissedQuestionDTO("test123", "What is the capital?", 75.5);
        MissedQuestionDTO dto3 = new MissedQuestionDTO("test456", "What is the capital?", 75.5);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void missedQuestionDTO_ToString_ShouldNotReturnNull() {
        MissedQuestionDTO dto = new MissedQuestionDTO("test123", "What is the capital?", 75.5);
        assertNotNull(dto.toString());
    }

    @Test
    void missedQuestionDTO_NullValues_ShouldWork() {
        MissedQuestionDTO dto = new MissedQuestionDTO();
        dto.setTestId(null);
        dto.setQuestionText(null);
        dto.setPercentageOfCandidates(null);

        assertNull(dto.getTestId());
        assertNull(dto.getQuestionText());
        assertNull(dto.getPercentageOfCandidates());

        MissedQuestionDTO dto2 = new MissedQuestionDTO(null, null, null);
        assertNull(dto2.getTestId());
        assertNull(dto2.getQuestionText());
        assertNull(dto2.getPercentageOfCandidates());
    }
}