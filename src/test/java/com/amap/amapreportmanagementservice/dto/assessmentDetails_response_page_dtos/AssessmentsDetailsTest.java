package com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class AssessmentsDetailsTest {

    @Test
    void assessmentsDetails_GetterSetter_ShouldWork() {
        AssessmentsDetails details = new AssessmentsDetails();

        AssessmentResponseDTO assessment = new AssessmentResponseDTO();
        List<MissedQuestionDTO> missedQuestions = Arrays.asList(new MissedQuestionDTO(), new MissedQuestionDTO());
        List<TestInfoDTO> testDetails = Arrays.asList(new TestInfoDTO(), new TestInfoDTO());

        details.setAssessment(assessment);
        details.setMostMissedQuestions(missedQuestions);
        details.setTestDetails(testDetails);

        assertEquals(assessment, details.getAssessment());
        assertEquals(missedQuestions, details.getMostMissedQuestions());
        assertEquals(testDetails, details.getTestDetails());
    }

    @Test
    void assessmentsDetails_AllArgsConstructor_ShouldCreateObject() {
        AssessmentResponseDTO assessment = new AssessmentResponseDTO();
        List<MissedQuestionDTO> missedQuestions = Arrays.asList(new MissedQuestionDTO(), new MissedQuestionDTO());
        List<TestInfoDTO> testDetails = Arrays.asList(new TestInfoDTO(), new TestInfoDTO());

        AssessmentsDetails details = new AssessmentsDetails(assessment, missedQuestions, testDetails);

        assertEquals(assessment, details.getAssessment());
        assertEquals(missedQuestions, details.getMostMissedQuestions());
        assertEquals(testDetails, details.getTestDetails());
    }

    @Test
    void assessmentsDetails_NoArgsConstructor_ShouldCreateObject() {
        AssessmentsDetails details = new AssessmentsDetails();
        assertNotNull(details);
    }

    @Test
    void assessmentsDetails_EqualsAndHashCode_ShouldWork() {
        AssessmentResponseDTO assessment1 = new AssessmentResponseDTO();
        AssessmentResponseDTO assessment2 = new AssessmentResponseDTO();
        List<MissedQuestionDTO> missedQuestions1 = Arrays.asList(new MissedQuestionDTO());
        List<MissedQuestionDTO> missedQuestions2 = Arrays.asList(new MissedQuestionDTO());
        List<TestInfoDTO> testDetails1 = Arrays.asList(new TestInfoDTO());
        List<TestInfoDTO> testDetails2 = Arrays.asList(new TestInfoDTO());

        AssessmentsDetails details1 = new AssessmentsDetails(assessment1, missedQuestions1, testDetails1);
        AssessmentsDetails details2 = new AssessmentsDetails(assessment2, missedQuestions2, testDetails2);
        AssessmentsDetails details3 = new AssessmentsDetails(new AssessmentResponseDTO(), Arrays.asList(new MissedQuestionDTO(), new MissedQuestionDTO()), Arrays.asList(new TestInfoDTO(), new TestInfoDTO()));

        assertEquals(details1, details2);
        assertEquals(details1.hashCode(), details2.hashCode());
        assertNotEquals(details1, details3);
        assertNotEquals(details1.hashCode(), details3.hashCode());
    }

    @Test
    void assessmentsDetails_ToString_ShouldNotReturnNull() {
        AssessmentsDetails details = new AssessmentsDetails(new AssessmentResponseDTO(), Arrays.asList(new MissedQuestionDTO()), Arrays.asList(new TestInfoDTO()));
        assertNotNull(details.toString());
    }

    @Test
    void assessmentsDetails_NullLists_ShouldWork() {
        AssessmentsDetails details = new AssessmentsDetails();
        details.setMostMissedQuestions(null);
        details.setTestDetails(null);

        assertNull(details.getMostMissedQuestions());
        assertNull(details.getTestDetails());

        AssessmentsDetails details2 = new AssessmentsDetails(new AssessmentResponseDTO(), null, null);

        assertNull(details2.getMostMissedQuestions());
        assertNull(details2.getTestDetails());
    }
}