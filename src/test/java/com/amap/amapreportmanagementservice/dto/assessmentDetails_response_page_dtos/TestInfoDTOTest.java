package com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class TestInfoDTOTest {

    @Test
    void testInfoDTO_GetterSetter_ShouldWork() {
        TestInfoDTO dto = new TestInfoDTO();

        dto.setTestId("test123");
        dto.setTestAverage(80.5);
        dto.setAveragePercentage(75.0);
        dto.setNumberOfCandidates(50);
        dto.setTestTitle("Math Test");
        dto.setNumberOfQuestions(20);
        dto.setNumberOfFlags(5);
        dto.setTotalCandidateMarks(400.0);
        dto.setCategory("Mathematics");

        assertEquals("test123", dto.getTestId());
        assertEquals(80.5, dto.getTestAverage());
        assertEquals(75.0, dto.getAveragePercentage());
        assertEquals(50, dto.getNumberOfCandidates());
        assertEquals("Math Test", dto.getTestTitle());
        assertEquals(20, dto.getNumberOfQuestions());
        assertEquals(5, dto.getNumberOfFlags());
        assertEquals(400.0, dto.getTotalCandidateMarks());
        assertEquals("Mathematics", dto.getCategory());
    }

    @Test
    void testInfoDTO_AllArgsConstructor_ShouldCreateObject() {
        TestInfoDTO dto = new TestInfoDTO(
                "test123", 80.5, 75.0, 50, "Math Test", 20, 5, 400.0, "Mathematics"
        );

        assertEquals("test123", dto.getTestId());
        assertEquals(80.5, dto.getTestAverage());
        assertEquals(75.0, dto.getAveragePercentage());
        assertEquals(50, dto.getNumberOfCandidates());
        assertEquals("Math Test", dto.getTestTitle());
        assertEquals(20, dto.getNumberOfQuestions());
        assertEquals(5, dto.getNumberOfFlags());
        assertEquals(400.0, dto.getTotalCandidateMarks());
        assertEquals("Mathematics", dto.getCategory());
    }

    @Test
    void testInfoDTO_NoArgsConstructor_ShouldCreateObject() {
        TestInfoDTO dto = new TestInfoDTO();
        assertNotNull(dto);
    }

    @Test
    void testInfoDTO_EqualsAndHashCode_ShouldWork() {
        TestInfoDTO dto1 = new TestInfoDTO("test123", 80.5, 75.0, 50, "Math Test", 20, 5, 400.0, "Mathematics");
        TestInfoDTO dto2 = new TestInfoDTO("test123", 80.5, 75.0, 50, "Math Test", 20, 5, 400.0, "Mathematics");
        TestInfoDTO dto3 = new TestInfoDTO("test456", 80.5, 75.0, 50, "Math Test", 20, 5, 400.0, "Mathematics");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testInfoDTO_ToString_ShouldNotReturnNull() {
        TestInfoDTO dto = new TestInfoDTO("test123", 80.5, 75.0, 50, "Math Test", 20, 5, 400.0, "Mathematics");
        assertNotNull(dto.toString());
    }

    @Test
    void testInfoDTO_NullValues_ShouldWork() {
        TestInfoDTO dto = new TestInfoDTO();
        dto.setTestId(null);
        dto.setTestTitle(null);
        dto.setCategory(null);
        dto.setTestAverage(0);
        dto.setAveragePercentage(0);
        dto.setNumberOfCandidates(0);
        dto.setNumberOfQuestions(0);
        dto.setNumberOfFlags(0);
        dto.setTotalCandidateMarks(0);

        assertNull(dto.getTestId());
        assertNull(dto.getTestTitle());
        assertNull(dto.getCategory());
        assertEquals(0, dto.getTestAverage());
        assertEquals(0, dto.getAveragePercentage());
        assertEquals(0, dto.getNumberOfCandidates());
        assertEquals(0, dto.getNumberOfQuestions());
        assertEquals(0, dto.getNumberOfFlags());
        assertEquals(0, dto.getTotalCandidateMarks());

        TestInfoDTO dto2 = new TestInfoDTO(null, 0, 0, 0, null, 0, 0, 0, null);

        assertNull(dto2.getTestId());
        assertNull(dto2.getTestTitle());
        assertNull(dto2.getCategory());
        assertEquals(0, dto2.getTestAverage());
        assertEquals(0, dto2.getAveragePercentage());
        assertEquals(0, dto2.getNumberOfCandidates());
        assertEquals(0, dto2.getNumberOfQuestions());
        assertEquals(0, dto2.getNumberOfFlags());
        assertEquals(0, dto2.getTotalCandidateMarks());
    }
}