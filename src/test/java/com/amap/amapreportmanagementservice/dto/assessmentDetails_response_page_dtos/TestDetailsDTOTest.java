package com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class TestDetailsDTOTest {

    @Test
    void testDetailsDTO_GetterSetter_ShouldWork() {
        TestDetailsDTO dto = new TestDetailsDTO();
        List<TestInfoDTO> testInfoList = Arrays.asList(new TestInfoDTO(), new TestInfoDTO());

        dto.setTestInfo(testInfoList);

        assertEquals(testInfoList, dto.getTestInfo());
    }

    @Test
    void testDetailsDTO_AllArgsConstructor_ShouldCreateObject() {
        List<TestInfoDTO> testInfoList = Arrays.asList(new TestInfoDTO(), new TestInfoDTO());
        TestDetailsDTO dto = new TestDetailsDTO(testInfoList);

        assertEquals(testInfoList, dto.getTestInfo());
    }

    @Test
    void testDetailsDTO_NoArgsConstructor_ShouldCreateObject() {
        TestDetailsDTO dto = new TestDetailsDTO();
        assertNotNull(dto);
    }

    @Test
    void testDetailsDTO_EqualsAndHashCode_ShouldWork() {
        List<TestInfoDTO> testInfoList1 = Arrays.asList(new TestInfoDTO());
        List<TestInfoDTO> testInfoList2 = Arrays.asList(new TestInfoDTO());
        List<TestInfoDTO> testInfoList3 = Arrays.asList(new TestInfoDTO(), new TestInfoDTO());

        TestDetailsDTO dto1 = new TestDetailsDTO(testInfoList1);
        TestDetailsDTO dto2 = new TestDetailsDTO(testInfoList2);
        TestDetailsDTO dto3 = new TestDetailsDTO(testInfoList3);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testDetailsDTO_ToString_ShouldNotReturnNull() {
        TestDetailsDTO dto = new TestDetailsDTO(Arrays.asList(new TestInfoDTO()));
        assertNotNull(dto.toString());
    }

    @Test
    void testDetailsDTO_NullList_ShouldWork() {
        TestDetailsDTO dto = new TestDetailsDTO();
        dto.setTestInfo(null);
        assertNull(dto.getTestInfo());

        TestDetailsDTO dto2 = new TestDetailsDTO(null);
        assertNull(dto2.getTestInfo());
    }
}