package com.amap.amapreportmanagementservice.dto;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class ResponseHandlerTest {

    @Test
    void testSuccessResponse_ShouldReturnCorrectResponseEntity() {
        Object data = "Success Data";
        HttpStatus httpStatus = HttpStatus.OK;

        ResponseEntity<Object> responseEntity = ResponseHandler.successResponse(httpStatus, data);

        assertEquals(httpStatus, responseEntity.getStatusCode());
        assertNotNull(responseEntity.getBody());

        @SuppressWarnings("unchecked")
        Map<String, Object> responseBody = (Map<String, Object>) responseEntity.getBody();

        assertTrue((Boolean) responseBody.get("success"));
        assertEquals(data, responseBody.get("data"));
        assertNull(responseBody.get("error"));
    }

    @Test
    void testErrorResponse_ShouldReturnCorrectResponseEntity() {
        Object errorData = "Error Data";
        HttpStatus httpStatus = HttpStatus.BAD_REQUEST;

        ResponseEntity<Object> responseEntity = ResponseHandler.errorResponse(httpStatus, errorData);

        assertEquals(httpStatus, responseEntity.getStatusCode());
        assertNotNull(responseEntity.getBody());

        @SuppressWarnings("unchecked")
        Map<String, Object> responseBody = (Map<String, Object>) responseEntity.getBody();

        assertFalse((Boolean) responseBody.get("success"));
        assertEquals(errorData, responseBody.get("error"));
        assertNull(responseBody.get("data"));
    }

    @Test
    void testResponse_PrivateMethod_ShouldReturnCorrectResponseEntity() {
        Object data = "Test Data";
        HttpStatus httpStatus = HttpStatus.ACCEPTED;
        boolean success = true;

        ResponseEntity<Object> responseEntity = invokePrivateResponseMethod(success, httpStatus, data);

        assertEquals(httpStatus, responseEntity.getStatusCode());
        assertNotNull(responseEntity.getBody());

        @SuppressWarnings("unchecked")
        Map<String, Object> responseBody = (Map<String, Object>) responseEntity.getBody();

        assertEquals(success, responseBody.get("success"));
        assertEquals(data, responseBody.get("data"));
        assertNull(responseBody.get("error"));

        success = false;
        data = "Error test data";

        responseEntity = invokePrivateResponseMethod(success, httpStatus, data);

        assertEquals(httpStatus, responseEntity.getStatusCode());
        assertNotNull(responseEntity.getBody());

        responseBody = (Map<String, Object>) responseEntity.getBody();

        assertEquals(success, responseBody.get("success"));
        assertEquals(data, responseBody.get("error"));
        assertNull(responseBody.get("data"));
    }

    private ResponseEntity<Object> invokePrivateResponseMethod(Boolean success, HttpStatus httpStatus, Object data) {
        try {
            java.lang.reflect.Method method = ResponseHandler.class.getDeclaredMethod("response", Boolean.class, HttpStatus.class, Object.class);
            method.setAccessible(true);
            return (ResponseEntity<Object>) method.invoke(null, success, httpStatus, data);
        } catch (Exception e) {
            fail("Failed to invoke private method: " + e.getMessage());
            return null; // unreachable, but needed for compilation
        }
    }

    @Test
    void testResponseHandler_GetterSetter_ShouldWork() {
        ResponseHandler handler = new ResponseHandler();
        HttpStatus status = HttpStatus.CREATED;
        Object data = "New Data";

        handler.setHttpStatus(status);
        handler.setData(data);

        assertEquals(status, handler.getHttpStatus());
        assertEquals(data, handler.getData());
    }

    @Test
    void testResponseHandler_AllArgsConstructor_ShouldCreateObject() {
        HttpStatus status = HttpStatus.OK;
        Object data = "Existing Data";
        ResponseHandler handler = new ResponseHandler(status, data);

        assertEquals(status, handler.getHttpStatus());
        assertEquals(data, handler.getData());
    }

    @Test
    void testResponseHandler_NoArgsConstructor_ShouldCreateObject() {
        ResponseHandler handler = new ResponseHandler();
        assertNotNull(handler);
    }

    @Test
    void testResponseHandler_EqualsAndHashCode_ShouldWork() {
        ResponseHandler handler1 = new ResponseHandler(HttpStatus.OK, "Data 1");
        ResponseHandler handler2 = new ResponseHandler(HttpStatus.OK, "Data 1");
        ResponseHandler handler3 = new ResponseHandler(HttpStatus.BAD_REQUEST, "Data 2");

        assertEquals(handler1, handler2);
        assertEquals(handler1.hashCode(), handler2.hashCode());
        assertNotEquals(handler1, handler3);
        assertNotEquals(handler1.hashCode(), handler3.hashCode());
    }

    @Test
    void testResponseHandler_ToString_ShouldNotReturnNull() {
        ResponseHandler handler = new ResponseHandler(HttpStatus.OK, "Data 1");
        assertNotNull(handler.toString());
    }
}