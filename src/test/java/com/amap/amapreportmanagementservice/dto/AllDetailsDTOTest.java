package com.amap.amapreportmanagementservice.dto;

import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.AssessmentsDetails;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateMetricsDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.ComparativeAnalysisCandidateInfo;
import com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221.GeneralDetailsDTO;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;


import static org.junit.jupiter.api.Assertions.*;

public class AllDetailsDTOTest {

    @Test
    void testAllDetailsDTO_GetterSetter_ShouldWork() {
        AllDetailsDTO dto = new AllDetailsDTO();
        GeneralDetailsDTO generalDetails = new GeneralDetailsDTO();
        AssessmentsDetails assessmentsDetails = new AssessmentsDetails();
        CandidateMetricsDTO candidateMetrics = new CandidateMetricsDTO();
        List<FeedbackResponseDTO> feedbacks = Arrays.asList(new FeedbackResponseDTO());
        List<ComparativeAnalysisCandidateInfo> totalComparativeAnalysis = Arrays.asList(new ComparativeAnalysisCandidateInfo());

        dto.setGeneralDetails(generalDetails);
        dto.setAssessmentsDetails(assessmentsDetails);
        dto.setCandidateMetrics(candidateMetrics);
        dto.setFeedbacks(feedbacks);
        dto.setTotalComparativeAnalysis(totalComparativeAnalysis);

        assertEquals(generalDetails, dto.getGeneralDetails());
        assertEquals(assessmentsDetails, dto.getAssessmentsDetails());
        assertEquals(candidateMetrics, dto.getCandidateMetrics());
        assertEquals(feedbacks, dto.getFeedbacks());
        assertEquals(totalComparativeAnalysis, dto.getTotalComparativeAnalysis());
    }

    @Test
    void testAllDetailsDTO_AllArgsConstructor_ShouldCreateObject() {
        GeneralDetailsDTO generalDetails = new GeneralDetailsDTO();
        AssessmentsDetails assessmentsDetails = new AssessmentsDetails();
        CandidateMetricsDTO candidateMetrics = new CandidateMetricsDTO();
        List<FeedbackResponseDTO> feedbacks = Arrays.asList(new FeedbackResponseDTO());
        List<ComparativeAnalysisCandidateInfo> totalComparativeAnalysis = Arrays.asList(new ComparativeAnalysisCandidateInfo());

        AllDetailsDTO dto = new AllDetailsDTO(generalDetails, assessmentsDetails, candidateMetrics, feedbacks, totalComparativeAnalysis);

        assertEquals(generalDetails, dto.getGeneralDetails());
        assertEquals(assessmentsDetails, dto.getAssessmentsDetails());
        assertEquals(candidateMetrics, dto.getCandidateMetrics());
        assertEquals(feedbacks, dto.getFeedbacks());
        assertEquals(totalComparativeAnalysis, dto.getTotalComparativeAnalysis());
    }

    @Test
    void testAllDetailsDTO_NoArgsConstructor_ShouldCreateObject() {
        AllDetailsDTO dto = new AllDetailsDTO();
        assertNotNull(dto);
    }

    @Test
    void testAllDetailsDTO_Builder_ShouldCreateObject() {
        GeneralDetailsDTO generalDetails = new GeneralDetailsDTO();
        AssessmentsDetails assessmentsDetails = new AssessmentsDetails();
        CandidateMetricsDTO candidateMetrics = new CandidateMetricsDTO();
        List<FeedbackResponseDTO> feedbacks = Arrays.asList(new FeedbackResponseDTO());
        List<ComparativeAnalysisCandidateInfo> totalComparativeAnalysis = Arrays.asList(new ComparativeAnalysisCandidateInfo());

        AllDetailsDTO dto = AllDetailsDTO.builder()
                .generalDetails(generalDetails)
                .assessmentsDetails(assessmentsDetails)
                .candidateMetrics(candidateMetrics)
                .feedbacks(feedbacks)
                .totalComparativeAnalysis(totalComparativeAnalysis)
                .build();

        assertEquals(generalDetails, dto.getGeneralDetails());
        assertEquals(assessmentsDetails, dto.getAssessmentsDetails());
        assertEquals(candidateMetrics, dto.getCandidateMetrics());
        assertEquals(feedbacks, dto.getFeedbacks());
        assertEquals(totalComparativeAnalysis, dto.getTotalComparativeAnalysis());
    }

    @Test
    void testAllDetailsDTO_EqualsAndHashCode_ShouldWork() {
        GeneralDetailsDTO generalDetails1 = new GeneralDetailsDTO();
        AssessmentsDetails assessmentsDetails1 = new AssessmentsDetails();
        CandidateMetricsDTO candidateMetrics1 = new CandidateMetricsDTO();
        List<FeedbackResponseDTO> feedbacks1 = Arrays.asList(new FeedbackResponseDTO());
        List<ComparativeAnalysisCandidateInfo> totalComparativeAnalysis1 = Arrays.asList(new ComparativeAnalysisCandidateInfo());

        GeneralDetailsDTO generalDetails2 = new GeneralDetailsDTO();
        AssessmentsDetails assessmentsDetails2 = new AssessmentsDetails();
        CandidateMetricsDTO candidateMetrics2 = new CandidateMetricsDTO();
        List<FeedbackResponseDTO> feedbacks2 = Arrays.asList(new FeedbackResponseDTO());
        List<ComparativeAnalysisCandidateInfo> totalComparativeAnalysis2 = Arrays.asList(new ComparativeAnalysisCandidateInfo());

        AllDetailsDTO dto1 = AllDetailsDTO.builder()
                .generalDetails(generalDetails1)
                .assessmentsDetails(assessmentsDetails1)
                .candidateMetrics(candidateMetrics1)
                .feedbacks(feedbacks1)
                .totalComparativeAnalysis(totalComparativeAnalysis1)
                .build();

        AllDetailsDTO dto2 = AllDetailsDTO.builder()
                .generalDetails(generalDetails2)
                .assessmentsDetails(assessmentsDetails2)
                .candidateMetrics(candidateMetrics2)
                .feedbacks(feedbacks2)
                .totalComparativeAnalysis(totalComparativeAnalysis2)
                .build();

        AllDetailsDTO dto3 = AllDetailsDTO.builder()
                .generalDetails(new GeneralDetailsDTO())
                .assessmentsDetails(new AssessmentsDetails())
                .candidateMetrics(new CandidateMetricsDTO())
                .feedbacks(Arrays.asList(new FeedbackResponseDTO()))
                .totalComparativeAnalysis(Arrays.asList(new ComparativeAnalysisCandidateInfo()))
                .build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testAllDetailsDTO_ToString_ShouldNotReturnNull() {
        AllDetailsDTO dto = AllDetailsDTO.builder().generalDetails(new GeneralDetailsDTO()).build();
        assertNotNull(dto.toString());
    }
}