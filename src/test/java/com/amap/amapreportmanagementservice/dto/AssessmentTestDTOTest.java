package com.amap.amapreportmanagementservice.dto;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class AssessmentTestDTOTest {

    @Test
    void testAssessmentTestDTO_GetterSetter_ShouldWork() {
        AssessmentTestDTO dto = new AssessmentTestDTO();
        List<TestDetailDTO> testDetails = Arrays.asList(new TestDetailDTO());

        dto.setAssessmentTitle("Assessment 1");
        dto.setTestDetails(testDetails);

        assertEquals("Assessment 1", dto.getAssessmentTitle());
        assertEquals(testDetails, dto.getTestDetails());
    }

    @Test
    void testAssessmentTestDTO_AllArgsConstructor_ShouldCreateObject() {
        List<TestDetailDTO> testDetails = Arrays.asList(new TestDetailDTO());
        AssessmentTestDTO dto = new AssessmentTestDTO("Assessment 1", testDetails);

        assertEquals("Assessment 1", dto.getAssessmentTitle());
        assertEquals(testDetails, dto.getTestDetails());
    }

    @Test
    void testAssessmentTestDTO_NoArgsConstructor_ShouldCreateObject() {
        AssessmentTestDTO dto = new AssessmentTestDTO();
        assertNotNull(dto);
    }

    @Test
    void testAssessmentTestDTO_EqualsAndHashCode_ShouldWork() {
        List<TestDetailDTO> testDetails1 = Arrays.asList(new TestDetailDTO("Test A"));
        List<TestDetailDTO> testDetails2 = Arrays.asList(new TestDetailDTO("Test A"));
        List<TestDetailDTO> testDetails3 = Arrays.asList(new TestDetailDTO("Test B"));

        AssessmentTestDTO dto1 = new AssessmentTestDTO("Assessment X", testDetails1);
        AssessmentTestDTO dto2 = new AssessmentTestDTO("Assessment X", testDetails2);
        AssessmentTestDTO dto3 = new AssessmentTestDTO("Assessment Y", testDetails3);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAssessmentTestDTO_ToString_ShouldNotReturnNull() {
        AssessmentTestDTO dto = new AssessmentTestDTO("Assessment X", Arrays.asList(new TestDetailDTO("Test A")));
        assertNotNull(dto.toString());
    }
}