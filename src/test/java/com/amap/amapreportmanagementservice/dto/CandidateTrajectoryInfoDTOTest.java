package com.amap.amapreportmanagementservice.dto;

import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateTrajectoryDTO;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CandidateTrajectoryInfoDTOTest {

    @Test
    void testCandidateTrajectoryInfoDTO_GetterSetter_ShouldWork() {
        CandidateTrajectoryInfoDTO dto = new CandidateTrajectoryInfoDTO();
        List<CandidateTrajectoryDTO> trajectory = Arrays.asList(new CandidateTrajectoryDTO());

        dto.setCandidateId("candidate123");
        dto.setCandidateEmail("<EMAIL>");
        dto.setCandidateTrajectory(trajectory);

        assertEquals("candidate123", dto.getCandidateId());
        assertEquals("<EMAIL>", dto.getCandidateEmail());
        assertEquals(trajectory, dto.getCandidateTrajectory());
    }

    @Test
    void testCandidateTrajectoryInfoDTO_AllArgsConstructor_ShouldCreateObject() {
        List<CandidateTrajectoryDTO> trajectory = Arrays.asList(new CandidateTrajectoryDTO());
        CandidateTrajectoryInfoDTO dto = new CandidateTrajectoryInfoDTO("candidate123", "<EMAIL>", trajectory);

        assertEquals("candidate123", dto.getCandidateId());
        assertEquals("<EMAIL>", dto.getCandidateEmail());
        assertEquals(trajectory, dto.getCandidateTrajectory());
    }

    @Test
    void testCandidateTrajectoryInfoDTO_NoArgsConstructor_ShouldCreateObject() {
        CandidateTrajectoryInfoDTO dto = new CandidateTrajectoryInfoDTO();
        assertNotNull(dto);
    }

    @Test
    void testCandidateTrajectoryInfoDTO_EqualsAndHashCode_ShouldWork() {
        List<CandidateTrajectoryDTO> trajectory1 = Arrays.asList(new CandidateTrajectoryDTO("A"));
        List<CandidateTrajectoryDTO> trajectory2 = Arrays.asList(new CandidateTrajectoryDTO("A"));
        List<CandidateTrajectoryDTO> trajectory3 = Arrays.asList(new CandidateTrajectoryDTO("B"));

        CandidateTrajectoryInfoDTO dto1 = new CandidateTrajectoryInfoDTO("1", "<EMAIL>", trajectory1);
        CandidateTrajectoryInfoDTO dto2 = new CandidateTrajectoryInfoDTO("1", "<EMAIL>", trajectory2);
        CandidateTrajectoryInfoDTO dto3 = new CandidateTrajectoryInfoDTO("2", "<EMAIL>", trajectory3);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testCandidateTrajectoryInfoDTO_ToString_ShouldNotReturnNull() {
        CandidateTrajectoryInfoDTO dto = new CandidateTrajectoryInfoDTO("1", "<EMAIL>", Arrays.asList(new CandidateTrajectoryDTO("A")));
        assertNotNull(dto.toString());
    }
}