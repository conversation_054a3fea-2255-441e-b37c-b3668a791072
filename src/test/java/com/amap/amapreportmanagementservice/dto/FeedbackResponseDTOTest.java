package com.amap.amapreportmanagementservice.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class FeedbackResponseDTOTest {

    @Test
    void testFeedbackResponseDTO_GetterSetter_ShouldWork() {
        FeedbackResponseDTO dto = new FeedbackResponseDTO();
        dto.setCandidateEmail("<EMAIL>");
        dto.setTestRating(4.5);
        dto.setOverallRating(4.0);
        dto.setComment("Good test");
        dto.setDate("2023-10-27");

        assertEquals("<EMAIL>", dto.getCandidateEmail());
        assertEquals(4.5, dto.getTestRating());
        assertEquals(4.0, dto.getOverallRating());
        assertEquals("Good test", dto.getComment());
        assertEquals("2023-10-27", dto.getDate());
    }

    @Test
    void testFeedbackResponseDTO_AllArgsConstructor_ShouldCreateObject() {
        FeedbackResponseDTO dto = new FeedbackResponseDTO("<EMAIL>", 4.5, 4.0, "Good test", "2023-10-27");

        assertEquals("<EMAIL>", dto.getCandidateEmail());
        assertEquals(4.5, dto.getTestRating());
        assertEquals(4.0, dto.getOverallRating());
        assertEquals("Good test", dto.getComment());
        assertEquals("2023-10-27", dto.getDate());
    }

    @Test
    void testFeedbackResponseDTO_NoArgsConstructor_ShouldCreateObject() {
        FeedbackResponseDTO dto = new FeedbackResponseDTO();
        assertNotNull(dto);
    }

    @Test
    void testFeedbackResponseDTO_EqualsAndHashCode_ShouldWork() {
        FeedbackResponseDTO dto1 = new FeedbackResponseDTO("<EMAIL>", 4.0, 3.5, "Comment A", "2023-10-26");
        FeedbackResponseDTO dto2 = new FeedbackResponseDTO("<EMAIL>", 4.0, 3.5, "Comment A", "2023-10-26");
        FeedbackResponseDTO dto3 = new FeedbackResponseDTO("<EMAIL>", 4.5, 4.0, "Comment B", "2023-10-27");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testFeedbackResponseDTO_ToString_ShouldNotReturnNull() {
        FeedbackResponseDTO dto = new FeedbackResponseDTO("<EMAIL>", 4.0, 3.5, "Comment A", "2023-10-26");
        assertNotNull(dto.toString());
    }
}