package com.amap.amapreportmanagementservice.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class AssessmentTestAverageDTOTest {

    @Test
    void testAssessmentTestAverageDTO_GetterSetter_ShouldWork() {
        AssessmentTestAverageDTO dto = new AssessmentTestAverageDTO();
        dto.setTestTitle("Test 1");
        dto.setAverageScore(85.5);

        assertEquals("Test 1", dto.getTestTitle());
        assertEquals(85.5, dto.getAverageScore());
    }

    @Test
    void testAssessmentTestAverageDTO_AllArgsConstructor_ShouldCreateObject() {
        AssessmentTestAverageDTO dto = new AssessmentTestAverageDTO("Test 1", 85.5);

        assertEquals("Test 1", dto.getTestTitle());
        assertEquals(85.5, dto.getAverageScore());
    }

    @Test
    void testAssessmentTestAverageDTO_NoArgsConstructor_ShouldCreateObject() {
        AssessmentTestAverageDTO dto = new AssessmentTestAverageDTO();
        assertNotNull(dto);
    }

    @Test
    void testAssessmentTestAverageDTO_EqualsAndHashCode_ShouldWork() {
        AssessmentTestAverageDTO dto1 = new AssessmentTestAverageDTO("Test A", 80.0);
        AssessmentTestAverageDTO dto2 = new AssessmentTestAverageDTO("Test A", 80.0);
        AssessmentTestAverageDTO dto3 = new AssessmentTestAverageDTO("Test B", 90.0);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAssessmentTestAverageDTO_ToString_ShouldNotReturnNull() {
        AssessmentTestAverageDTO dto = new AssessmentTestAverageDTO("Test A", 80.0);
        assertNotNull(dto.toString());
    }
}