package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ComparativeAnalysisCandidateInfoTest {

    @Test
    void comparativeAnalysisCandidateInfo_GetterSetter_ShouldWork() {
        ComparativeAnalysisCandidateInfo dto = new ComparativeAnalysisCandidateInfo();
        ComparativeTestAnalysisDTO analysis = new ComparativeTestAnalysisDTO();

        dto.setEmail("<EMAIL>");
        dto.setCandidateId("candidate123");
        dto.setCandidateComparativeAnalysis(analysis);

        assertEquals("<EMAIL>", dto.getEmail());
        assertEquals("candidate123", dto.getCandidateId());
        assertEquals(analysis, dto.getCandidateComparativeAnalysis());
    }

    @Test
    void comparativeAnalysisCandidateInfo_AllArgsConstructor_ShouldCreateObject() {
        ComparativeTestAnalysisDTO analysis = new ComparativeTestAnalysisDTO();
        ComparativeAnalysisCandidateInfo dto = new ComparativeAnalysisCandidateInfo(
                "<EMAIL>", "candidate123", analysis
        );

        assertEquals("<EMAIL>", dto.getEmail());
        assertEquals("candidate123", dto.getCandidateId());
        assertEquals(analysis, dto.getCandidateComparativeAnalysis());
    }

    @Test
    void comparativeAnalysisCandidateInfo_NoArgsConstructor_ShouldCreateObject() {
        ComparativeAnalysisCandidateInfo dto = new ComparativeAnalysisCandidateInfo();
        assertNotNull(dto);
    }

    @Test
    void comparativeAnalysisCandidateInfo_Builder_ShouldCreateObject() {
        ComparativeTestAnalysisDTO analysis = new ComparativeTestAnalysisDTO();
        ComparativeAnalysisCandidateInfo dto = ComparativeAnalysisCandidateInfo.builder()
                .email("<EMAIL>")
                .candidateId("candidate123")
                .candidateComparativeAnalysis(analysis)
                .build();

        assertEquals("<EMAIL>", dto.getEmail());
        assertEquals("candidate123", dto.getCandidateId());
        assertEquals(analysis, dto.getCandidateComparativeAnalysis());
    }

    @Test
    void comparativeAnalysisCandidateInfo_EqualsAndHashCode_ShouldWork() {
        ComparativeTestAnalysisDTO analysis1 = new ComparativeTestAnalysisDTO();
        ComparativeTestAnalysisDTO analysis2 = new ComparativeTestAnalysisDTO();
        ComparativeTestAnalysisDTO analysis3 = new ComparativeTestAnalysisDTO();

        ComparativeAnalysisCandidateInfo dto1 = new ComparativeAnalysisCandidateInfo(
                "<EMAIL>", "candidate123", analysis1
        );
        ComparativeAnalysisCandidateInfo dto2 = new ComparativeAnalysisCandidateInfo(
                "<EMAIL>", "candidate123", analysis2
        );
        ComparativeAnalysisCandidateInfo dto3 = new ComparativeAnalysisCandidateInfo(
                "<EMAIL>", "candidate123", analysis3
        );

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void comparativeAnalysisCandidateInfo_ToString_ShouldNotReturnNull() {
        ComparativeAnalysisCandidateInfo dto = new ComparativeAnalysisCandidateInfo(
                "<EMAIL>", "candidate123", new ComparativeTestAnalysisDTO()
        );
        assertNotNull(dto.toString());
    }

    @Test
    void comparativeAnalysisCandidateInfo_NullAnalysis_ShouldWork() {
        ComparativeAnalysisCandidateInfo dto = new ComparativeAnalysisCandidateInfo();
        dto.setCandidateComparativeAnalysis(null);
        assertNull(dto.getCandidateComparativeAnalysis());

        ComparativeAnalysisCandidateInfo dto2 = new ComparativeAnalysisCandidateInfo(
                "<EMAIL>", "candidate123", null
        );
        assertNull(dto2.getCandidateComparativeAnalysis());
    }
}