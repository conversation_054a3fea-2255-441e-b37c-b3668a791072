package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class CandidateTrajectoryDTOTest {

    @Test
    void candidateTrajectoryDTO_GetterSetter_ShouldWork() {
        CandidateTrajectoryDTO dto = new CandidateTrajectoryDTO();

        dto.setAssessmentTitle("Java Assessment");
        dto.setAssessmentScore(85.0);
        dto.setAssessmentStartTime("2024-01-01T10:00:00");

        assertEquals("Java Assessment", dto.getAssessmentTitle());
        assertEquals(85.0, dto.getAssessmentScore());
        assertEquals("2024-01-01T10:00:00", dto.getAssessmentStartTime());
    }

    @Test
    void candidateTrajectoryDTO_AllArgsConstructor_ShouldCreateObject() {
        CandidateTrajectoryDTO dto = new CandidateTrajectoryDTO(
                "Java Assessment", 85.0, "2024-01-01T10:00:00"
        );

        assertEquals("Java Assessment", dto.getAssessmentTitle());
        assertEquals(85.0, dto.getAssessmentScore());
        assertEquals("2024-01-01T10:00:00", dto.getAssessmentStartTime());
    }

    @Test
    void candidateTrajectoryDTO_NoArgsConstructor_ShouldCreateObject() {
        CandidateTrajectoryDTO dto = new CandidateTrajectoryDTO();
        assertNotNull(dto);
    }

    @Test
    void candidateTrajectoryDTO_Builder_ShouldCreateObject() {
        CandidateTrajectoryDTO dto = CandidateTrajectoryDTO.builder()
                .assessmentTitle("Java Assessment")
                .assessmentScore(85.0)
                .assessmentStartTime("2024-01-01T10:00:00")
                .build();

        assertEquals("Java Assessment", dto.getAssessmentTitle());
        assertEquals(85.0, dto.getAssessmentScore());
        assertEquals("2024-01-01T10:00:00", dto.getAssessmentStartTime());
    }

    @Test
    void candidateTrajectoryDTO_EqualsAndHashCode_ShouldWork() {
        CandidateTrajectoryDTO dto1 = new CandidateTrajectoryDTO(
                "Java Assessment", 85.0, "2024-01-01T10:00:00"
        );
        CandidateTrajectoryDTO dto2 = new CandidateTrajectoryDTO(
                "Java Assessment", 85.0, "2024-01-01T10:00:00"
        );
        CandidateTrajectoryDTO dto3 = new CandidateTrajectoryDTO(
                "Python Assessment", 85.0, "2024-01-01T10:00:00"
        );

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void candidateTrajectoryDTO_ToString_ShouldNotReturnNull() {
        CandidateTrajectoryDTO dto = new CandidateTrajectoryDTO(
                "Java Assessment", 85.0, "2024-01-01T10:00:00"
        );
        assertNotNull(dto.toString());
    }

    @Test
    void candidateTrajectoryDTO_NullValues_ShouldWork() {
        CandidateTrajectoryDTO dto = new CandidateTrajectoryDTO();
        dto.setAssessmentTitle(null);
        dto.setAssessmentStartTime(null);
        dto.setAssessmentScore(0);

        assertNull(dto.getAssessmentTitle());
        assertNull(dto.getAssessmentStartTime());
        assertEquals(0, dto.getAssessmentScore());

        CandidateTrajectoryDTO dto2 = new CandidateTrajectoryDTO(null, 0, null);

        assertNull(dto2.getAssessmentTitle());
        assertNull(dto2.getAssessmentStartTime());
        assertEquals(0, dto2.getAssessmentScore());
    }
}