package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import com.amap.amapreportmanagementservice.entity.CandidateQuestionResult;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class TestResultsDTOTest {

    @Test
    void testResultsDTO_GetterSetter_ShouldWork() {
        TestResultsDTO dto = new TestResultsDTO();
        List<CandidateQuestionResult> results = Arrays.asList(new CandidateQuestionResult());

        dto.setName("Math Test");
        dto.setQuestionsAnswered(15);
        dto.setTotalNumQuestions(20);
        dto.setTestTime(60);
        dto.setCandidateScore(80.0);
        dto.setOverallScore(100.0);
        dto.setPercentage(80.0);
        dto.setNumberOfQuestionsFailed(5);
        dto.setNumberOfQuestionsPassed(15);
        dto.setNumberOfQuestionsAnswered(15);
        dto.setNumberOfQuestions(20);
        dto.setTestWindowViolationDuration(30);
        dto.setTestWindowViolationCount(2);
        dto.setTestTakerShotCount(100);
        dto.setTestTakerViolationShotCount(5);
        dto.setTestWindowShotCount(50);
        dto.setTestWindowViolationShotCount(2);
        dto.setPassage("This is a passage.");
        dto.setStatus("Completed");
        dto.setPassStatus("Passed");
        dto.setDomain("Mathematics");
        dto.setQuestionResults(results);

        assertEquals("Math Test", dto.getName());
        assertEquals(15, dto.getQuestionsAnswered());
        assertEquals(20, dto.getTotalNumQuestions());
        assertEquals(60, dto.getTestTime());
        assertEquals(80.0, dto.getCandidateScore());
        assertEquals(100.0, dto.getOverallScore());
        assertEquals(80.0, dto.getPercentage());
        assertEquals(5, dto.getNumberOfQuestionsFailed());
        assertEquals(15, dto.getNumberOfQuestionsPassed());
        assertEquals(15, dto.getNumberOfQuestionsAnswered());
        assertEquals(20, dto.getNumberOfQuestions());
        assertEquals(30, dto.getTestWindowViolationDuration());
        assertEquals(2, dto.getTestWindowViolationCount());
        assertEquals(100, dto.getTestTakerShotCount());
        assertEquals(5, dto.getTestTakerViolationShotCount());
        assertEquals(50, dto.getTestWindowShotCount());
        assertEquals(2, dto.getTestWindowViolationShotCount());
        assertEquals("This is a passage.", dto.getPassage());
        assertEquals("Completed", dto.getStatus());
        assertEquals("Passed", dto.getPassStatus());
        assertEquals("Mathematics", dto.getDomain());
        assertEquals(results, dto.getQuestionResults());
    }

    @Test
    void testResultsDTO_AllArgsConstructor_ShouldCreateObject() {
        List<CandidateQuestionResult> results = Arrays.asList(new CandidateQuestionResult());
        TestResultsDTO dto = new TestResultsDTO(
                "Math Test", 15, 20, 60, 80.0, 100.0, 80.0, 5, 15, 15, 20, 30, 2, 100, 5, 50, 2, "This is a passage.", "Completed", "Passed", "Mathematics", results
        );

        assertEquals("Math Test", dto.getName());
        assertEquals(15, dto.getQuestionsAnswered());
        assertEquals(20, dto.getTotalNumQuestions());
        assertEquals(60, dto.getTestTime());
        assertEquals(80.0, dto.getCandidateScore());
        assertEquals(100.0, dto.getOverallScore());
        assertEquals(80.0, dto.getPercentage());
        assertEquals(5, dto.getNumberOfQuestionsFailed());
        assertEquals(15, dto.getNumberOfQuestionsPassed());
        assertEquals(15, dto.getNumberOfQuestionsAnswered());
        assertEquals(20, dto.getNumberOfQuestions());
        assertEquals(30, dto.getTestWindowViolationDuration());
        assertEquals(2, dto.getTestWindowViolationCount());
        assertEquals(100, dto.getTestTakerShotCount());
        assertEquals(5, dto.getTestTakerViolationShotCount());
        assertEquals(50, dto.getTestWindowShotCount());
        assertEquals(2, dto.getTestWindowViolationShotCount());
        assertEquals("This is a passage.", dto.getPassage());
        assertEquals("Completed", dto.getStatus());
        assertEquals("Passed", dto.getPassStatus());
        assertEquals("Mathematics", dto.getDomain());
        assertEquals(results, dto.getQuestionResults());
    }

    @Test
    void testResultsDTO_NoArgsConstructor_ShouldCreateObject() {
        TestResultsDTO dto = new TestResultsDTO();
        assertNotNull(dto);
    }

    @Test
    void testResultsDTO_Builder_ShouldCreateObject() {
        List<CandidateQuestionResult> results = Arrays.asList(new CandidateQuestionResult());
        TestResultsDTO dto = TestResultsDTO.builder()
                .name("Math Test")
                .questionsAnswered(15)
                .totalNumQuestions(20)
                .testTime(60)
                .candidateScore(80.0)
                .questionResults(results)
                .build();

        assertEquals("Math Test", dto.getName());
        assertEquals(15, dto.getQuestionsAnswered());
        assertEquals(20, dto.getTotalNumQuestions());
        assertEquals(60, dto.getTestTime());
        assertEquals(80.0, dto.getCandidateScore());
        assertEquals(results, dto.getQuestionResults());
    }

    @Test
    void testResultsDTO_EqualsAndHashCode_ShouldWork() {
        List<CandidateQuestionResult> results = Arrays.asList(new CandidateQuestionResult());
        TestResultsDTO dto1 = new TestResultsDTO(
                "Math Test", 15, 20, 60, 80.0, 100.0, 80.0, 5, 15, 15, 20, 30, 2, 100, 5, 50, 2, "This is a passage.", "Completed", "Passed", "Mathematics", results
        );
        TestResultsDTO dto2 = new TestResultsDTO(
                "Math Test", 15, 20, 60, 80.0, 100.0, 80.0, 5, 15, 15, 20, 30, 2, 100, 5, 50, 2, "This is a passage.", "Completed", "Passed", "Mathematics", results
        );
        TestResultsDTO dto3 = new TestResultsDTO(
                "Science Test", 15, 20, 60, 80.0, 100.0, 80.0, 5, 15, 15, 20, 30, 2, 100, 5, 50, 2, "This is a passage.", "Completed", "Passed", "Mathematics", results
        );

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testResultsDTO_ToString_ShouldNotReturnNull() {
        List<CandidateQuestionResult> results = Arrays.asList(new CandidateQuestionResult());
        TestResultsDTO dto = new TestResultsDTO(
                "Math Test", 15, 20, 60, 80.0, 100.0, 80.0, 5, 15, 15, 20, 30, 2, 100, 5, 50, 2, "This is a passage.", "Completed", "Passed", "Mathematics", results
        );
        assertNotNull(dto.toString());
    }

    @Test
    void testResultsDTO_NullList_ShouldWork() {
        TestResultsDTO dto = new TestResultsDTO();
        dto.setQuestionResults(null);
        assertNull(dto.getQuestionResults());
    }
}