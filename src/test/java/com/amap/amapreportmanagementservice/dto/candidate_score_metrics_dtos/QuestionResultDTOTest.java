package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import org.junit.jupiter.api.Test;
import com.amap.amapreportmanagementservice.dto.progress.CodeExecutionSummaryDTO;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class QuestionResultDTOTest {

    @Test
    void questionResultDTO_GetterSetter_ShouldWork() {
        QuestionResultDTO dto = new QuestionResultDTO();
        List<String> answers = Arrays.asList("answer1", "answer2");

        dto.setCandidateId("candidate123");
        dto.setQuestionId("question456");
        dto.setCandidateEmail("<EMAIL>");
        dto.setQuestionText("What is the capital?");
        dto.setQuestionType("Multiple Choice");
        dto.setTotalScore(10);
        dto.setCandidateMarks(8);
        dto.setDifficultyLevel("Medium");
        dto.setTimeLimit(60);
        dto.setIsFlagged("Yes");
        dto.setTestTakerAnswers(answers);
        dto.setComprehension(true);
        dto.setIsAnswerCorrect("CORRECT");

        assertEquals("candidate123", dto.getCandidateId());
        assertEquals("question456", dto.getQuestionId());
        assertEquals("<EMAIL>", dto.getCandidateEmail());
        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals("Multiple Choice", dto.getQuestionType());
        assertEquals(10, dto.getTotalScore());
        assertEquals(8, dto.getCandidateMarks());
        assertEquals("Medium", dto.getDifficultyLevel());
        assertEquals(60, dto.getTimeLimit());
        assertEquals("Yes", dto.getIsFlagged());
        assertEquals(answers, dto.getTestTakerAnswers());
        assertTrue(dto.isComprehension());
        assertEquals("CORRECT",(dto.getIsAnswerCorrect()));
    }

    @Test
    void questionResultDTO_AllArgsConstructor_ShouldCreateObject() {
        List<String> answers = Arrays.asList("answer1", "answer2");
        CodeExecutionSummaryDTO codeExecutionSummary = new CodeExecutionSummaryDTO();


        QuestionResultDTO dto = new QuestionResultDTO(
                "candidate123", "question456", "<EMAIL>", "What is the capital?", "Multiple Choice",
                10, 8, "Medium", 60, "Yes", answers, codeExecutionSummary, true, "CORRECT"
        );

        assertEquals("candidate123", dto.getCandidateId());
        assertEquals("question456", dto.getQuestionId());
        assertEquals("<EMAIL>", dto.getCandidateEmail());
        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals("Multiple Choice", dto.getQuestionType());
        assertEquals(10, dto.getTotalScore());
        assertEquals(8, dto.getCandidateMarks());
        assertEquals("Medium", dto.getDifficultyLevel());
        assertEquals(60, dto.getTimeLimit());
        assertEquals("Yes", dto.getIsFlagged());
        assertEquals(answers, dto.getTestTakerAnswers());
        assertEquals(codeExecutionSummary, dto.getCodeExecutionSummary());
        assertTrue(dto.isComprehension());
        assertEquals("CORRECT",(dto.getIsAnswerCorrect()));
    }
    @Test
    void questionResultDTO_NoArgsConstructor_ShouldCreateObject() {
        QuestionResultDTO dto = new QuestionResultDTO();
        assertNotNull(dto);
    }

    @Test
    void questionResultDTO_Builder_ShouldCreateObject() {
        List<String> answers = Arrays.asList("answer1", "answer2");
        QuestionResultDTO dto = QuestionResultDTO.builder()
                .candidateId("candidate123")
                .questionId("question456")
                .candidateEmail("<EMAIL>")
                .questionText("What is the capital?")
                .questionType("Multiple Choice")
                .totalScore(10)
                .candidateMarks(8)
                .difficultyLevel("Medium")
                .timeLimit(60)
                .isFlagged("Yes")
                .TestTakerAnswers(answers)
                .isComprehension(true)
                .isAnswerCorrect("CORRECT")
                .build();

        assertEquals("candidate123", dto.getCandidateId());
        assertEquals("question456", dto.getQuestionId());
        assertEquals("<EMAIL>", dto.getCandidateEmail());
        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals("Multiple Choice", dto.getQuestionType());
        assertEquals(10, dto.getTotalScore());
        assertEquals(8, dto.getCandidateMarks());
        assertEquals("Medium", dto.getDifficultyLevel());
        assertEquals(60, dto.getTimeLimit());
        assertEquals("Yes", dto.getIsFlagged());
        assertEquals(answers, dto.getTestTakerAnswers());
        assertTrue(dto.isComprehension());
        assertEquals("CORRECT",(dto.getIsAnswerCorrect()));
    }

    @Test
    void questionResultDTO_EqualsAndHashCode_ShouldWork() {
        List<String> answers = Arrays.asList("answer1", "answer2");
        CodeExecutionSummaryDTO codeExecutionSummary = new CodeExecutionSummaryDTO();
        QuestionResultDTO dto1 = new QuestionResultDTO(
                "candidate123", "question456", "<EMAIL>", "What is the capital?", "Multiple Choice",
                10, 8, "Medium", 60, "Yes", answers,codeExecutionSummary ,true, "CORRECT"
        );
        QuestionResultDTO dto2 = new QuestionResultDTO(
                "candidate123", "question456", "<EMAIL>", "What is the capital?", "Multiple Choice",
                10, 8, "Medium", 60, "Yes", answers,codeExecutionSummary, true, "CORRECT"
        );
        QuestionResultDTO dto3 = new QuestionResultDTO(
                "candidate456", "question456", "<EMAIL>", "What is the capital?", "Multiple Choice",
                10, 8, "Medium", 60, "Yes", answers, codeExecutionSummary,true, "CORRECT"
        );

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void questionResultDTO_ToString_ShouldNotReturnNull() {
        List<String> answers = Arrays.asList("answer1", "answer2");
        CodeExecutionSummaryDTO codeExecutionSummary = new CodeExecutionSummaryDTO();
        QuestionResultDTO dto = new QuestionResultDTO(
                "candidate123", "question456", "<EMAIL>", "What is the capital?", "Multiple Choice",
                10, 8, "Medium", 60, "Yes", answers,codeExecutionSummary, true, "CORRECT"
        );
        assertNotNull(dto.toString());
    }

    @Test
    void questionResultDTO_NullList_ShouldWork() {
        QuestionResultDTO dto = new QuestionResultDTO();
        dto.setTestTakerAnswers(null);
        assertNull(dto.getTestTakerAnswers());

        Map<String, Object> codeExecutionSummary = new HashMap<>();
        codeExecutionSummary.put("compilationSuccess", true);
        codeExecutionSummary.put("executionTime", 120);
        QuestionResultDTO dto2 = new QuestionResultDTO(
                "candidate123", "question456", "<EMAIL>", "What is the capital?", "Multiple Choice",
                10, 8, "Medium", 60, "Yes", null,null, true, "CORRECT"
        );
        assertNull(dto2.getTestTakerAnswers());
    }

    @Test
    void questionResultDTO_BooleanFalse_ShouldWork() {
        QuestionResultDTO dto = new QuestionResultDTO();
        dto.setComprehension(false);
        dto.setIsAnswerCorrect("WRONG");
        assertFalse(dto.isComprehension());
        assertEquals("WRONG", dto.getIsAnswerCorrect());
        CodeExecutionSummaryDTO codeExecutionSummary = new CodeExecutionSummaryDTO();
        QuestionResultDTO dto2 = new QuestionResultDTO(
                "candidate123", "question456", "<EMAIL>", "What is the capital?", "Multiple Choice",
                10, 8, "Medium", 60, "Yes" ,Arrays.asList("answer1"), codeExecutionSummary,false, "WRONG"
        );
        assertFalse(dto2.isComprehension());
        assertEquals("WRONG",(dto2.getIsAnswerCorrect()));
    }
}