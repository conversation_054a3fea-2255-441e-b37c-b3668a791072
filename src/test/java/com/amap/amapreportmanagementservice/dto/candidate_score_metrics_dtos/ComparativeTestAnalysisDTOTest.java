package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class ComparativeTestAnalysisDTOTest {

    @Test
    void comparativeTestAnalysisDTO_GetterSetter_ShouldWork() {
        ComparativeTestAnalysisDTO dto = new ComparativeTestAnalysisDTO();
        List<ComparativeAnalysisDTO> comparativeAnalysis = Arrays.asList(new ComparativeAnalysisDTO());
        List<CandidateTrajectoryDTO> candidateTrajectory = Arrays.asList(new CandidateTrajectoryDTO());
        CandidateFeedbackDTO candidateFeedback = new CandidateFeedbackDTO();

        dto.setComparativeAnalysis(comparativeAnalysis);
        dto.setCandidateTrajectory(candidateTrajectory);
        dto.setCandidateFeedback(candidateFeedback);

        assertEquals(comparativeAnalysis, dto.getComparativeAnalysis());
        assertEquals(candidateTrajectory, dto.getCandidateTrajectory());
        assertEquals(candidateFeedback, dto.getCandidateFeedback());
    }

    @Test
    void comparativeTestAnalysisDTO_AllArgsConstructor_ShouldCreateObject() {
        List<ComparativeAnalysisDTO> comparativeAnalysis = Arrays.asList(new ComparativeAnalysisDTO());
        List<CandidateTrajectoryDTO> candidateTrajectory = Arrays.asList(new CandidateTrajectoryDTO());
        CandidateFeedbackDTO candidateFeedback = new CandidateFeedbackDTO();

        ComparativeTestAnalysisDTO dto = new ComparativeTestAnalysisDTO(
                comparativeAnalysis, candidateTrajectory, candidateFeedback
        );

        assertEquals(comparativeAnalysis, dto.getComparativeAnalysis());
        assertEquals(candidateTrajectory, dto.getCandidateTrajectory());
        assertEquals(candidateFeedback, dto.getCandidateFeedback());
    }

    @Test
    void comparativeTestAnalysisDTO_NoArgsConstructor_ShouldCreateObject() {
        ComparativeTestAnalysisDTO dto = new ComparativeTestAnalysisDTO();
        assertNotNull(dto);
    }

    @Test
    void comparativeTestAnalysisDTO_EqualsAndHashCode_ShouldWork() {
        List<ComparativeAnalysisDTO> analysisList1 = Arrays.asList(new ComparativeAnalysisDTO());
        List<ComparativeAnalysisDTO> analysisList2 = Arrays.asList(new ComparativeAnalysisDTO());
        List<ComparativeAnalysisDTO> analysisList3 = Arrays.asList(new ComparativeAnalysisDTO(), new ComparativeAnalysisDTO());

        List<CandidateTrajectoryDTO> trajectoryList1 = Arrays.asList(new CandidateTrajectoryDTO());
        List<CandidateTrajectoryDTO> trajectoryList2 = Arrays.asList(new CandidateTrajectoryDTO());
        List<CandidateTrajectoryDTO> trajectoryList3 = Arrays.asList(new CandidateTrajectoryDTO(), new CandidateTrajectoryDTO());

        CandidateFeedbackDTO feedback1 = new CandidateFeedbackDTO();
        CandidateFeedbackDTO feedback2 = new CandidateFeedbackDTO();
        CandidateFeedbackDTO feedback3 = new CandidateFeedbackDTO(Arrays.asList());

        ComparativeTestAnalysisDTO dto1 = new ComparativeTestAnalysisDTO(analysisList1, trajectoryList1, feedback1);
        ComparativeTestAnalysisDTO dto2 = new ComparativeTestAnalysisDTO(analysisList2, trajectoryList2, feedback2);
        ComparativeTestAnalysisDTO dto3 = new ComparativeTestAnalysisDTO(analysisList3, trajectoryList3, feedback3);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void comparativeTestAnalysisDTO_ToString_ShouldNotReturnNull() {
        ComparativeTestAnalysisDTO dto = new ComparativeTestAnalysisDTO(
                Arrays.asList(new ComparativeAnalysisDTO()), Arrays.asList(new CandidateTrajectoryDTO()), new CandidateFeedbackDTO()
        );
        assertNotNull(dto.toString());
    }

    @Test
    void comparativeTestAnalysisDTO_NullListsAndFeedback_ShouldWork() {
        ComparativeTestAnalysisDTO dto = new ComparativeTestAnalysisDTO();
        dto.setComparativeAnalysis(null);
        dto.setCandidateTrajectory(null);
        dto.setCandidateFeedback(null);

        assertNull(dto.getComparativeAnalysis());
        assertNull(dto.getCandidateTrajectory());
        assertNull(dto.getCandidateFeedback());

        ComparativeTestAnalysisDTO dto2 = new ComparativeTestAnalysisDTO(null, null, null);

        assertNull(dto2.getComparativeAnalysis());
        assertNull(dto2.getCandidateTrajectory());
        assertNull(dto2.getCandidateFeedback());
    }
}