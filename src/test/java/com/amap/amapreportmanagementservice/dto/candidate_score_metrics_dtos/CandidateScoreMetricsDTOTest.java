package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class CandidateScoreMetricsDTOTest {

    @Test
    void candidateScoreMetricsDTO_GetterSetter_ShouldWork() {
        CandidateScoreMetricsDTO dto = new CandidateScoreMetricsDTO();

        dto.setCandidateId("candidate123");
        dto.setCandidateEmail("<EMAIL>");
        dto.setCandidateMarks(85.0);
        dto.setIntegrityScore(90);
        dto.setScorePercentage(85.0);
        dto.setRiskLevel("Low");
        dto.setProctoringLevel("High");
        dto.setAssessmentWindowViolationCount(2);
        dto.setAssessmentWindowViolationDuration(30);
        dto.setAssessmentTakerShotCount(100);
        dto.setAssessmentTakerViolationShotCount(5);
        dto.setWindowShotCount(50);
        dto.setWindowViolationShotCount(2);
        dto.setScreenshotsInterval(10);
        dto.setCamerashotsInterval(15);

        assertEquals("candidate123", dto.getCandidateId());
        assertEquals("<EMAIL>", dto.getCandidateEmail());
        assertEquals(85.0, dto.getCandidateMarks());
        assertEquals(90, dto.getIntegrityScore());
        assertEquals(85.0, dto.getScorePercentage());
        assertEquals("Low", dto.getRiskLevel());
        assertEquals("High", dto.getProctoringLevel());
        assertEquals(2, dto.getAssessmentWindowViolationCount());
        assertEquals(30, dto.getAssessmentWindowViolationDuration());
        assertEquals(100, dto.getAssessmentTakerShotCount());
        assertEquals(5, dto.getAssessmentTakerViolationShotCount());
        assertEquals(50, dto.getWindowShotCount());
        assertEquals(2, dto.getWindowViolationShotCount());
        assertEquals(10, dto.getScreenshotsInterval());
        assertEquals(15, dto.getCamerashotsInterval());
    }

    @Test
    void candidateScoreMetricsDTO_AllArgsConstructor_ShouldCreateObject() {
        CandidateScoreMetricsDTO dto = new CandidateScoreMetricsDTO(
                "candidate123", "<EMAIL>", 85.0, 90, 85.0, "Low", "High", 2, 30, 100, 5, 50, 2, 10, 15
        );

        assertEquals("candidate123", dto.getCandidateId());
        assertEquals("<EMAIL>", dto.getCandidateEmail());
        assertEquals(85.0, dto.getCandidateMarks());
        assertEquals(90, dto.getIntegrityScore());
        assertEquals(85.0, dto.getScorePercentage());
        assertEquals("Low", dto.getRiskLevel());
        assertEquals("High", dto.getProctoringLevel());
        assertEquals(2, dto.getAssessmentWindowViolationCount());
        assertEquals(30, dto.getAssessmentWindowViolationDuration());
        assertEquals(100, dto.getAssessmentTakerShotCount());
        assertEquals(5, dto.getAssessmentTakerViolationShotCount());
        assertEquals(50, dto.getWindowShotCount());
        assertEquals(2, dto.getWindowViolationShotCount());
        assertEquals(10, dto.getScreenshotsInterval());
        assertEquals(15, dto.getCamerashotsInterval());
    }

    @Test
    void candidateScoreMetricsDTO_NoArgsConstructor_ShouldCreateObject() {
        CandidateScoreMetricsDTO dto = new CandidateScoreMetricsDTO();
        assertNotNull(dto);
    }

    @Test
    void candidateScoreMetricsDTO_Builder_ShouldCreateObject() {
        CandidateScoreMetricsDTO dto = CandidateScoreMetricsDTO.builder()
                .candidateId("candidate123")
                .candidateEmail("<EMAIL>")
                .candidateMarks(85.0)
                .build();

        assertEquals("candidate123", dto.getCandidateId());
        assertEquals("<EMAIL>", dto.getCandidateEmail());
        assertEquals(85.0, dto.getCandidateMarks());
    }

    @Test
    void candidateScoreMetricsDTO_EqualsAndHashCode_ShouldWork() {
        CandidateScoreMetricsDTO dto1 = new CandidateScoreMetricsDTO(
                "candidate123", "<EMAIL>", 85.0, 90, 85.0, "Low", "High", 2, 30, 100, 5, 50, 2, 10, 15
        );
        CandidateScoreMetricsDTO dto2 = new CandidateScoreMetricsDTO(
                "candidate123", "<EMAIL>", 85.0, 90, 85.0, "Low", "High", 2, 30, 100, 5, 50, 2, 10, 15
        );
        CandidateScoreMetricsDTO dto3 = new CandidateScoreMetricsDTO(
                "candidate456", "<EMAIL>", 85.0, 90, 85.0, "Low", "High", 2, 30, 100, 5, 50, 2, 10, 15
        );

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void candidateScoreMetricsDTO_ToString_ShouldNotReturnNull() {
        CandidateScoreMetricsDTO dto = new CandidateScoreMetricsDTO(
                "candidate123", "<EMAIL>", 85.0, 90, 85.0, "Low", "High", 2, 30, 100, 5, 50, 2, 10, 15
        );
        assertNotNull(dto.toString());
    }
}