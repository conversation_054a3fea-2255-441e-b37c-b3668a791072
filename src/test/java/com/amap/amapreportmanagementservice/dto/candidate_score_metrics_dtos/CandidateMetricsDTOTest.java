package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CandidateMetricsDTOTest {

    @Test
    void candidateMetricsDTO_GetterSetter_ShouldWork() {
        CandidateMetricsDTO dto = new CandidateMetricsDTO();
        List<CandidateScoreMetricsDTO> metrics = Arrays.asList(new CandidateScoreMetricsDTO(), new CandidateScoreMetricsDTO());

        dto.setCandidateScoreMetrics(metrics);

        assertEquals(metrics, dto.getCandidateScoreMetrics());
    }

    @Test
    void candidateMetricsDTO_AllArgsConstructor_ShouldCreateObject() {
        List<CandidateScoreMetricsDTO> metrics = Arrays.asList(new CandidateScoreMetricsDTO(), new CandidateScoreMetricsDTO());
        CandidateMetricsDTO dto = new CandidateMetricsDTO(metrics);

        assertEquals(metrics, dto.getCandidateScoreMetrics());
    }

    @Test
    void candidateMetricsDTO_NoArgsConstructor_ShouldCreateObject() {
        CandidateMetricsDTO dto = new CandidateMetricsDTO();
        assertNotNull(dto);
    }

    @Test
    void candidateMetricsDTO_Builder_ShouldCreateObject() {
        List<CandidateScoreMetricsDTO> metrics = Arrays.asList(new CandidateScoreMetricsDTO());
        CandidateMetricsDTO dto = CandidateMetricsDTO.builder()
                .candidateScoreMetrics(metrics)
                .build();

        assertEquals(metrics, dto.getCandidateScoreMetrics());
    }

    @Test
    void candidateMetricsDTO_EqualsAndHashCode_ShouldWork() {
        List<CandidateScoreMetricsDTO> metrics1 = Arrays.asList(new CandidateScoreMetricsDTO());
        List<CandidateScoreMetricsDTO> metrics2 = Arrays.asList(new CandidateScoreMetricsDTO());
        List<CandidateScoreMetricsDTO> metrics3 = Arrays.asList(new CandidateScoreMetricsDTO(), new CandidateScoreMetricsDTO());

        CandidateMetricsDTO dto1 = new CandidateMetricsDTO(metrics1);
        CandidateMetricsDTO dto2 = new CandidateMetricsDTO(metrics2);
        CandidateMetricsDTO dto3 = new CandidateMetricsDTO(metrics3);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void candidateMetricsDTO_ToString_ShouldNotReturnNull() {
        CandidateMetricsDTO dto = new CandidateMetricsDTO(Arrays.asList(new CandidateScoreMetricsDTO()));
        assertNotNull(dto.toString());
    }

    @Test
    void candidateMetricsDTO_NullList_ShouldWork() {
        CandidateMetricsDTO dto = new CandidateMetricsDTO();
        dto.setCandidateScoreMetrics(null);
        assertNull(dto.getCandidateScoreMetrics());

        CandidateMetricsDTO dto2 = new CandidateMetricsDTO(null);
        assertNull(dto2.getCandidateScoreMetrics());
    }
}