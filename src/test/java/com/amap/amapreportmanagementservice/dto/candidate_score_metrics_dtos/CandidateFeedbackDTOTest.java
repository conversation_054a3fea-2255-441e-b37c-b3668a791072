package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import com.amap.amapreportmanagementservice.entity.Feedback;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CandidateFeedbackDTOTest {

    @Test
    void candidateFeedbackDTO_GetterSetter_ShouldWork() {
        CandidateFeedbackDTO dto = new CandidateFeedbackDTO();
        List<Feedback> feedbacks = Arrays.asList(new Feedback(), new Feedback());

        dto.setFeedbacks(feedbacks);

        assertEquals(feedbacks, dto.getFeedbacks());
    }

    @Test
    void candidateFeedbackDTO_AllArgsConstructor_ShouldCreateObject() {
        List<Feedback> feedbacks = Arrays.asList(new Feedback(), new Feedback());
        CandidateFeedbackDTO dto = new CandidateFeedbackDTO(feedbacks);

        assertEquals(feedbacks, dto.getFeedbacks());
    }

    @Test
    void candidateFeedbackDTO_NoArgsConstructor_ShouldCreateObject() {
        CandidateFeedbackDTO dto = new CandidateFeedbackDTO();
        assertNotNull(dto);
    }

    @Test
    void candidateFeedbackDTO_EqualsAndHashCode_ShouldWork() {
        List<Feedback> feedbacks1 = Arrays.asList(new Feedback());
        List<Feedback> feedbacks2 = Arrays.asList(new Feedback());
        List<Feedback> feedbacks3 = Arrays.asList(new Feedback(), new Feedback());

        CandidateFeedbackDTO dto1 = new CandidateFeedbackDTO(feedbacks1);
        CandidateFeedbackDTO dto2 = new CandidateFeedbackDTO(feedbacks2);
        CandidateFeedbackDTO dto3 = new CandidateFeedbackDTO(feedbacks3);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void candidateFeedbackDTO_ToString_ShouldNotReturnNull() {
        CandidateFeedbackDTO dto = new CandidateFeedbackDTO(Arrays.asList(new Feedback()));
        assertNotNull(dto.toString());
    }

    @Test
    void candidateFeedbackDTO_NullList_ShouldWork() {
        CandidateFeedbackDTO dto = new CandidateFeedbackDTO();
        dto.setFeedbacks(null);
        assertNull(dto.getFeedbacks());

        CandidateFeedbackDTO dto2 = new CandidateFeedbackDTO(null);
        assertNull(dto2.getFeedbacks());
    }
}