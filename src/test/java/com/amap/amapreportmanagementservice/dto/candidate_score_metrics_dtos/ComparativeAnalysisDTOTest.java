package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ComparativeAnalysisDTOTest {

    @Test
    void comparativeAnalysisDTO_GetterSetter_ShouldWork() {
        ComparativeAnalysisDTO dto = new ComparativeAnalysisDTO();

        dto.setTestName("Math Test");
        dto.setTestAverageScore(80.5);
        dto.setCandidateScore(90.0);

        assertEquals("Math Test", dto.getTestName());
        assertEquals(80.5, dto.getTestAverageScore());
        assertEquals(90.0, dto.getCandidateScore());
    }

    @Test
    void comparativeAnalysisDTO_AllArgsConstructor_ShouldCreateObject() {
        ComparativeAnalysisDTO dto = new ComparativeAnalysisDTO("Math Test", 80.5, 90.0);

        assertEquals("Math Test", dto.getTestName());
        assertEquals(80.5, dto.getTestAverageScore());
        assertEquals(90.0, dto.getCandidateScore());
    }

    @Test
    void comparativeAnalysisDTO_NoArgsConstructor_ShouldCreateObject() {
        ComparativeAnalysisDTO dto = new ComparativeAnalysisDTO();
        assertNotNull(dto);
    }

    @Test
    void comparativeAnalysisDTO_EqualsAndHashCode_ShouldWork() {
        ComparativeAnalysisDTO dto1 = new ComparativeAnalysisDTO("Math Test", 80.5, 90.0);
        ComparativeAnalysisDTO dto2 = new ComparativeAnalysisDTO("Math Test", 80.5, 90.0);
        ComparativeAnalysisDTO dto3 = new ComparativeAnalysisDTO("Science Test", 80.5, 90.0);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void comparativeAnalysisDTO_ToString_ShouldNotReturnNull() {
        ComparativeAnalysisDTO dto = new ComparativeAnalysisDTO("Math Test", 80.5, 90.0);
        assertNotNull(dto.toString());
    }

    @Test
    void comparativeAnalysisDTO_NullValues_ShouldWork() {
        ComparativeAnalysisDTO dto = new ComparativeAnalysisDTO();
        dto.setTestName(null);
        dto.setTestAverageScore(0);
        dto.setCandidateScore(0);

        assertNull(dto.getTestName());
        assertEquals(0, dto.getTestAverageScore());
        assertEquals(0, dto.getCandidateScore());

        ComparativeAnalysisDTO dto2 = new ComparativeAnalysisDTO(null, 0, 0);

        assertNull(dto2.getTestName());
        assertEquals(0, dto2.getTestAverageScore());
        assertEquals(0, dto2.getCandidateScore());
    }
}