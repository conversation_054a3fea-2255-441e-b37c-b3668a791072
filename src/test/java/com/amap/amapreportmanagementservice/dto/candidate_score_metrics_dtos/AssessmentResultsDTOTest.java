package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import com.amap.amapreportmanagementservice.dto.FeedbackResponseDTO;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class AssessmentResultsDTOTest {

    @Test
    void assessmentResultsDTO_GetterSetter_ShouldWork() {
        AssessmentResultsDTO dto = new AssessmentResultsDTO();

        dto.setAssessmentTitle("Java Assessment");
        dto.setStatus("Completed");
        dto.setProctor("John Doe");
        dto.setProctorLevel("Senior");
        dto.setCommenceDate("2024-01-01");
        dto.setExpireDate("2024-12-31");
        dto.setAssessmentEndTime("2024-01-01T11:00:00");
        dto.setAssessmentStartTime("2024-01-01T10:00:00");
        dto.setAssessmentTime(120);
        dto.setAssessmentTimeTaken(60);
        dto.setAssessmentCandidateScore(85.0);
        dto.setAssessmentOverallScore(100.0);
        dto.setAssessmentCandidatePercentage(85.0);
        dto.setAssessmentWindowViolationCount(2);
        dto.setAssessmentWindowViolationDuration(30);
        dto.setAssessmentTakerShotCount(100);
        dto.setAssessmentTakerViolationShotCount(5);
        dto.setWindowShotCount(50);
        dto.setWindowViolationShotCount(2);
        dto.setScreenshotsInterval(10);
        dto.setIntegrityScore(90);
        dto.setCamerashotsInterval(15);
        dto.setTestResults(Arrays.asList(new TestResultsDTO()));
        dto.setFeedback(new FeedbackResponseDTO());

        assertEquals("Java Assessment", dto.getAssessmentTitle());
        assertEquals("Completed", dto.getStatus());
        assertEquals("John Doe", dto.getProctor());
        assertEquals("Senior", dto.getProctorLevel());
        assertEquals("2024-01-01", dto.getCommenceDate());
        assertEquals("2024-12-31", dto.getExpireDate());
        assertEquals("2024-01-01T11:00:00", dto.getAssessmentEndTime());
        assertEquals("2024-01-01T10:00:00", dto.getAssessmentStartTime());
        assertEquals(120, dto.getAssessmentTime());
        assertEquals(60, dto.getAssessmentTimeTaken());
        assertEquals(85.0, dto.getAssessmentCandidateScore());
        assertEquals(100.0, dto.getAssessmentOverallScore());
        assertEquals(85.0, dto.getAssessmentCandidatePercentage());
        assertEquals(2, dto.getAssessmentWindowViolationCount());
        assertEquals(30, dto.getAssessmentWindowViolationDuration());
        assertEquals(100, dto.getAssessmentTakerShotCount());
        assertEquals(5, dto.getAssessmentTakerViolationShotCount());
        assertEquals(50, dto.getWindowShotCount());
        assertEquals(2, dto.getWindowViolationShotCount());
        assertEquals(10, dto.getScreenshotsInterval());
        assertEquals(90, dto.getIntegrityScore());
        assertEquals(15, dto.getCamerashotsInterval());
        assertNotNull(dto.getTestResults());
        assertNotNull(dto.getFeedback());
    }

    @Test
    void assessmentResultsDTO_AllArgsConstructor_ShouldCreateObject() {
        List<TestResultsDTO> testResults = Arrays.asList(new TestResultsDTO());
        FeedbackResponseDTO feedback = new FeedbackResponseDTO();

        AssessmentResultsDTO dto = new AssessmentResultsDTO(
                "Java Assessment", "Completed", "John Doe", "Senior", "2024-01-01", "2024-12-31",
                "2024-01-01T11:00:00", "2024-01-01T10:00:00", 120, 60, 85.0, 100.0, 85.0, 2, 30, 100, 5, 50, 2, 10, 90, 15, testResults, feedback
        );

        assertEquals("Java Assessment", dto.getAssessmentTitle());
        assertEquals("Completed", dto.getStatus());
        assertEquals("John Doe", dto.getProctor());
        assertEquals("Senior", dto.getProctorLevel());
        assertEquals("2024-01-01", dto.getCommenceDate());
        assertEquals("2024-12-31", dto.getExpireDate());
        assertEquals("2024-01-01T11:00:00", dto.getAssessmentEndTime());
        assertEquals("2024-01-01T10:00:00", dto.getAssessmentStartTime());
        assertEquals(120, dto.getAssessmentTime());
        assertEquals(60, dto.getAssessmentTimeTaken());
        assertEquals(85.0, dto.getAssessmentCandidateScore());
        assertEquals(100.0, dto.getAssessmentOverallScore());
        assertEquals(85.0, dto.getAssessmentCandidatePercentage());
        assertEquals(2, dto.getAssessmentWindowViolationCount());
        assertEquals(30, dto.getAssessmentWindowViolationDuration());
        assertEquals(100, dto.getAssessmentTakerShotCount());
        assertEquals(5, dto.getAssessmentTakerViolationShotCount());
        assertEquals(50, dto.getWindowShotCount());
        assertEquals(2, dto.getWindowViolationShotCount());
        assertEquals(10, dto.getScreenshotsInterval());
        assertEquals(90, dto.getIntegrityScore());
        assertEquals(15, dto.getCamerashotsInterval());
        assertEquals(testResults, dto.getTestResults());
        assertEquals(feedback, dto.getFeedback());
    }

    @Test
    void assessmentResultsDTO_NoArgsConstructor_ShouldCreateObject() {
        AssessmentResultsDTO dto = new AssessmentResultsDTO();
        assertNotNull(dto);
    }

    @Test
    void assessmentResultsDTO_EqualsAndHashCode_ShouldWork() {
        AssessmentResultsDTO dto1 = new AssessmentResultsDTO(
                "Java Assessment", "Completed", "John Doe", "Senior", "2024-01-01", "2024-12-31",
                "2024-01-01T11:00:00", "2024-01-01T10:00:00", 120, 60, 85.0, 100.0, 85.0, 2, 30, 100, 5, 50, 2, 10, 90, 15, Arrays.asList(new TestResultsDTO()), new FeedbackResponseDTO()
        );

        AssessmentResultsDTO dto2 = new AssessmentResultsDTO(
                "Java Assessment", "Completed", "John Doe", "Senior", "2024-01-01", "2024-12-31",
                "2024-01-01T11:00:00", "2024-01-01T10:00:00", 120, 60, 85.0, 100.0, 85.0, 2, 30, 100, 5, 50, 2, 10, 90, 15, Arrays.asList(new TestResultsDTO()), new FeedbackResponseDTO()
        );

        AssessmentResultsDTO dto3 = new AssessmentResultsDTO(
                "Python Assessment", "Completed", "John Doe", "Senior", "2024-01-01", "2024-12-31",
                "2024-01-01T11:00:00", "2024-01-01T10:00:00", 120, 60, 85.0, 100.0, 85.0, 2, 30, 100, 5, 50, 2, 10, 90, 15, Arrays.asList(new TestResultsDTO()), new FeedbackResponseDTO()
        );

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void assessmentResultsDTO_ToString_ShouldNotReturnNull() {
        AssessmentResultsDTO dto = new AssessmentResultsDTO(
                "Java Assessment", "Completed", "John Doe", "Senior", "2024-01-01", "2024-12-31",
                "2024-01-01T11:00:00", "2024-01-01T10:00:00", 120, 60, 85.0, 100.0, 85.0, 2, 30, 100, 5, 50, 2, 10, 90, 15, Arrays.asList(new TestResultsDTO()), new FeedbackResponseDTO()
        );
        assertNotNull(dto.toString());
    }

    @Test
    void assessmentResultsDTO_NullListsAndFeedback_ShouldWork() {
        AssessmentResultsDTO dto = new AssessmentResultsDTO();
        dto.setTestResults(null);
        dto.setFeedback(null);

        assertNull(dto.getTestResults());
        assertNull(dto.getFeedback());

        AssessmentResultsDTO dto2 = new AssessmentResultsDTO(
                "Java Assessment", "Completed", "John Doe", "Senior", "2024-01-01", "2024-12-31",
                "2024-01-01T11:00:00", "2024-01-01T10:00:00", 120, 60, 85.0, 100.0, 85.0, 2, 30, 100, 5, 50, 2, 10, 90, 15, null, null
        );

        assertNull(dto2.getTestResults());
        assertNull(dto2.getFeedback());
    }

    @Test
    void assessmentResultsDTO_ZeroValues_ShouldWork() {
        AssessmentResultsDTO dto = new AssessmentResultsDTO();
        dto.setAssessmentTime(0);
        dto.setAssessmentTimeTaken(0);
        dto.setAssessmentCandidateScore(0.0);
        dto.setAssessmentOverallScore(0.0);
        dto.setAssessmentCandidatePercentage(0.0);
        dto.setAssessmentWindowViolationCount(0);
        dto.setAssessmentWindowViolationDuration(0);
        dto.setAssessmentTakerShotCount(0);
        dto.setAssessmentTakerViolationShotCount(0);
        dto.setWindowShotCount(0);
        dto.setWindowViolationShotCount(0);
        dto.setScreenshotsInterval(0);
        dto.setIntegrityScore(0);
        dto.setCamerashotsInterval(0);

        assertEquals(0, dto.getAssessmentTime());
        assertEquals(0, dto.getAssessmentTimeTaken());
        assertEquals(0.0, dto.getAssessmentCandidateScore());
        assertEquals(0.0, dto.getAssessmentOverallScore());
        assertEquals(0.0, dto.getAssessmentCandidatePercentage());
        assertEquals(0, dto.getAssessmentWindowViolationCount());
        assertEquals(0, dto.getAssessmentWindowViolationDuration());
        assertEquals(0, dto.getAssessmentTakerShotCount());
        assertEquals(0, dto.getAssessmentTakerViolationShotCount());
        assertEquals(0, dto.getWindowShotCount());
        assertEquals(0, dto.getWindowViolationShotCount());
        assertEquals(0, dto.getScreenshotsInterval());
        assertEquals(0, dto.getIntegrityScore());
        assertEquals(0, dto.getCamerashotsInterval());

        AssessmentResultsDTO dto2 = new AssessmentResultsDTO(
                "Java Assessment", "Completed", "John Doe", "Senior", "2024-01-01", "2024-12-31",
                "2024-01-01T11:00:00", "2024-01-01T10:00:00", 0, 0, 0.0, 0.0, 0.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null
        );

        assertEquals(0, dto2.getAssessmentTime());
        assertEquals(0, dto2.getAssessmentTimeTaken());
        assertEquals(0.0, dto2.getAssessmentCandidateScore());
        assertEquals(0.0, dto2.getAssessmentOverallScore());
        assertEquals(0.0, dto2.getAssessmentCandidatePercentage());
        assertEquals(0, dto2.getAssessmentWindowViolationCount());
        assertEquals(0, dto2.getAssessmentWindowViolationDuration());
        assertEquals(0, dto2.getAssessmentTakerShotCount());
        assertEquals(0, dto2.getAssessmentTakerViolationShotCount());
        assertEquals(0, dto2.getWindowShotCount());
        assertEquals(0, dto2.getWindowViolationShotCount());
        assertEquals(0, dto2.getScreenshotsInterval());
        assertEquals(0, dto2.getIntegrityScore());
        assertEquals(0, dto2.getCamerashotsInterval());
    }
}