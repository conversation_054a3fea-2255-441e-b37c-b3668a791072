package com.amap.amapreportmanagementservice.dto.progress;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class MatchMatrixAnswerDTOTest {

    @Test
    void testMatchMatrixAnswerDTO_GetterSetter_ShouldWork() {
        MatchMatrixAnswerDTO dto = new MatchMatrixAnswerDTO();
        List<String> options = Arrays.asList("option1", "option2");
        List<String> questions = Arrays.asList("question1", "question2");

        dto.setId("123");
        dto.setQuestionId("456");
        dto.setOptions(options);
        dto.setQuestions(questions);

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(questions, dto.getQuestions());
    }

    @Test
    void testMatchMatrixAnswerDTO_AllArgsConstructor_ShouldCreateObject() {
        List<String> options = Arrays.asList("option1", "option2");
        List<String> questions = Arrays.asList("question1", "question2");

        MatchMatrixAnswerDTO dto = new MatchMatrixAnswerDTO("123", "456", options, questions);

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(questions, dto.getQuestions());
    }

    @Test
    void testMatchMatrixAnswerDTO_NoArgsConstructor_ShouldCreateObject() {
        MatchMatrixAnswerDTO dto = new MatchMatrixAnswerDTO();
        assertNotNull(dto);
    }

    @Test
    void testMatchMatrixAnswerDTO_Builder_ShouldCreateObject() {
        List<String> options = Arrays.asList("option1", "option2");
        List<String> questions = Arrays.asList("question1", "question2");

        MatchMatrixAnswerDTO dto = MatchMatrixAnswerDTO.builder()
                .id("123")
                .questionId("456")
                .options(options)
                .questions(questions)
                .build();

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(questions, dto.getQuestions());
    }

    @Test
    void testMatchMatrixAnswerDTO_EqualsAndHashCode_ShouldWork() {
        List<String> options1 = Arrays.asList("option1", "option2");
        List<String> questions1 = Arrays.asList("question1", "question2");
        List<String> options2 = Arrays.asList("option3", "option4");
        List<String> questions2 = Arrays.asList("question3", "question4");

        MatchMatrixAnswerDTO dto1 = MatchMatrixAnswerDTO.builder().id("1").questionId("q1").options(options1).questions(questions1).build();
        MatchMatrixAnswerDTO dto2 = MatchMatrixAnswerDTO.builder().id("1").questionId("q1").options(options1).questions(questions1).build();
        MatchMatrixAnswerDTO dto3 = MatchMatrixAnswerDTO.builder().id("2").questionId("q2").options(options2).questions(questions2).build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testMatchMatrixAnswerDTO_ToString_ShouldNotReturnNull() {
        MatchMatrixAnswerDTO dto = MatchMatrixAnswerDTO.builder().id("1").build();
        assertNotNull(dto.toString());
    }
}