package com.amap.amapreportmanagementservice.dto.progress;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class FillInAnswerDTOTest {

    @Test
    void testFillInAnswerDTO_GetterSetter_ShouldWork() {
        FillInAnswerDTO dto = new FillInAnswerDTO();
        List<String> options = Arrays.asList("option1", "option2");
        List<String> answer = Arrays.asList("answer1", "answer2");

        dto.setId("123");
        dto.setQuestionId("456");
        dto.setOptions(options);
        dto.setAnswer(answer);

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(answer, dto.getAnswer());
    }

    @Test
    void testFillInAnswerDTO_AllArgsConstructor_ShouldCreateObject() {
        List<String> options = Arrays.asList("option1", "option2");
        List<String> answer = Arrays.asList("answer1", "answer2");

        FillInAnswerDTO dto = new FillInAnswerDTO("123", "456", options, answer);

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(answer, dto.getAnswer());
    }

    @Test
    void testFillInAnswerDTO_NoArgsConstructor_ShouldCreateObject() {
        FillInAnswerDTO dto = new FillInAnswerDTO();
        assertNotNull(dto);
    }

    @Test
    void testFillInAnswerDTO_Builder_ShouldCreateObject() {
        List<String> options = Arrays.asList("option1", "option2");
        List<String> answer = Arrays.asList("answer1", "answer2");

        FillInAnswerDTO dto = FillInAnswerDTO.builder()
                .id("123")
                .questionId("456")
                .options(options)
                .answer(answer)
                .build();

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(answer, dto.getAnswer());
    }

    @Test
    void testFillInAnswerDTO_EqualsAndHashCode_ShouldWork() {
        List<String> options1 = Arrays.asList("option1", "option2");
        List<String> answer1 = Arrays.asList("answer1", "answer2");
        List<String> options2 = Arrays.asList("option3", "option4");
        List<String> answer2 = Arrays.asList("answer3", "answer4");

        FillInAnswerDTO dto1 = FillInAnswerDTO.builder().id("1").questionId("q1").options(options1).answer(answer1).build();
        FillInAnswerDTO dto2 = FillInAnswerDTO.builder().id("1").questionId("q1").options(options1).answer(answer1).build();
        FillInAnswerDTO dto3 = FillInAnswerDTO.builder().id("2").questionId("q2").options(options2).answer(answer2).build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testFillInAnswerDTO_ToString_ShouldNotReturnNull() {
        FillInAnswerDTO dto = FillInAnswerDTO.builder().id("1").build();
        assertNotNull(dto.toString());
    }
}