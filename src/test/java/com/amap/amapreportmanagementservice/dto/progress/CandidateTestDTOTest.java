package com.amap.amapreportmanagementservice.dto.progress;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CandidateTestDTOTest {

    @Test
    void testCandidateTestDTO_GetterSetter_ShouldWork() {
        CandidateTestDTO dto = new CandidateTestDTO();
        List<CandidateQuestionDTO> questions = Arrays.asList(new CandidateQuestionDTO());

        dto.setId("123");
        dto.setTitle("Test Title");
        dto.setDomain("Test Domain");
        dto.setActivated(true);
        dto.setDifficultyLevel("Hard");
        dto.setPassage("Test Passage");
        dto.setDomainId("domain1");
        dto.setDescription("Test Description");
        dto.setInstructions("Test Instructions");
        dto.setDuration(120);
        dto.setQuestions(questions);

        assertEquals("123", dto.getId());
        assertEquals("Test Title", dto.getTitle());
        assertEquals("Test Domain", dto.getDomain());
        assertTrue(dto.isActivated());
        assertEquals("Hard", dto.getDifficultyLevel());
        assertEquals("Test Passage", dto.getPassage());
        assertEquals("domain1", dto.getDomainId());
        assertEquals("Test Description", dto.getDescription());
        assertEquals("Test Instructions", dto.getInstructions());
        assertEquals(120, dto.getDuration());
        assertEquals(questions, dto.getQuestions());
    }

    @Test
    void testCandidateTestDTO_AllArgsConstructor_ShouldCreateObject() {
        List<CandidateQuestionDTO> questions = Arrays.asList(new CandidateQuestionDTO());

        CandidateTestDTO dto = new CandidateTestDTO("123", "Test Title", "Test Domain", true, "Hard", "Test Passage", "domain1", "Test Description", "Test Instructions", 120, questions);

        assertEquals("123", dto.getId());
        assertEquals("Test Title", dto.getTitle());
        assertEquals("Test Domain", dto.getDomain());
        assertTrue(dto.isActivated());
        assertEquals("Hard", dto.getDifficultyLevel());
        assertEquals("Test Passage", dto.getPassage());
        assertEquals("domain1", dto.getDomainId());
        assertEquals("Test Description", dto.getDescription());
        assertEquals("Test Instructions", dto.getInstructions());
        assertEquals(120, dto.getDuration());
        assertEquals(questions, dto.getQuestions());
    }

    @Test
    void testCandidateTestDTO_NoArgsConstructor_ShouldCreateObject() {
        CandidateTestDTO dto = new CandidateTestDTO();
        assertNotNull(dto);
    }

    @Test
    void testCandidateTestDTO_Builder_ShouldCreateObject() {
        List<CandidateQuestionDTO> questions = Arrays.asList(new CandidateQuestionDTO());

        CandidateTestDTO dto = CandidateTestDTO.builder()
                .id("123")
                .title("Test Title")
                .domain("Test Domain")
                .isActivated(true)
                .difficultyLevel("Hard")
                .passage("Test Passage")
                .domainId("domain1")
                .description("Test Description")
                .instructions("Test Instructions")
                .duration(120)
                .questions(questions)
                .build();

        assertEquals("123", dto.getId());
        assertEquals("Test Title", dto.getTitle());
        assertEquals("Test Domain", dto.getDomain());
        assertTrue(dto.isActivated());
        assertEquals("Hard", dto.getDifficultyLevel());
        assertEquals("Test Passage", dto.getPassage());
        assertEquals("domain1", dto.getDomainId());
        assertEquals("Test Description", dto.getDescription());
        assertEquals("Test Instructions", dto.getInstructions());
        assertEquals(120, dto.getDuration());
        assertEquals(questions, dto.getQuestions());
    }

    @Test
    void testCandidateTestDTO_NonNullFields_ShouldNotBeNull() {
        assertThrows(NullPointerException.class, () -> new CandidateTestDTO(null, "Test Title", "Test Domain", true, "Hard", "Test Passage", "domain1", "Test Description", "Test Instructions", 120, Arrays.asList(new CandidateQuestionDTO())));
    }

    @Test
    void testCandidateTestDTO_EqualsAndHashCode_ShouldWork() {
        CandidateTestDTO dto1 = CandidateTestDTO.builder().id("1").title("t1").build();
        CandidateTestDTO dto2 = CandidateTestDTO.builder().id("1").title("t1").build();
        CandidateTestDTO dto3 = CandidateTestDTO.builder().id("2").title("t2").build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testCandidateTestDTO_ToString_ShouldNotReturnNull() {
        CandidateTestDTO dto = CandidateTestDTO.builder().id("1").build();
        assertNotNull(dto.toString());
    }
}