package com.amap.amapreportmanagementservice.dto.progress;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class MultipleAnswerDTOTest {

    @Test
    void testMultipleAnswerDTO_GetterSetter_ShouldWork() {
        MultipleAnswerDTO dto = new MultipleAnswerDTO();
        List<String> options = Arrays.asList("option1", "option2");
        List<String> answer = Arrays.asList("answer1", "answer2");

        dto.setId("123");
        dto.setQuestionId("456");
        dto.setOptions(options);
        dto.setAnswer(answer);

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(answer, dto.getAnswer());
    }

    @Test
    void testMultipleAnswerDTO_AllArgsConstructor_ShouldCreateObject() {
        List<String> options = Arrays.asList("option1", "option2");
        List<String> answer = Arrays.asList("answer1", "answer2");

        MultipleAnswerDTO dto = new MultipleAnswerDTO("123", "456", options, answer);

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(answer, dto.getAnswer());
    }

    @Test
    void testMultipleAnswerDTO_NoArgsConstructor_ShouldCreateObject() {
        MultipleAnswerDTO dto = new MultipleAnswerDTO();
        assertNotNull(dto);
    }

    @Test
    void testMultipleAnswerDTO_Builder_ShouldCreateObject() {
        List<String> options = Arrays.asList("option1", "option2");
        List<String> answer = Arrays.asList("answer1", "answer2");

        MultipleAnswerDTO dto = MultipleAnswerDTO.builder()
                .id("123")
                .questionId("456")
                .options(options)
                .answer(answer)
                .build();

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(answer, dto.getAnswer());
    }

    @Test
    void testMultipleAnswerDTO_EqualsAndHashCode_ShouldWork() {
        List<String> options1 = Arrays.asList("option1", "option2");
        List<String> answer1 = Arrays.asList("answer1", "answer2");
        List<String> options2 = Arrays.asList("option3", "option4");
        List<String> answer2 = Arrays.asList("answer3", "answer4");

        MultipleAnswerDTO dto1 = MultipleAnswerDTO.builder().id("1").questionId("q1").options(options1).answer(answer1).build();
        MultipleAnswerDTO dto2 = MultipleAnswerDTO.builder().id("1").questionId("q1").options(options1).answer(answer1).build();
        MultipleAnswerDTO dto3 = MultipleAnswerDTO.builder().id("2").questionId("q2").options(options2).answer(answer2).build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testMultipleAnswerDTO_ToString_ShouldNotReturnNull() {
        MultipleAnswerDTO dto = MultipleAnswerDTO.builder().id("1").build();
        assertNotNull(dto.toString());
    }
}