package com.amap.amapreportmanagementservice.dto.progress;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;


class CandidateAssessmentDTOTest {

    @Test
    void testCandidateAssessmentDTO_GetterSetter_ShouldWork() {
        CandidateAssessmentDTO dto = new CandidateAssessmentDTO();
        List<CandidateTestDTO> tests = Arrays.asList(new CandidateTestDTO());

        dto.setId("123");
        dto.setTitle("Math Test");
        dto.setInstructions("Follow the instructions carefully.");
        dto.setSystem("Online Testing System");
        dto.setProctor("John Doe");
        dto.setProctorLevel("Advanced");
        dto.setAssessmentDuration(60);
        dto.setAssessmentStartTime("2024-01-01T10:00:00");
        dto.setTests(tests);

        assertEquals("123", dto.getId());
        assertEquals("Math Test", dto.getTitle());
        assertEquals("Follow the instructions carefully.", dto.getInstructions());
        assertEquals("Online Testing System", dto.getSystem());
        assertEquals("John Doe", dto.getProctor());
        assertEquals("Advanced", dto.getProctorLevel());
        assertEquals(60, dto.getAssessmentDuration());
        assertEquals("2024-01-01T10:00:00", dto.getAssessmentStartTime());
        assertEquals(tests, dto.getTests());
    }

    @Test
    void testCandidateAssessmentDTO_AllArgsConstructor_ShouldCreateObject() {
        List<CandidateTestDTO> tests = Arrays.asList(new CandidateTestDTO());
        CandidateAssessmentDTO dto = new CandidateAssessmentDTO("123", "Math Test", "Follow the instructions carefully.", "Online Testing System", "John Doe", "Advanced", 60, "2024-01-01T10:00:00", tests);

        assertEquals("123", dto.getId());
        assertEquals("Math Test", dto.getTitle());
        assertEquals("Follow the instructions carefully.", dto.getInstructions());
        assertEquals("Online Testing System", dto.getSystem());
        assertEquals("John Doe", dto.getProctor());
        assertEquals("Advanced", dto.getProctorLevel());
        assertEquals(60, dto.getAssessmentDuration());
        assertEquals("2024-01-01T10:00:00", dto.getAssessmentStartTime());
        assertEquals(tests, dto.getTests());
    }

    @Test
    void testCandidateAssessmentDTO_NoArgsConstructor_ShouldCreateObject() {
        CandidateAssessmentDTO dto = new CandidateAssessmentDTO();
        assertNotNull(dto);
    }

    @Test
    void testCandidateAssessmentDTO_Builder_ShouldCreateObject() {
        List<CandidateTestDTO> tests = Arrays.asList(new CandidateTestDTO());
        CandidateAssessmentDTO dto = CandidateAssessmentDTO.builder()
                .id("123")
                .title("Math Test")
                .instructions("Follow the instructions carefully.")
                .system("Online Testing System")
                .proctor("John Doe")
                .proctorLevel("Advanced")
                .assessmentDuration(60)
                .assessmentStartTime("2024-01-01T10:00:00")
                .tests(tests)
                .build();

        assertEquals("123", dto.getId());
        assertEquals("Math Test", dto.getTitle());
        assertEquals("Follow the instructions carefully.", dto.getInstructions());
        assertEquals("Online Testing System", dto.getSystem());
        assertEquals("John Doe", dto.getProctor());
        assertEquals("Advanced", dto.getProctorLevel());
        assertEquals(60, dto.getAssessmentDuration());
        assertEquals("2024-01-01T10:00:00", dto.getAssessmentStartTime());
        assertEquals(tests, dto.getTests());
    }

    @Test
    void testCandidateAssessmentDTO_NonNullFields_ShouldNotBeNull() {
        assertThrows(NullPointerException.class, () -> new CandidateAssessmentDTO(null, "Math Test", "Follow the instructions carefully.", "Online Testing System", "John Doe", "Advanced", 60, "2024-01-01T10:00:00", Arrays.asList(new CandidateTestDTO())));
    }

    @Test
    void testCandidateAssessmentDTO_EqualsAndHashCode_ShouldWork() {
        List<CandidateTestDTO> tests = Arrays.asList(new CandidateTestDTO());
        CandidateAssessmentDTO dto1 = new CandidateAssessmentDTO("123", "Math Test", "Follow the instructions carefully.", "Online Testing System", "John Doe", "Advanced", 60, "2024-01-01T10:00:00", tests);
        CandidateAssessmentDTO dto2 = new CandidateAssessmentDTO("123", "Math Test", "Follow the instructions carefully.", "Online Testing System", "John Doe", "Advanced", 60, "2024-01-01T10:00:00", tests);
        CandidateAssessmentDTO dto3 = new CandidateAssessmentDTO("456", "Science Test", "Read the instructions carefully.", "Another System", "Jane Doe", "Intermediate", 90, "2024-02-01T11:00:00", Arrays.asList(new CandidateTestDTO()));

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testCandidateAssessmentDTO_ToString_ShouldNotReturnNull() {
        CandidateAssessmentDTO dto = new CandidateAssessmentDTO("123", "Math Test", "Follow the instructions carefully.", "Online Testing System", "John Doe", "Advanced", 60, "2024-01-01T10:00:00", Arrays.asList(new CandidateTestDTO()));
        assertNotNull(dto.toString());
    }
}