package com.amap.amapreportmanagementservice.dto.progress;

import com.amap.amapreportmanagementservice.dto.results.CodeResultDTO;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CandidateQuestionDTOTest {

    @Test
    void testCandidateQuestionDTO_GetterSetter_ShouldWork() {
        CandidateQuestionDTO dto = new CandidateQuestionDTO();
        List<String> testTakerAnswers = Arrays.asList("answer1", "answer2");
        MultipleAnswerDTO multipleSelectAnswer = new MultipleAnswerDTO();
        MultipleChoiceAnswerDTO multipleChoiceAnswer = new MultipleChoiceAnswerDTO();
        TrueOrFalseAnswerDTO trueOrFalseAnswer = new TrueOrFalseAnswerDTO();
        FillInAnswerDTO fillInAnswer = new FillInAnswerDTO();
        MatchMatrixAnswerDTO matchMatrixAnswer = new MatchMatrixAnswerDTO();

        dto.setId("123");
        dto.setQuestionText("What is the capital of France?");
        dto.setQuestionType("Multiple Choice");
        dto.setDomain("Geography");
        dto.setCategory("Capitals");
        dto.setScore(10.0);
        dto.setTimeLimit(60);
        dto.setDifficultyLevel("Easy");
        dto.setIsComprehension("false");
        dto.setIsAnswerCorrect("true");
        dto.setDomainId("domain1");
        dto.setCategoryId("category1");
        dto.setMultipleSelectAnswer(multipleSelectAnswer);
        dto.setMultipleChoiceAnswer(multipleChoiceAnswer);
        dto.setTrueOrFalseAnswer(trueOrFalseAnswer);
        dto.setFillInAnswer(fillInAnswer);
        dto.setMatchMatrixAnswer(matchMatrixAnswer);
        dto.setTestTakerAnswers(testTakerAnswers);

        assertEquals("123", dto.getId());
        assertEquals("What is the capital of France?", dto.getQuestionText());
        assertEquals("Multiple Choice", dto.getQuestionType());
        assertEquals("Geography", dto.getDomain());
        assertEquals("Capitals", dto.getCategory());
        assertEquals(10.0, dto.getScore());
        assertEquals(60, dto.getTimeLimit());
        assertEquals("Easy", dto.getDifficultyLevel());
        assertEquals("false", dto.getIsComprehension());
        assertEquals("true", dto.getIsAnswerCorrect());
        assertEquals("domain1", dto.getDomainId());
        assertEquals("category1", dto.getCategoryId());
        assertEquals(multipleSelectAnswer, dto.getMultipleSelectAnswer());
        assertEquals(multipleChoiceAnswer, dto.getMultipleChoiceAnswer());
        assertEquals(trueOrFalseAnswer, dto.getTrueOrFalseAnswer());
        assertEquals(fillInAnswer, dto.getFillInAnswer());
        assertEquals(matchMatrixAnswer, dto.getMatchMatrixAnswer());
        assertEquals(testTakerAnswers, dto.getTestTakerAnswers());
    }

    @Test
    void testCandidateQuestionDTO_AllArgsConstructor_ShouldCreateObject() {
        List<String> testTakerAnswers = Arrays.asList("answer1", "answer2");
        MultipleAnswerDTO multipleSelectAnswer = new MultipleAnswerDTO();
        MultipleChoiceAnswerDTO multipleChoiceAnswer = new MultipleChoiceAnswerDTO();
        TrueOrFalseAnswerDTO trueOrFalseAnswer = new TrueOrFalseAnswerDTO();
        FillInAnswerDTO fillInAnswer = new FillInAnswerDTO();
        MatchMatrixAnswerDTO matchMatrixAnswer = new MatchMatrixAnswerDTO();
        CodeResultDTO codeResult = new CodeResultDTO();
        CandidateQuestionDTO dto = new CandidateQuestionDTO("123", "What is the capital of France?", "Multiple Choice", "Geography", "Capitals", 10.0, 60, "Easy", "false", "true", "domain1", "category1", multipleSelectAnswer, multipleChoiceAnswer, trueOrFalseAnswer, fillInAnswer, matchMatrixAnswer, codeResult, testTakerAnswers);

        assertEquals("123", dto.getId());
        assertEquals("What is the capital of France?", dto.getQuestionText());
        assertEquals("Multiple Choice", dto.getQuestionType());
        assertEquals("Geography", dto.getDomain());
        assertEquals("Capitals", dto.getCategory());
        assertEquals(10.0, dto.getScore());
        assertEquals(60, dto.getTimeLimit());
        assertEquals("Easy", dto.getDifficultyLevel());
        assertEquals("false", dto.getIsComprehension());
        assertEquals("true", dto.getIsAnswerCorrect());
        assertEquals("domain1", dto.getDomainId());
        assertEquals("category1", dto.getCategoryId());
        assertEquals(multipleSelectAnswer, dto.getMultipleSelectAnswer());
        assertEquals(multipleChoiceAnswer, dto.getMultipleChoiceAnswer());
        assertEquals(trueOrFalseAnswer, dto.getTrueOrFalseAnswer());
        assertEquals(fillInAnswer, dto.getFillInAnswer());
        assertEquals(matchMatrixAnswer, dto.getMatchMatrixAnswer());
        assertEquals(testTakerAnswers, dto.getTestTakerAnswers());
    }

    @Test
    void testCandidateQuestionDTO_NoArgsConstructor_ShouldCreateObject() {
        CandidateQuestionDTO dto = new CandidateQuestionDTO();
        assertNotNull(dto);
    }

    @Test
    void testCandidateQuestionDTO_Builder_ShouldCreateObject() {
        List<String> testTakerAnswers = Arrays.asList("answer1", "answer2");
        MultipleAnswerDTO multipleSelectAnswer = new MultipleAnswerDTO();
        MultipleChoiceAnswerDTO multipleChoiceAnswer = new MultipleChoiceAnswerDTO();
        TrueOrFalseAnswerDTO trueOrFalseAnswer = new TrueOrFalseAnswerDTO();
        FillInAnswerDTO fillInAnswer = new FillInAnswerDTO();
        MatchMatrixAnswerDTO matchMatrixAnswer = new MatchMatrixAnswerDTO();

        CandidateQuestionDTO dto = CandidateQuestionDTO.builder()
                .id("123")
                .questionText("What is the capital of France?")
                .questionType("Multiple Choice")
                .domain("Geography")
                .category("Capitals")
                .score(10.0)
                .timeLimit(60)
                .difficultyLevel("Easy")
                .isComprehension("false")
                .isAnswerCorrect("true")
                .domainId("domain1")
                .categoryId("category1")
                .multipleSelectAnswer(multipleSelectAnswer)
                .multipleChoiceAnswer(multipleChoiceAnswer)
                .trueOrFalseAnswer(trueOrFalseAnswer)
                .fillInAnswer(fillInAnswer)
                .matchMatrixAnswer(matchMatrixAnswer)
                .testTakerAnswers(testTakerAnswers)
                .build();

        assertEquals("123", dto.getId());
        assertEquals("What is the capital of France?", dto.getQuestionText());
        assertEquals("Multiple Choice", dto.getQuestionType());
        assertEquals("Geography", dto.getDomain());
        assertEquals("Capitals", dto.getCategory());
        assertEquals(10.0, dto.getScore());
        assertEquals(60, dto.getTimeLimit());
        assertEquals("Easy", dto.getDifficultyLevel());
        assertEquals("false", dto.getIsComprehension());
        assertEquals("true", dto.getIsAnswerCorrect());
        assertEquals("domain1", dto.getDomainId());
        assertEquals("category1", dto.getCategoryId());
        assertEquals(multipleSelectAnswer, dto.getMultipleSelectAnswer());
        assertEquals(multipleChoiceAnswer, dto.getMultipleChoiceAnswer());
        assertEquals(trueOrFalseAnswer, dto.getTrueOrFalseAnswer());
        assertEquals(fillInAnswer, dto.getFillInAnswer());
        assertEquals(matchMatrixAnswer, dto.getMatchMatrixAnswer());
        assertEquals(testTakerAnswers, dto.getTestTakerAnswers());
    }

    @Test
    void testCandidateQuestionDTO_NonNullFields_ShouldNotBeNull() {
        assertThrows(NullPointerException.class, () -> new CandidateQuestionDTO(null, "What is the capital of France?", "Multiple Choice", "Geography", "Capitals", 10.0, 60, "Easy", "false", "true", "domain1", "category1", new MultipleAnswerDTO(), new MultipleChoiceAnswerDTO(), new TrueOrFalseAnswerDTO(), new FillInAnswerDTO(), new MatchMatrixAnswerDTO(),new CodeResultDTO(), Arrays.asList("answer1", "answer2")));
    }

    @Test
    void testCandidateQuestionDTO_EqualsAndHashCode_ShouldWork() {
        CandidateQuestionDTO dto1 = CandidateQuestionDTO.builder().id("1").questionText("q1").build();
        CandidateQuestionDTO dto2 = CandidateQuestionDTO.builder().id("1").questionText("q1").build();
        CandidateQuestionDTO dto3 = CandidateQuestionDTO.builder().id("2").questionText("q2").build();

        assertEquals(dto1, dto2);
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto2, dto3);

        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1.hashCode(), dto3.hashCode());

        // Test with null
        CandidateQuestionDTO dto4 = null;
        assertNotEquals(dto1, dto4);

        // Test with different object type
        assertNotEquals(dto1, "Not a DTO");

        // Test reflexivity
        assertEquals(dto1, dto1);

        // Test symmetry
        assertEquals(dto2, dto1);
    }
@Test
    void testCandidateQuestionDTO_ToString_ShouldNotReturnNull() {
        CandidateQuestionDTO dto = CandidateQuestionDTO.builder().id("1").questionText("q1").build();
        assertNotNull(dto.toString());
    }
}