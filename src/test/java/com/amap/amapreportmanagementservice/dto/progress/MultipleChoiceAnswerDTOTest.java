package com.amap.amapreportmanagementservice.dto.progress;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class MultipleChoiceAnswerDTOTest {

    @Test
    void testMultipleChoiceAnswerDTO_GetterSetter_ShouldWork() {
        MultipleChoiceAnswerDTO dto = new MultipleChoiceAnswerDTO();
        List<String> options = Arrays.asList("option1", "option2");
        List<String> answer = Arrays.asList("answer1");

        dto.setId("123");
        dto.setQuestionId("456");
        dto.setOptions(options);
        dto.setAnswer(answer);

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(answer, dto.getAnswer());
    }

    @Test
    void testMultipleChoiceAnswerDTO_AllArgsConstructor_ShouldCreateObject() {
        List<String> options = Arrays.asList("option1", "option2");
        List<String> answer = Arrays.asList("answer1");

        MultipleChoiceAnswerDTO dto = new MultipleChoiceAnswerDTO("123", "456", options, answer);

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(answer, dto.getAnswer());
    }

    @Test
    void testMultipleChoiceAnswerDTO_NoArgsConstructor_ShouldCreateObject() {
        MultipleChoiceAnswerDTO dto = new MultipleChoiceAnswerDTO();
        assertNotNull(dto);
    }

    @Test
    void testMultipleChoiceAnswerDTO_Builder_ShouldCreateObject() {
        List<String> options = Arrays.asList("option1", "option2");
        List<String> answer = Arrays.asList("answer1");

        MultipleChoiceAnswerDTO dto = MultipleChoiceAnswerDTO.builder()
                .id("123")
                .questionId("456")
                .options(options)
                .answer(answer)
                .build();

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(answer, dto.getAnswer());
    }

    @Test
    void testMultipleChoiceAnswerDTO_EqualsAndHashCode_ShouldWork() {
        List<String> options1 = Arrays.asList("option1", "option2");
        List<String> answer1 = Arrays.asList("answer1");
        List<String> options2 = Arrays.asList("option3", "option4");
        List<String> answer2 = Arrays.asList("answer2");

        MultipleChoiceAnswerDTO dto1 = MultipleChoiceAnswerDTO.builder().id("1").questionId("q1").options(options1).answer(answer1).build();
        MultipleChoiceAnswerDTO dto2 = MultipleChoiceAnswerDTO.builder().id("1").questionId("q1").options(options1).answer(answer1).build();
        MultipleChoiceAnswerDTO dto3 = MultipleChoiceAnswerDTO.builder().id("2").questionId("q2").options(options2).answer(answer2).build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testMultipleChoiceAnswerDTO_ToString_ShouldNotReturnNull() {
        MultipleChoiceAnswerDTO dto = MultipleChoiceAnswerDTO.builder().id("1").build();
        assertNotNull(dto.toString());
    }
}