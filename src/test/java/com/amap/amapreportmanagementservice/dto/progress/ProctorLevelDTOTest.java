package com.amap.amapreportmanagementservice.dto.progress;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ProctorLevelDTOTest {

    @Test
    void testProctorLevelDTO_GetterSetter_ShouldWork() {
        ProctorLevelDTO dto = new ProctorLevelDTO();
        dto.setId("123");
        dto.setName("Level 1");

        assertEquals("123", dto.getId());
        assertEquals("Level 1", dto.getName());
    }

    @Test
    void testProctorLevelDTO_AllArgsConstructor_ShouldCreateObject() {
        ProctorLevelDTO dto = new ProctorLevelDTO("123", "Level 1");

        assertEquals("123", dto.getId());
        assertEquals("Level 1", dto.getName());
    }

    @Test
    void testProctorLevelDTO_NoArgsConstructor_ShouldCreateObject() {
        ProctorLevelDTO dto = new ProctorLevelDTO();
        assertNotNull(dto);
    }

    @Test
    void testProctorLevelDTO_EqualsAndHashCode_ShouldWork() {
        ProctorLevelDTO dto1 = new ProctorLevelDTO("1", "Level A");
        ProctorLevelDTO dto2 = new ProctorLevelDTO("1", "Level A");
        ProctorLevelDTO dto3 = new ProctorLevelDTO("2", "Level B");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testProctorLevelDTO_ToString_ShouldNotReturnNull() {
        ProctorLevelDTO dto = new ProctorLevelDTO("1", "Level A");
        assertNotNull(dto.toString());
    }
}