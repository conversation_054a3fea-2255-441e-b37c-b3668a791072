package com.amap.amapreportmanagementservice.dto.progress;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class TrueOrFalseAnswerDTOTest {

    @Test
    void testTrueOrFalseAnswerDTO_GetterSetter_ShouldWork() {
        TrueOrFalseAnswerDTO dto = new TrueOrFalseAnswerDTO();
        List<String> options = Arrays.asList("True", "False");
        List<String> answer = Arrays.asList("True");

        dto.setId("123");
        dto.setQuestionId("456");
        dto.setOptions(options);
        dto.setAnswer(answer);

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(answer, dto.getAnswer());
    }

    @Test
    void testTrueOrFalseAnswerDTO_AllArgsConstructor_ShouldCreateObject() {
        List<String> options = Arrays.asList("True", "False");
        List<String> answer = Arrays.asList("True");

        TrueOrFalseAnswerDTO dto = new TrueOrFalseAnswerDTO("123", "456", options, answer);

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(answer, dto.getAnswer());
    }

    @Test
    void testTrueOrFalseAnswerDTO_NoArgsConstructor_ShouldCreateObject() {
        TrueOrFalseAnswerDTO dto = new TrueOrFalseAnswerDTO();
        assertNotNull(dto);
    }

    @Test
    void testTrueOrFalseAnswerDTO_Builder_ShouldCreateObject() {
        List<String> options = Arrays.asList("True", "False");
        List<String> answer = Arrays.asList("True");

        TrueOrFalseAnswerDTO dto = TrueOrFalseAnswerDTO.builder()
                .id("123")
                .questionId("456")
                .options(options)
                .answer(answer)
                .build();

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals(options, dto.getOptions());
        assertEquals(answer, dto.getAnswer());
    }

    @Test
    void testTrueOrFalseAnswerDTO_EqualsAndHashCode_ShouldWork() {
        List<String> options1 = Arrays.asList("True", "False");
        List<String> answer1 = Arrays.asList("True");
        List<String> options2 = Arrays.asList("True", "False");
        List<String> answer2 = Arrays.asList("False");

        TrueOrFalseAnswerDTO dto1 = TrueOrFalseAnswerDTO.builder().id("1").questionId("q1").options(options1).answer(answer1).build();
        TrueOrFalseAnswerDTO dto2 = TrueOrFalseAnswerDTO.builder().id("1").questionId("q1").options(options1).answer(answer1).build();
        TrueOrFalseAnswerDTO dto3 = TrueOrFalseAnswerDTO.builder().id("2").questionId("q2").options(options2).answer(answer2).build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testTrueOrFalseAnswerDTO_ToString_ShouldNotReturnNull() {
        TrueOrFalseAnswerDTO dto = TrueOrFalseAnswerDTO.builder().id("1").build();
        assertNotNull(dto.toString());
    }
}