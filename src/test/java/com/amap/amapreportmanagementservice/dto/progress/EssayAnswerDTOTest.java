package com.amap.amapreportmanagementservice.dto.progress;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class EssayAnswerDTOTest {

    @Test
    void testEssayAnswerDTO_GetterSetter_ShouldWork() {
        EssayAnswerDTO dto = new EssayAnswerDTO();
        dto.setId("123");
        dto.setQuestionId("456");
        dto.setRubrics("Test Rubrics");

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals("Test Rubrics", dto.getRubrics());
    }

    @Test
    void testEssayAnswerDTO_AllArgsConstructor_ShouldCreateObject() {
        EssayAnswerDTO dto = new EssayAnswerDTO("123", "456", "Test Rubrics");

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals("Test Rubrics", dto.getRubrics());
    }

    @Test
    void testEssayAnswerDTO_NoArgsConstructor_ShouldCreateObject() {
        EssayAnswerDTO dto = new EssayAnswerDTO();
        assertNotNull(dto);
    }

    @Test
    void testEssayAnswerDTO_Builder_ShouldCreateObject() {
        EssayAnswerDTO dto = EssayAnswerDTO.builder()
                .id("123")
                .questionId("456")
                .rubrics("Test Rubrics")
                .build();

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getQuestionId());
        assertEquals("Test Rubrics", dto.getRubrics());
    }

    @Test
    void testEssayAnswerDTO_EqualsAndHashCode_ShouldWork() {
        EssayAnswerDTO dto1 = EssayAnswerDTO.builder().id("1").questionId("q1").rubrics("r1").build();
        EssayAnswerDTO dto2 = EssayAnswerDTO.builder().id("1").questionId("q1").rubrics("r1").build();
        EssayAnswerDTO dto3 = EssayAnswerDTO.builder().id("2").questionId("q2").rubrics("r2").build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testEssayAnswerDTO_ToString_ShouldNotReturnNull() {
        EssayAnswerDTO dto = EssayAnswerDTO.builder().id("1").build();
        assertNotNull(dto.toString());
    }
}