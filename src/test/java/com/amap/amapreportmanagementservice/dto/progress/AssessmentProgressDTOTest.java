package com.amap.amapreportmanagementservice.dto.progress;

import lombok.*;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;


class AssessmentProgressDTOTest {

    @Test
    void testAssessmentProgressDTO_GetterSetter_ShouldWork() {
        AssessmentProgressDTO dto = new AssessmentProgressDTO();
        CandidateAssessmentDTO assessment = new CandidateAssessmentDTO();

        dto.setId("123");
        dto.setAssessmentId("456");
        dto.setOrganizationId("789");
        dto.setEmail("<EMAIL>");
        dto.setStatus("Started");
        dto.setProctor("John Doe");
        dto.setScreenshotsInterval(60);
        dto.setCamerashotsInterval(120);
        dto.setAssessment(assessment);
        dto.setExpireDate("2024-12-31");
        dto.setCommenceDate("2024-01-01");
        dto.setPassMark(70.0);
        dto.setReportCallbackURL("http://example.com/callback");

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getAssessmentId());
        assertEquals("789", dto.getOrganizationId());
        assertEquals("<EMAIL>", dto.getEmail());
        assertEquals("Started", dto.getStatus());
        assertEquals("John Doe", dto.getProctor());
        assertEquals(60, dto.getScreenshotsInterval());
        assertEquals(120, dto.getCamerashotsInterval());
        assertEquals(assessment, dto.getAssessment());
        assertEquals("2024-12-31", dto.getExpireDate());
        assertEquals("2024-01-01", dto.getCommenceDate());
        assertEquals(70.0, dto.getPassMark());
        assertEquals("http://example.com/callback", dto.getReportCallbackURL());
    }

    @Test
    void testAssessmentProgressDTO_AllArgsConstructor_ShouldCreateObject() {
        CandidateAssessmentDTO assessment = new CandidateAssessmentDTO();
        AssessmentProgressDTO dto = new AssessmentProgressDTO("123", "456", "789", "<EMAIL>", "Started", "John Doe", 60, 120, assessment, "2024-12-31", "2024-01-01", 70.0, "http://example.com/callback");

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getAssessmentId());
        assertEquals("789", dto.getOrganizationId());
        assertEquals("<EMAIL>", dto.getEmail());
        assertEquals("Started", dto.getStatus());
        assertEquals("John Doe", dto.getProctor());
        assertEquals(60, dto.getScreenshotsInterval());
        assertEquals(120, dto.getCamerashotsInterval());
        assertEquals(assessment, dto.getAssessment());
        assertEquals("2024-12-31", dto.getExpireDate());
        assertEquals("2024-01-01", dto.getCommenceDate());
        assertEquals(70.0, dto.getPassMark());
        assertEquals("http://example.com/callback", dto.getReportCallbackURL());
    }

    @Test
    void testAssessmentProgressDTO_NoArgsConstructor_ShouldCreateObject() {
        AssessmentProgressDTO dto = new AssessmentProgressDTO();
        assertNotNull(dto);
    }

    @Test
    void testAssessmentProgressDTO_Builder_ShouldCreateObject() {
        CandidateAssessmentDTO assessment = new CandidateAssessmentDTO();
        AssessmentProgressDTO dto = AssessmentProgressDTO.builder()
                .id("123")
                .assessmentId("456")
                .organizationId("789")
                .email("<EMAIL>")
                .status("Started")
                .proctor("John Doe")
                .screenshotsInterval(60)
                .camerashotsInterval(120)
                .assessment(assessment)
                .expireDate("2024-12-31")
                .commenceDate("2024-01-01")
                .passMark(70.0)
                .reportCallbackURL("http://example.com/callback")
                .build();

        assertEquals("123", dto.getId());
        assertEquals("456", dto.getAssessmentId());
        assertEquals("789", dto.getOrganizationId());
        assertEquals("<EMAIL>", dto.getEmail());
        assertEquals("Started", dto.getStatus());
        assertEquals("John Doe", dto.getProctor());
        assertEquals(60, dto.getScreenshotsInterval());
        assertEquals(120, dto.getCamerashotsInterval());
        assertEquals(assessment, dto.getAssessment());
        assertEquals("2024-12-31", dto.getExpireDate());
        assertEquals("2024-01-01", dto.getCommenceDate());
        assertEquals(70.0, dto.getPassMark());
        assertEquals("http://example.com/callback", dto.getReportCallbackURL());
    }

    @Test
    void testAssessmentProgressDTO_NonNullFields_ShouldNotBeNull() {
        assertThrows(NullPointerException.class, () -> new AssessmentProgressDTO(null, "456", "789", "<EMAIL>", "Started", "John Doe", 60, 120, new CandidateAssessmentDTO(), "2024-12-31", "2024-01-01", 70.0, "http://example.com/callback"));
        assertThrows(NullPointerException.class, () -> new AssessmentProgressDTO("123", null, "789", "<EMAIL>", "Started", "John Doe", 60, 120, new CandidateAssessmentDTO(), "2024-12-31", "2024-01-01", 70.0, "http://example.com/callback"));
        assertThrows(NullPointerException.class, () -> new AssessmentProgressDTO("123", "456", null, "<EMAIL>", "Started", "John Doe", 60, 120, new CandidateAssessmentDTO(), "2024-12-31", "2024-01-01", 70.0, "http://example.com/callback"));
    }

    @Test
    void testAssessmentProgressDTO_EqualsAndHashCode_ShouldWork() {
        CandidateAssessmentDTO assessment = new CandidateAssessmentDTO();
        AssessmentProgressDTO dto1 = new AssessmentProgressDTO("123", "456", "789", "<EMAIL>", "Started", "John Doe", 60, 120, assessment, "2024-12-31", "2024-01-01", 70.0, "http://example.com/callback");
        AssessmentProgressDTO dto2 = new AssessmentProgressDTO("123", "456", "789", "<EMAIL>", "Started", "John Doe", 60, 120, assessment, "2024-12-31", "2024-01-01", 70.0, "http://example.com/callback");
        AssessmentProgressDTO dto3 = new AssessmentProgressDTO("abc", "def", "ghi", "<EMAIL>", "Completed", "Jane Doe", 30, 90, new CandidateAssessmentDTO(), "2025-01-01", "2024-02-01", 80.0, "http://other.com/callback");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAssessmentProgressDTO_ToString_ShouldNotReturnNull() {
        AssessmentProgressDTO dto = new AssessmentProgressDTO("123", "456", "789", "<EMAIL>", "Started", "John Doe", 60, 120, new CandidateAssessmentDTO(), "2024-12-31", "2024-01-01", 70.0, "http://example.com/callback");
        assertNotNull(dto.toString());
    }
}