package com.amap.amapreportmanagementservice.dto.progress;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;


class AssessmentTakerDTOTest {

    @Test
    void testAssessmentTakerDTO_GetterSetter_ShouldWork() {
        AssessmentTakerDTO dto = new AssessmentTakerDTO();
        AssessmentProgressDTO progressDTO = new AssessmentProgressDTO();

        dto.setAssessmentTaker(progressDTO);

        assertEquals(progressDTO, dto.getAssessmentTaker());
    }

    @Test
    void testAssessmentTakerDTO_AllArgsConstructor_ShouldCreateObject() {
        AssessmentProgressDTO progressDTO = new AssessmentProgressDTO();
        AssessmentTakerDTO dto = new AssessmentTakerDTO(progressDTO);

        assertEquals(progressDTO, dto.getAssessmentTaker());
    }

    @Test
    void testAssessmentTakerDTO_NoArgsConstructor_ShouldCreateObject() {
        AssessmentTakerDTO dto = new AssessmentTakerDTO();
        assertNotNull(dto);
    }

    @Test
    void testAssessmentTakerDTO_EqualsAndHashCode_ShouldWork() {
        AssessmentProgressDTO progressDTO1 = new AssessmentProgressDTO("1", "assessment1", "org1", "email1", "status1", "proctor1", 60, 120, new CandidateAssessmentDTO(), "2024-12-31", "2024-01-01", 70.0, "http://example.com/callback");
        AssessmentProgressDTO progressDTO2 = new AssessmentProgressDTO("1", "assessment1", "org1", "email1", "status1", "proctor1", 60, 120, new CandidateAssessmentDTO(), "2024-12-31", "2024-01-01", 70.0, "http://example.com/callback");
        AssessmentProgressDTO progressDTO3 = AssessmentProgressDTO.builder().id("different").assessmentId("assessment3").organizationId("org3").build(); // Added assessmentId

        AssessmentTakerDTO dto1 = new AssessmentTakerDTO(progressDTO1);
        AssessmentTakerDTO dto2 = new AssessmentTakerDTO(progressDTO2);
        AssessmentTakerDTO dto3 = new AssessmentTakerDTO(progressDTO3);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAssessmentTakerDTO_ToString_ShouldNotReturnNull() {
        AssessmentTakerDTO dto = new AssessmentTakerDTO(new AssessmentProgressDTO());
        assertNotNull(dto.toString());
    }
}