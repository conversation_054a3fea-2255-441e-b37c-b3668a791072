package com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;


class CompletionRateDTOTest {

    @Test
    void testCompletionRateDTO_GetterSetter_ShouldWork() {
        CompletionRateDTO dto = new CompletionRateDTO();

        dto.setCandidateStart(100);
        dto.setCandidateFinished(80);
        dto.setCompletionPercentage(80.0);

        assertEquals(100, dto.getCandidateStart());
        assertEquals(80, dto.getCandidateFinished());
        assertEquals(80.0, dto.getCompletionPercentage());
    }

    @Test
    void testCompletionRateDTO_AllArgsConstructor_ShouldCreateObject() {
        CompletionRateDTO dto = new CompletionRateDTO(100, 80, 80.0);

        assertEquals(100, dto.getCandidateStart());
        assertEquals(80, dto.getCandidateFinished());
        assertEquals(80.0, dto.getCompletionPercentage());
    }

    @Test
    void testCompletionRateDTO_NoArgsConstructor_ShouldCreateObject() {
        CompletionRateDTO dto = new CompletionRateDTO();
        assertNotNull(dto);
    }

    @Test
    void testCompletionRateDTO_EqualsAndHashCode_ShouldWork() {
        CompletionRateDTO dto1 = new CompletionRateDTO(100, 80, 80.0);
        CompletionRateDTO dto2 = new CompletionRateDTO(100, 80, 80.0);
        CompletionRateDTO dto3 = new CompletionRateDTO(120, 90, 75.0);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testCompletionRateDTO_ToString_ShouldNotReturnNull() {
        CompletionRateDTO dto = new CompletionRateDTO(100, 80, 80.0);
        assertNotNull(dto.toString());
    }
}