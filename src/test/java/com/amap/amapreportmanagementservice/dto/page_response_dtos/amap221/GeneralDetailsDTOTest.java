package com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221;

import com.amap.amapreportmanagementservice.dto.AssessmentScoreMetricsDTO;
import com.amap.amapreportmanagementservice.dto.AverageTimesDTO;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class GeneralDetailsDTOTest {

    @Test
    void testGeneralDetailsDTO_GetterSetter_ShouldWork() {
        GeneralDetailsDTO dto = new GeneralDetailsDTO();
        AverageTimesDTO durations = new AverageTimesDTO();
        CompletionRateDTO completionRate = new CompletionRateDTO();
        AssessmentScoreMetricsDTO averageScoreMetrics = new AssessmentScoreMetricsDTO();
        List<Double> percentageScoreDistribution = Arrays.asList(80.0, 90.0, 100.0);

        dto.setDurations(durations);
        dto.setCompletionRate(completionRate);
        dto.setAverageScoreMetrics(averageScoreMetrics);
        dto.setPercentageScoreDistribution(percentageScoreDistribution);

        assertEquals(durations, dto.getDurations());
        assertEquals(completionRate, dto.getCompletionRate());
        assertEquals(averageScoreMetrics, dto.getAverageScoreMetrics());
        assertEquals(percentageScoreDistribution, dto.getPercentageScoreDistribution());
    }

    @Test
    void testGeneralDetailsDTO_AllArgsConstructor_ShouldCreateObject() {
        AverageTimesDTO durations = new AverageTimesDTO();
        CompletionRateDTO completionRate = new CompletionRateDTO();
        AssessmentScoreMetricsDTO averageScoreMetrics = new AssessmentScoreMetricsDTO();
        List<Double> percentageScoreDistribution = Arrays.asList(80.0, 90.0, 100.0);

        GeneralDetailsDTO dto = new GeneralDetailsDTO(durations, completionRate, averageScoreMetrics, percentageScoreDistribution);

        assertEquals(durations, dto.getDurations());
        assertEquals(completionRate, dto.getCompletionRate());
        assertEquals(averageScoreMetrics, dto.getAverageScoreMetrics());
        assertEquals(percentageScoreDistribution, dto.getPercentageScoreDistribution());
    }

    @Test
    void testGeneralDetailsDTO_NoArgsConstructor_ShouldCreateObject() {
        GeneralDetailsDTO dto = new GeneralDetailsDTO();
        assertNotNull(dto);
    }

    @Test
    void testGeneralDetailsDTO_Builder_ShouldCreateObject() {
        AverageTimesDTO durations = new AverageTimesDTO();
        CompletionRateDTO completionRate = new CompletionRateDTO();
        AssessmentScoreMetricsDTO averageScoreMetrics = new AssessmentScoreMetricsDTO();
        List<Double> percentageScoreDistribution = Arrays.asList(80.0, 90.0, 100.0);

        GeneralDetailsDTO dto = GeneralDetailsDTO.builder()
                .durations(durations)
                .completionRate(completionRate)
                .averageScoreMetrics(averageScoreMetrics)
                .percentageScoreDistribution(percentageScoreDistribution)
                .build();

        assertEquals(durations, dto.getDurations());
        assertEquals(completionRate, dto.getCompletionRate());
        assertEquals(averageScoreMetrics, dto.getAverageScoreMetrics());
        assertEquals(percentageScoreDistribution, dto.getPercentageScoreDistribution());
    }

    @Test
    void testGeneralDetailsDTO_EqualsAndHashCode_ShouldWork() {
        AverageTimesDTO durations = new AverageTimesDTO();
        CompletionRateDTO completionRate = new CompletionRateDTO();
        AssessmentScoreMetricsDTO averageScoreMetrics = new AssessmentScoreMetricsDTO();
        List<Double> percentageScoreDistribution = Arrays.asList(80.0, 90.0, 100.0);

        GeneralDetailsDTO dto1 = new GeneralDetailsDTO(durations, completionRate, averageScoreMetrics, percentageScoreDistribution);
        GeneralDetailsDTO dto2 = new GeneralDetailsDTO(durations, completionRate, averageScoreMetrics, percentageScoreDistribution);
        GeneralDetailsDTO dto3 = new GeneralDetailsDTO(new AverageTimesDTO(), new CompletionRateDTO(), new AssessmentScoreMetricsDTO(), Arrays.asList(70.0, 80.0, 90.0));

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testGeneralDetailsDTO_ToString_ShouldNotReturnNull() {
        AverageTimesDTO durations = new AverageTimesDTO();
        CompletionRateDTO completionRate = new CompletionRateDTO();
        AssessmentScoreMetricsDTO averageScoreMetrics = new AssessmentScoreMetricsDTO();
        List<Double> percentageScoreDistribution = Arrays.asList(80.0, 90.0, 100.0);
        GeneralDetailsDTO dto = new GeneralDetailsDTO(durations, completionRate, averageScoreMetrics, percentageScoreDistribution);
        assertNotNull(dto.toString());
    }
}