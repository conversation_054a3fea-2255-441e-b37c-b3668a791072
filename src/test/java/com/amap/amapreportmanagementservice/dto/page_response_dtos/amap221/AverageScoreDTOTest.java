package com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class AverageScoreDTOTest {

    @Test
    void testAverageScoreDTO_GetterSetter_ShouldWork() {
        AverageScoreDTO dto = new AverageScoreDTO();

        dto.setAverageScore(80);
        dto.setHighestScore(95);
        dto.setLowestScore(65);

        assertEquals(80, dto.getAverageScore());
        assertEquals(95, dto.getHighestScore());
        assertEquals(65, dto.getLowestScore());
    }

    @Test
    void testAverageScoreDTO_AllArgsConstructor_ShouldCreateObject() {
        AverageScoreDTO dto = new AverageScoreDTO(80, 95, 65);

        assertEquals(80, dto.getAverageScore());
        assertEquals(95, dto.getHighestScore());
        assertEquals(65, dto.getLowestScore());
    }

    @Test
    void testAverageScoreDTO_NoArgsConstructor_ShouldCreateObject() {
        AverageScoreDTO dto = new AverageScoreDTO();
        assertNotNull(dto);
    }

    @Test
    void testAverageScoreDTO_EqualsAndHashCode_ShouldWork() {
        AverageScoreDTO dto1 = new AverageScoreDTO(80, 95, 65);
        AverageScoreDTO dto2 = new AverageScoreDTO(80, 95, 65);
        AverageScoreDTO dto3 = new AverageScoreDTO(70, 90, 60);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAverageScoreDTO_ToString_ShouldNotReturnNull() {
        AverageScoreDTO dto = new AverageScoreDTO(80, 95, 65);
        assertNotNull(dto.toString());
    }
}