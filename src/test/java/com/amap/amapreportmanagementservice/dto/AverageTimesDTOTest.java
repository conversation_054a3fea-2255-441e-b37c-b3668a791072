package com.amap.amapreportmanagementservice.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class AverageTimesDTOTest {

    @Test
    void testAverageTimesDTO_GetterSetter_ShouldWork() {
        AverageTimesDTO dto = new AverageTimesDTO();
        dto.setTotalCandidate(100L);
        dto.setLongestTime(3600L);
        dto.setShortestTime(600L);
        dto.setAverageTime(1800L);

        assertEquals(100L, dto.getTotalCandidate());
        assertEquals(3600L, dto.getLongestTime());
        assertEquals(600L, dto.getShortestTime());
        assertEquals(1800L, dto.getAverageTime());
    }

    @Test
    void testAverageTimesDTO_AllArgsConstructor_ShouldCreateObject() {
        AverageTimesDTO dto = new AverageTimesDTO(100L, 3600L, 600L, 1800L);

        assertEquals(100L, dto.getTotalCandidate());
        assertEquals(3600L, dto.getLongestTime());
        assertEquals(600L, dto.getShortestTime());
        assertEquals(1800L, dto.getAverageTime());
    }

    @Test
    void testAverageTimesDTO_NoArgsConstructor_ShouldCreateObject() {
        AverageTimesDTO dto = new AverageTimesDTO();
        assertNotNull(dto);
    }

    @Test
    void testAverageTimesDTO_EqualsAndHashCode_ShouldWork() {
        AverageTimesDTO dto1 = new AverageTimesDTO(100L, 3600L, 600L, 1800L);
        AverageTimesDTO dto2 = new AverageTimesDTO(100L, 3600L, 600L, 1800L);
        AverageTimesDTO dto3 = new AverageTimesDTO(200L, 7200L, 1200L, 3600L);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAverageTimesDTO_ToString_ShouldNotReturnNull() {
        AverageTimesDTO dto = new AverageTimesDTO(100L, 3600L, 600L, 1800L);
        assertNotNull(dto.toString());
    }
}