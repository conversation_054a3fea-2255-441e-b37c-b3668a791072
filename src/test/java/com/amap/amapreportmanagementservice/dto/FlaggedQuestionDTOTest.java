package com.amap.amapreportmanagementservice.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class FlaggedQuestionDTOTest {

    @Test
    void testFlaggedQuestionDTO_GetterSetter_ShouldWork() {
        FlaggedQuestionDTO dto = new FlaggedQuestionDTO();
        dto.setQuestionText("What is the capital?");
        dto.setTimesFlagged(5);

        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals(5, dto.getTimesFlagged());
    }

    @Test
    void testFlaggedQuestionDTO_AllArgsConstructor_ShouldCreateObject() {
        FlaggedQuestionDTO dto = new FlaggedQuestionDTO("What is the capital?", 5);

        assertEquals("What is the capital?", dto.getQuestionText());
        assertEquals(5, dto.getTimesFlagged());
    }

    @Test
    void testFlaggedQuestionDTO_NoArgsConstructor_ShouldCreateObject() {
        FlaggedQuestionDTO dto = new FlaggedQuestionDTO();
        assertNotNull(dto);
    }

    @Test
    void testFlaggedQuestionDTO_EqualsAndHashCode_ShouldWork() {
        FlaggedQuestionDTO dto1 = new FlaggedQuestionDTO("Question A", 3);
        FlaggedQuestionDTO dto2 = new FlaggedQuestionDTO("Question A", 3);
        FlaggedQuestionDTO dto3 = new FlaggedQuestionDTO("Question B", 5);

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testFlaggedQuestionDTO_ToString_ShouldNotReturnNull() {
        FlaggedQuestionDTO dto = new FlaggedQuestionDTO("Question A", 3);
        assertNotNull(dto.toString());
    }
}