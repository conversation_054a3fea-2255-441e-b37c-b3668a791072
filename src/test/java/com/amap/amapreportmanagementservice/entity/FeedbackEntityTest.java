package com.amap.amapreportmanagementservice.entity;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class FeedbackEntityTest { // Renamed to FeedbackEntityTest

    @Test
    void feedback_BuilderAndGetterSetter_ShouldWork() {
        Feedback feedback = Feedback.builder()
                .questionText("Question 1")
                .questionAnswer("Answer 1")
                .type("Text")
                .build();

        assertEquals("Question 1", feedback.getQuestionText());
        assertEquals("Answer 1", feedback.getQuestionAnswer());
        assertEquals("Text", feedback.getType());

        feedback.setQuestionText("New Question 1");
        assertEquals("New Question 1", feedback.getQuestionText());
    }

    @Test
    void feedback_NoArgsConstructor_ShouldCreateObject() {
        Feedback feedback = new Feedback();
        assertNotNull(feedback);
    }

    @Test
    void feedback_AllArgsConstructor_ShouldCreateObject() {
        Feedback feedback = new Feedback("Question 1", "Answer 1", "Text");

        assertEquals("Question 1", feedback.getQuestionText());
        assertEquals("Answer 1", feedback.getQuestionAnswer());
        assertEquals("Text", feedback.getType());
    }

    @Test
    void feedback_EqualsAndHashCode_ShouldWork() {
        Feedback feedback1 = Feedback.builder()
                .questionText("Question 1")
                .questionAnswer("Answer 1")
                .build();

        Feedback feedback2 = Feedback.builder()
                .questionText("Question 1")
                .questionAnswer("Answer 1")
                .build();

        Feedback feedback3 = Feedback.builder()
                .questionText("Question 2")
                .questionAnswer("Answer 1")
                .build();

        assertEquals(feedback1, feedback2);
        assertEquals(feedback1.hashCode(), feedback2.hashCode());
        assertNotEquals(feedback1, feedback3);
        assertNotEquals(feedback1.hashCode(), feedback3.hashCode());
    }

    @Test
    void feedback_ToString_ShouldNotReturnNull() {
        Feedback feedback = Feedback.builder()
                .questionText("Question 1")
                .build();
        assertNotNull(feedback.toString());
    }
}