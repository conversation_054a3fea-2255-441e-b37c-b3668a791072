package com.amap.amapreportmanagementservice.entity;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class CandidateAssessmentEntityTest { // Renamed to CandidateAssessmentEntityTest

    @Test
    void candidateAssessment_BuilderAndGetterSetter_ShouldWork() {
        CandidateAssessment candidateAssessment = CandidateAssessment.builder()
                .candidateId("candidate123")
                .title("Java Assessment")
                .candidateEmail("<EMAIL>")
                .assessmentId("assess456")
                .timeTaken(60)
                .status("Completed")
                .totalScore(100.0f)
                .candidateMarks(85.0)
                .assessmentStartTime("2024-01-01T10:00:00")
                .assessmentEndTime("2024-01-01T11:00:00")
                .scorePercentage(85.0)
                .proctor("John Doe")
                .proctorLevel("Senior")
                .integrityScore(90)
                .duration(120)
                .assessmentWindowViolationCount(2)
                .assessmentWindowViolationDuration(30)
                .assessmentTakerShotCount(100)
                .assessmentTakerViolationShotCount(5)
                .windowShotCount(50)
                .windowViolationShotCount(2)
                .assessmentDuration(120)
                .screenshotsInterval(10)
                .camerashotsInterval(15)
                .reportCallbackURL("http://example.com/callback")
                .build();

        assertEquals("candidate123", candidateAssessment.getCandidateId());
        assertEquals("Java Assessment", candidateAssessment.getTitle());
        assertEquals("<EMAIL>", candidateAssessment.getCandidateEmail());
        assertEquals("assess456", candidateAssessment.getAssessmentId());
        assertEquals(60, candidateAssessment.getTimeTaken());
        assertEquals("Completed", candidateAssessment.getStatus());
        assertEquals(100.0f, candidateAssessment.getTotalScore());
        assertEquals(85.0, candidateAssessment.getCandidateMarks());
        assertEquals("2024-01-01T10:00:00", candidateAssessment.getAssessmentStartTime());
        assertEquals("2024-01-01T11:00:00", candidateAssessment.getAssessmentEndTime());
        assertEquals(85.0, candidateAssessment.getScorePercentage());
        assertEquals("John Doe", candidateAssessment.getProctor());
        assertEquals("Senior", candidateAssessment.getProctorLevel());
        assertEquals(90, candidateAssessment.getIntegrityScore());
        assertEquals(120, candidateAssessment.getDuration());
        assertEquals(2, candidateAssessment.getAssessmentWindowViolationCount());
        assertEquals(30, candidateAssessment.getAssessmentWindowViolationDuration());
        assertEquals(100, candidateAssessment.getAssessmentTakerShotCount());
        assertEquals(5, candidateAssessment.getAssessmentTakerViolationShotCount());
        assertEquals(50, candidateAssessment.getWindowShotCount());
        assertEquals(2, candidateAssessment.getWindowViolationShotCount());
        assertEquals(120, candidateAssessment.getAssessmentDuration());
        assertEquals(10, candidateAssessment.getScreenshotsInterval());
        assertEquals(15, candidateAssessment.getCamerashotsInterval());
        assertEquals("http://example.com/callback", candidateAssessment.getReportCallbackURL());

        candidateAssessment.setCandidateId("newCandidate123");
        assertEquals("newCandidate123", candidateAssessment.getCandidateId());
    }

    @Test
    void candidateAssessment_NoArgsConstructor_ShouldCreateObject() {
        CandidateAssessment candidateAssessment = new CandidateAssessment();
        assertNotNull(candidateAssessment);
    }

    @Test
    void candidateAssessment_AllArgsConstructor_ShouldCreateObject() {
        CandidateAssessment candidateAssessment = new CandidateAssessment("candidate123", "Java Assessment", "<EMAIL>", "assess456", 60, "Completed", 100.0f, 85.0, "2024-01-01T10:00:00", "2024-01-01T11:00:00", 85.0, "John Doe", "Senior", 90, 120, 2, 30, 100, 5, 50, 2, 120, 10, 15, "http://example.com/callback");

        assertEquals("candidate123", candidateAssessment.getCandidateId());
        assertEquals("Java Assessment", candidateAssessment.getTitle());
        assertEquals("<EMAIL>", candidateAssessment.getCandidateEmail());
        assertEquals("assess456", candidateAssessment.getAssessmentId());
        assertEquals(60, candidateAssessment.getTimeTaken());
        assertEquals("Completed", candidateAssessment.getStatus());
        assertEquals(100.0f, candidateAssessment.getTotalScore());
        assertEquals(85.0, candidateAssessment.getCandidateMarks());
        assertEquals("2024-01-01T10:00:00", candidateAssessment.getAssessmentStartTime());
        assertEquals("2024-01-01T11:00:00", candidateAssessment.getAssessmentEndTime());
        assertEquals(85.0, candidateAssessment.getScorePercentage());
        assertEquals("John Doe", candidateAssessment.getProctor());
        assertEquals("Senior", candidateAssessment.getProctorLevel());
        assertEquals(90, candidateAssessment.getIntegrityScore());
        assertEquals(120, candidateAssessment.getDuration());
        assertEquals(2, candidateAssessment.getAssessmentWindowViolationCount());
        assertEquals(30, candidateAssessment.getAssessmentWindowViolationDuration());
        assertEquals(100, candidateAssessment.getAssessmentTakerShotCount());
        assertEquals(5, candidateAssessment.getAssessmentTakerViolationShotCount());
        assertEquals(50, candidateAssessment.getWindowShotCount());
        assertEquals(2, candidateAssessment.getWindowViolationShotCount());
        assertEquals(120, candidateAssessment.getAssessmentDuration());
        assertEquals(10, candidateAssessment.getScreenshotsInterval());
        assertEquals(15, candidateAssessment.getCamerashotsInterval());
        assertEquals("http://example.com/callback", candidateAssessment.getReportCallbackURL());
    }

    @Test
    void candidateAssessment_EqualsAndHashCode_ShouldWork() {
        CandidateAssessment assessment1 = CandidateAssessment.builder()
                .candidateId("candidate123")
                .assessmentId("assess456")
                .build();

        CandidateAssessment assessment2 = CandidateAssessment.builder()
                .candidateId("candidate123")
                .assessmentId("assess456")
                .build();

        CandidateAssessment assessment3 = CandidateAssessment.builder()
                .candidateId("candidate789")
                .assessmentId("assess456")
                .build();

        assertEquals(assessment1, assessment2);
        assertEquals(assessment1.hashCode(), assessment2.hashCode());
        assertNotEquals(assessment1, assessment3);
        assertNotEquals(assessment1.hashCode(), assessment3.hashCode());
    }

    @Test
    void candidateAssessment_ToString_ShouldNotReturnNull() {
        CandidateAssessment candidateAssessment = CandidateAssessment.builder()
                .candidateId("candidate123")
                .assessmentId("assess456")
                .build();
        assertNotNull(candidateAssessment.toString());
    }
}