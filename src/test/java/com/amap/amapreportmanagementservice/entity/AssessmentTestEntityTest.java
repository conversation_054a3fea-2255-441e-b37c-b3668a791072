package com.amap.amapreportmanagementservice.entity;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class AssessmentTestEntityTest { // Renamed to AssessmentTestEntityTest

    @Test
    void assessmentTest_BuilderAndGetterSetter_ShouldWork() {
        List<String> questionIds = Arrays.asList("q1", "q2", "q3");

        AssessmentTest assessmentTest = AssessmentTest.builder()
                .assessmentId("assess123")
                .title("Math Test")
                .testId("test456")
                .domain("Mathematics")
                .totalScore(100)
                .averageScore(75.5)
                .duration(120)
                .passage("This is a math test passage.")
                .totalCandidateMarks(80.0)
                .totalCandidatePercentage(80.0)
                .averagePercentageScore(78.0)
                .numberOfTimesAnswered(50)
                .numberOfFlags(5)
                .questionIds(questionIds)
                .build();

        assertEquals("assess123", assessmentTest.getAssessmentId());
        assertEquals("Math Test", assessmentTest.getTitle());
        assertEquals("test456", assessmentTest.getTestId());
        assertEquals("Mathematics", assessmentTest.getDomain());
        assertEquals(100, assessmentTest.getTotalScore());
        assertEquals(75.5, assessmentTest.getAverageScore());
        assertEquals(120, assessmentTest.getDuration());
        assertEquals("This is a math test passage.", assessmentTest.getPassage());
        assertEquals(80.0, assessmentTest.getTotalCandidateMarks());
        assertEquals(80.0, assessmentTest.getTotalCandidatePercentage());
        assertEquals(78.0, assessmentTest.getAveragePercentageScore());
        assertEquals(50, assessmentTest.getNumberOfTimesAnswered());
        assertEquals(5, assessmentTest.getNumberOfFlags());
        assertEquals(questionIds, assessmentTest.getQuestionIds());

        assessmentTest.setAssessmentId("newAssess123");
        assertEquals("newAssess123", assessmentTest.getAssessmentId());
    }

    @Test
    void assessmentTest_NoArgsConstructor_ShouldCreateObject() {
        AssessmentTest assessmentTest = new AssessmentTest();
        assertNotNull(assessmentTest);
    }

    @Test
    void assessmentTest_AllArgsConstructor_ShouldCreateObject() {
        List<String> questionIds = Arrays.asList("q1", "q2", "q3");

        AssessmentTest assessmentTest = new AssessmentTest("assess123", "Math Test", "test456", "Mathematics", 100, 75.5, 120, "This is a math test passage.", 80.0, 80.0, 78.0, 50, 5, questionIds);

        assertEquals("assess123", assessmentTest.getAssessmentId());
        assertEquals("Math Test", assessmentTest.getTitle());
        assertEquals("test456", assessmentTest.getTestId());
        assertEquals("Mathematics", assessmentTest.getDomain());
        assertEquals(100, assessmentTest.getTotalScore());
        assertEquals(75.5, assessmentTest.getAverageScore());
        assertEquals(120, assessmentTest.getDuration());
        assertEquals("This is a math test passage.", assessmentTest.getPassage());
        assertEquals(80.0, assessmentTest.getTotalCandidateMarks());
        assertEquals(80.0, assessmentTest.getTotalCandidatePercentage());
        assertEquals(78.0, assessmentTest.getAveragePercentageScore());
        assertEquals(50, assessmentTest.getNumberOfTimesAnswered());
        assertEquals(5, assessmentTest.getNumberOfFlags());
        assertEquals(questionIds, assessmentTest.getQuestionIds());
    }

    @Test
    void assessmentTest_EqualsAndHashCode_ShouldWork() {
        List<String> questionIds1 = Arrays.asList("q1", "q2", "q3");
        List<String> questionIds2 = Arrays.asList("q1", "q2", "q3");

        AssessmentTest assessmentTest1 = AssessmentTest.builder()
                .assessmentId("assess123")
                .testId("test456")
                .questionIds(questionIds1)
                .build();

        AssessmentTest assessmentTest2 = AssessmentTest.builder()
                .assessmentId("assess123")
                .testId("test456")
                .questionIds(questionIds2)
                .build();

        AssessmentTest assessmentTest3 = AssessmentTest.builder()
                .assessmentId("assess789")
                .testId("test456")
                .questionIds(questionIds1)
                .build();

        assertEquals(assessmentTest1, assessmentTest2);
        assertEquals(assessmentTest1.hashCode(), assessmentTest2.hashCode());
        assertNotEquals(assessmentTest1, assessmentTest3);
        assertNotEquals(assessmentTest1.hashCode(), assessmentTest3.hashCode());
    }

    @Test
    void assessmentTest_ToString_ShouldNotReturnNull() {
        AssessmentTest assessmentTest = AssessmentTest.builder()
                .assessmentId("assess123")
                .testId("test456")
                .build();
        assertNotNull(assessmentTest.toString());
    }
}