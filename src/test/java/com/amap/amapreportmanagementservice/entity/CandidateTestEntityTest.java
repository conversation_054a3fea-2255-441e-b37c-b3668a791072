package com.amap.amapreportmanagementservice.entity;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CandidateTestEntityTest { // Renamed to CandidateTestEntityTest

    @Test
    void candidateTest_BuilderAndGetterSetter_ShouldWork() {
        List<String> questionIDs = Arrays.asList("q1", "q2", "q3");

        CandidateTest candidateTest = CandidateTest.builder()
                .candidateId("candidate123")
                .candidateEmail("<EMAIL>")
                .testId("test456")
                .totalScore(100)
                .domain("Math")
                .candidateMarks(85.0)
                .scorePercentage(85.0)
                .passage("This is a math test passage.")
                .timeTaken(60)
                .numberOfQuestionsFailed(5)
                .numberOfQuestionsPassed(15)
                .numberOfQuestionsAnswered(20)
                .numberOfQuestions(20)
                .testWindowViolationDuration(30)
                .testWindowViolationCount(2)
                .testTakerShotCount(100)
                .testTakerViolationShotCount(5)
                .testWindowShotCount(50)
                .testWindowViolationShotCount(2)
                .status("Completed")
                .passStatus("Passed")
                .questionIDs(questionIDs)
                .build();

        assertEquals("candidate123", candidateTest.getCandidateId());
        assertEquals("<EMAIL>", candidateTest.getCandidateEmail());
        assertEquals("test456", candidateTest.getTestId());
        assertEquals(100, candidateTest.getTotalScore());
        assertEquals("Math", candidateTest.getDomain());
        assertEquals(85.0, candidateTest.getCandidateMarks());
        assertEquals(85.0, candidateTest.getScorePercentage());
        assertEquals("This is a math test passage.", candidateTest.getPassage());
        assertEquals(60, candidateTest.getTimeTaken());
        assertEquals(5, candidateTest.getNumberOfQuestionsFailed());
        assertEquals(15, candidateTest.getNumberOfQuestionsPassed());
        assertEquals(20, candidateTest.getNumberOfQuestionsAnswered());
        assertEquals(20, candidateTest.getNumberOfQuestions());
        assertEquals(30, candidateTest.getTestWindowViolationDuration());
        assertEquals(2, candidateTest.getTestWindowViolationCount());
        assertEquals(100, candidateTest.getTestTakerShotCount());
        assertEquals(5, candidateTest.getTestTakerViolationShotCount());
        assertEquals(50, candidateTest.getTestWindowShotCount());
        assertEquals(2, candidateTest.getTestWindowViolationShotCount());
        assertEquals("Completed", candidateTest.getStatus());
        assertEquals("Passed", candidateTest.getPassStatus());
        assertEquals(questionIDs, candidateTest.getQuestionIDs());

        candidateTest.setCandidateId("newCandidate123");
        assertEquals("newCandidate123", candidateTest.getCandidateId());
    }

    @Test
    void candidateTest_NoArgsConstructor_ShouldCreateObject() {
        CandidateTest candidateTest = new CandidateTest();
        assertNotNull(candidateTest);
    }

    @Test
    void candidateTest_AllArgsConstructor_ShouldCreateObject() {
        List<String> questionIDs = Arrays.asList("q1", "q2", "q3");

        CandidateTest candidateTest = new CandidateTest(
                "candidate123", "<EMAIL>", "test456", 100, "Math", 85.0, 85.0,
                "This is a math test passage.", 60, 5, 15, 20, 20, 30, 2, 100, 5, 50, 2, "Completed", "Passed", questionIDs
        );

        assertEquals("candidate123", candidateTest.getCandidateId());
        assertEquals("<EMAIL>", candidateTest.getCandidateEmail());
        assertEquals("test456", candidateTest.getTestId());
        assertEquals(100, candidateTest.getTotalScore());
        assertEquals("Math", candidateTest.getDomain());
        assertEquals(85.0, candidateTest.getCandidateMarks());
        assertEquals(85.0, candidateTest.getScorePercentage());
        assertEquals("This is a math test passage.", candidateTest.getPassage());
        assertEquals(60, candidateTest.getTimeTaken());
        assertEquals(5, candidateTest.getNumberOfQuestionsFailed());
        assertEquals(15, candidateTest.getNumberOfQuestionsPassed());
        assertEquals(20, candidateTest.getNumberOfQuestionsAnswered());
        assertEquals(20, candidateTest.getNumberOfQuestions());
        assertEquals(30, candidateTest.getTestWindowViolationDuration());
        assertEquals(2, candidateTest.getTestWindowViolationCount());
        assertEquals(100, candidateTest.getTestTakerShotCount());
        assertEquals(5, candidateTest.getTestTakerViolationShotCount());
        assertEquals(50, candidateTest.getTestWindowShotCount());
        assertEquals(2, candidateTest.getTestWindowViolationShotCount());
        assertEquals("Completed", candidateTest.getStatus());
        assertEquals("Passed", candidateTest.getPassStatus());
        assertEquals(questionIDs, candidateTest.getQuestionIDs());
    }

    @Test
    void candidateTest_EqualsAndHashCode_ShouldWork() {
        CandidateTest test1 = CandidateTest.builder()
                .candidateId("candidate123")
                .testId("test456")
                .build();

        CandidateTest test2 = CandidateTest.builder()
                .candidateId("candidate123")
                .testId("test456")
                .build();

        CandidateTest test3 = CandidateTest.builder()
                .candidateId("candidate789")
                .testId("test456")
                .build();

        assertEquals(test1, test2);
        assertEquals(test1.hashCode(), test2.hashCode());
        assertNotEquals(test1, test3);
        assertNotEquals(test1.hashCode(), test3.hashCode());
    }

    @Test
    void candidateTest_ToString_ShouldNotReturnNull() {
        CandidateTest candidateTest = CandidateTest.builder()
                .candidateId("candidate123")
                .testId("test456")
                .build();
        assertNotNull(candidateTest.toString());
    }
}