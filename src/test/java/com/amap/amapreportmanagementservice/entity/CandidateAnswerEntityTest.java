package com.amap.amapreportmanagementservice.entity;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CandidateAnswerEntityTest {

    @Test
    void candidateAnswer_BuilderAndGetterSetter_ShouldWork() {
        List<String> answers = Arrays.asList("answer1", "answer2", "answer3");

        CandidateAnswer candidateAnswer = CandidateAnswer.builder()
                .questionId("question123")
                .testTakerAnswer(answers)
                .build();

        assertEquals("question123", candidateAnswer.getQuestionId());
        assertEquals(answers, candidateAnswer.getTestTakerAnswer());

        candidateAnswer.setQuestionId("newQuestion123");
        assertEquals("newQuestion123", candidateAnswer.getQuestionId());
    }

    @Test
    void candidateAnswer_NoArgsConstructor_ShouldCreateObject() {
        CandidateAnswer candidateAnswer = new CandidateAnswer();
        assertNotNull(candidateAnswer);
    }

    @Test
    void candidateAnswer_AllArgsConstructor_ShouldCreateObject() {
        List<String> answers = Arrays.asList("answer1", "answer2", "answer3");

        CandidateAnswer candidateAnswer = new CandidateAnswer("question123", answers);

        assertEquals("question123", candidateAnswer.getQuestionId());
        assertEquals(answers, candidateAnswer.getTestTakerAnswer());
    }

    @Test
    void candidateAnswer_EqualsAndHashCode_ShouldWork() {
        List<String> answers1 = Arrays.asList("answer1", "answer2");
        List<String> answers2 = Arrays.asList("answer1", "answer2");

        CandidateAnswer candidateAnswer1 = CandidateAnswer.builder()
                .questionId("question123")
                .testTakerAnswer(answers1)
                .build();

        CandidateAnswer candidateAnswer2 = CandidateAnswer.builder()
                .questionId("question123")
                .testTakerAnswer(answers2)
                .build();

        CandidateAnswer candidateAnswer3 = CandidateAnswer.builder()
                .questionId("question456")
                .testTakerAnswer(answers1)
                .build();

        assertEquals(candidateAnswer1, candidateAnswer2);
        assertEquals(candidateAnswer1.hashCode(), candidateAnswer2.hashCode());
        assertNotEquals(candidateAnswer1, candidateAnswer3);
        assertNotEquals(candidateAnswer1.hashCode(), candidateAnswer3.hashCode());
    }

    @Test
    void candidateAnswer_ToString_ShouldNotReturnNull() {
        CandidateAnswer candidateAnswer = CandidateAnswer.builder()
                .questionId("question123")
                .build();
        assertNotNull(candidateAnswer.toString());
    }
}