package com.amap.amapreportmanagementservice.entity;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class WebhookReportJobEntityTest { // Renamed to WebhookReportJobEntityTest

    @Test
    void webhookReportJob_BuilderAndGetterSetter_ShouldWork() {
        WebhookReportJob webhookReportJob = WebhookReportJob.builder()
                .candidateId("candidate123")
                .assessmentId("assess456")
                .email("<EMAIL>")
                .reportCallbackURL("http://example.com/callback")
                .trails(3)
                .build();

        assertEquals("candidate123", webhookReportJob.getCandidateId());
        assertEquals("assess456", webhookReportJob.getAssessmentId());
        assertEquals("<EMAIL>", webhookReportJob.getEmail());
        assertEquals("http://example.com/callback", webhookReportJob.getReportCallbackURL());
        assertEquals(3, webhookReportJob.getTrails());

        webhookReportJob.setCandidateId("newCandidate123");
        assertEquals("newCandidate123", webhookReportJob.getCandidateId());
    }

    @Test
    void webhookReportJob_NoArgsConstructor_ShouldCreateObject() {
        WebhookReportJob webhookReportJob = new WebhookReportJob();
        assertNotNull(webhookReportJob);
    }

    @Test
    void webhookReportJob_AllArgsConstructor_ShouldCreateObject() {
        WebhookReportJob webhookReportJob = new WebhookReportJob(
                "candidate123", "assess456", "<EMAIL>", "http://example.com/callback", 3
        );

        assertEquals("candidate123", webhookReportJob.getCandidateId());
        assertEquals("assess456", webhookReportJob.getAssessmentId());
        assertEquals("<EMAIL>", webhookReportJob.getEmail());
        assertEquals("http://example.com/callback", webhookReportJob.getReportCallbackURL());
        assertEquals(3, webhookReportJob.getTrails());
    }

    @Test
    void webhookReportJob_EqualsAndHashCode_ShouldWork() {
        WebhookReportJob job1 = WebhookReportJob.builder()
                .candidateId("candidate123")
                .assessmentId("assess456")
                .email("<EMAIL>")
                .reportCallbackURL("http://example.com/callback")
                .trails(3)
                .build();

        WebhookReportJob job2 = WebhookReportJob.builder()
                .candidateId("candidate123")
                .assessmentId("assess456")
                .email("<EMAIL>")
                .reportCallbackURL("http://example.com/callback")
                .trails(3)
                .build();

        WebhookReportJob job3 = WebhookReportJob.builder()
                .candidateId("candidate789")
                .assessmentId("assess456")
                .email("<EMAIL>")
                .reportCallbackURL("http://example.com/callback")
                .trails(3)
                .build();

        assertEquals(job1, job2);
        assertEquals(job1.hashCode(), job2.hashCode());
        assertNotEquals(job1, job3);
        assertNotEquals(job1.hashCode(), job3.hashCode());
    }

    @Test
    void webhookReportJob_ToString_ShouldNotReturnNull() {
        WebhookReportJob webhookReportJob = WebhookReportJob.builder()
                .candidateId("candidate123")
                .assessmentId("assess456")
                .email("<EMAIL>")
                .reportCallbackURL("http://example.com/callback")
                .build();
        assertNotNull(webhookReportJob.toString());
    }

    @Test
    void webhookReportJob_NonNullFields_ShouldWork() {
        assertThrows(NullPointerException.class, () -> WebhookReportJob.builder().build());
        assertThrows(NullPointerException.class, () -> WebhookReportJob.builder().candidateId("test").build());
        assertThrows(NullPointerException.class, () -> WebhookReportJob.builder().candidateId("test").assessmentId("test").build());
        assertThrows(NullPointerException.class, () -> WebhookReportJob.builder().candidateId("test").assessmentId("test").email("test").build());

        WebhookReportJob job = WebhookReportJob.builder().candidateId("test").assessmentId("test").email("test").reportCallbackURL("test").build();
        assertNotNull(job);
    }
}