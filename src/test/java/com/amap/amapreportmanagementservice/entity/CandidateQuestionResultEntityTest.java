package com.amap.amapreportmanagementservice.entity;

import com.amap.amapreportmanagementservice.dto.progress.CodeConstraintsDTO;
import com.amap.amapreportmanagementservice.dto.progress.CodeExecutionSummaryDTO;
import com.amap.amapreportmanagementservice.dto.results.CodeResultDTO;
import com.amap.amapreportmanagementservice.dto.results.CodeTemplateDTO;
import org.junit.jupiter.api.Test;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

class CandidateQuestionResultEntityTest { // Renamed to CandidateQuestionResultEntityTest

    @Test
    void candidateQuestionResult_BuilderAndGetterSetter_ShouldWork() {
        List<String> testTakerAnswers = Arrays.asList("answer1", "answer2");
        List<String> optionAnswers = Arrays.asList("optionA", "optionB");
        List<String> correctAnswers = Arrays.asList("correct1", "correct2");

        CandidateQuestionResult candidateQuestionResult = CandidateQuestionResult.builder()
                .candidateId("candidate123")
                .questionId("question456")
                .candidateEmail("<EMAIL>")
                .questionText("What is the capital of France?")
                .domain("Geography")
                .category("Capitals")
                .questionType("Multiple Choice")
                .totalScore(10)
                .candidateMarks(8.0)
                .difficultyLevel("Medium")
                .timeLimit(60)
                .isFlagged("Yes")
                .testTakerAnswers(testTakerAnswers)
                .optionAnswers(optionAnswers)
                .correctAnswers(correctAnswers)
                .isComprehension(false)
                .isAnswerCorrect("CORRECT")
                .essayRubrics("Good essay")
                .build();

        assertEquals("candidate123", candidateQuestionResult.getCandidateId());
        assertEquals("question456", candidateQuestionResult.getQuestionId());
        assertEquals("<EMAIL>", candidateQuestionResult.getCandidateEmail());
        assertEquals("What is the capital of France?", candidateQuestionResult.getQuestionText());
        assertEquals("Geography", candidateQuestionResult.getDomain());
        assertEquals("Capitals", candidateQuestionResult.getCategory());
        assertEquals("Multiple Choice", candidateQuestionResult.getQuestionType());
        assertEquals(10, candidateQuestionResult.getTotalScore());
        assertEquals(8.0, candidateQuestionResult.getCandidateMarks());
        assertEquals("Medium", candidateQuestionResult.getDifficultyLevel());
        assertEquals(60, candidateQuestionResult.getTimeLimit());
        assertEquals("Yes", candidateQuestionResult.getIsFlagged());
        assertEquals(testTakerAnswers, candidateQuestionResult.getTestTakerAnswers());
        assertEquals(optionAnswers, candidateQuestionResult.getOptionAnswers());
        assertEquals(correctAnswers, candidateQuestionResult.getCorrectAnswers());
        assertFalse(candidateQuestionResult.isComprehension());
        assertEquals("CORRECT",(candidateQuestionResult.getIsAnswerCorrect()));
        assertEquals("Good essay", candidateQuestionResult.getEssayRubrics());

        candidateQuestionResult.setCandidateId("newCandidate123");
        assertEquals("newCandidate123", candidateQuestionResult.getCandidateId());
    }

    @Test
    void candidateQuestionResult_NoArgsConstructor_ShouldCreateObject() {
        CandidateQuestionResult candidateQuestionResult = new CandidateQuestionResult();
        assertNotNull(candidateQuestionResult);
    }

    @Test
    void candidateQuestionResult_AllArgsConstructor_ShouldCreateObject() {
        // Arrange: Setup test data
        List<String> testTakerAnswers = Arrays.asList("answer1", "answer2");
        List<String> optionAnswers = Arrays.asList("optionA", "optionB");
        List<String> correctAnswers = Arrays.asList("correct1", "correct2");
        CodeExecutionSummary codeExecutionSummary = new CodeExecutionSummary(); // Use actual data if needed for deeper tests
        CodeConstraint codeConstraints = new CodeConstraint(); // Use actual data if needed
        List<CodeTemplate> codeTemplate = new ArrayList<>(); // Use actual data if needed
        List<CodeResult> codeResult = new ArrayList<>(); // Use actual data if needed
        List<ReferenceSolution> referenceSolution = new ArrayList<>();
        String expectedIsAnswerCorrect = "CORRECT"; // Define the expected value for the new field

        // Act: Create the object using the AllArgsConstructor
        CandidateQuestionResult candidateQuestionResult = new CandidateQuestionResult(
                "candidate123",         // candidateId
                "question456",          // questionId
                "<EMAIL>",     // candidateEmail
                "What is the capital of France?", // questionText
                "Geography",            // domain
                "Capitals",             // category
                "Multiple Choice",      // questionType
                10,                     // totalScore
                8.0,                    // candidateMarks
                "Medium",               // difficultyLevel
                60,                     // timeLimit
                "Yes",                  // isFlagged
                testTakerAnswers,       // testTakerAnswers
                optionAnswers,          // optionAnswers
                correctAnswers,         // correctAnswers
                false,
                true,                   // isAnswered
                "CORRECT",                  // isComprehension
                "Good essay",            // essayRubrics
                "codeReview",
                codeTemplate,
                codeResult,
                codeExecutionSummary,
                codeConstraints,
                referenceSolution
        );

        // Assert: Verify all fields are set correctly
        assertEquals("candidate123", candidateQuestionResult.getCandidateId());
        assertEquals("question456", candidateQuestionResult.getQuestionId());
        assertEquals("<EMAIL>", candidateQuestionResult.getCandidateEmail());
        assertEquals("What is the capital of France?", candidateQuestionResult.getQuestionText());
        assertEquals("Geography", candidateQuestionResult.getDomain());
        assertEquals("Capitals", candidateQuestionResult.getCategory());
        assertEquals("Multiple Choice", candidateQuestionResult.getQuestionType());
        assertEquals(10, candidateQuestionResult.getTotalScore());
        assertEquals(8.0, candidateQuestionResult.getCandidateMarks());
        assertEquals("Medium", candidateQuestionResult.getDifficultyLevel());
        assertEquals(60, candidateQuestionResult.getTimeLimit());
        assertEquals("Yes", candidateQuestionResult.getIsFlagged());
        assertEquals(testTakerAnswers, candidateQuestionResult.getTestTakerAnswers());
        assertEquals(optionAnswers, candidateQuestionResult.getOptionAnswers());
        assertEquals(correctAnswers, candidateQuestionResult.getCorrectAnswers());
        assertEquals(codeTemplate, candidateQuestionResult.getCodeTemplates());
        assertEquals(codeConstraints, candidateQuestionResult.getCodeConstraint());
        assertTrue(candidateQuestionResult.isAnswered());
        assertEquals(codeResult, candidateQuestionResult.getCodeResults());
        assertEquals(expectedIsAnswerCorrect, candidateQuestionResult.getIsAnswerCorrect()); // <-- Added assertion for the new field
        assertEquals(codeExecutionSummary, candidateQuestionResult.getCodeExecutionSummary());
        assertFalse(candidateQuestionResult.isComprehension());
        assertEquals("Good essay", candidateQuestionResult.getEssayRubrics());
    }
    @Test
    void candidateQuestionResult_EqualsAndHashCode_ShouldWork() {
        CandidateQuestionResult result1 = CandidateQuestionResult.builder()
                .candidateId("candidate123")
                .questionId("question456")
                .build();

        CandidateQuestionResult result2 = CandidateQuestionResult.builder()
                .candidateId("candidate123")
                .questionId("question456")
                .build();

        CandidateQuestionResult result3 = CandidateQuestionResult.builder()
                .candidateId("candidate789")
                .questionId("question456")
                .build();

        assertEquals(result1, result2);
        assertEquals(result1.hashCode(), result2.hashCode());
        assertNotEquals(result1, result3);
        assertNotEquals(result1.hashCode(), result3.hashCode());
    }

    @Test
    void candidateQuestionResult_ToString_ShouldNotReturnNull() {
        CandidateQuestionResult candidateQuestionResult = CandidateQuestionResult.builder()
                .candidateId("candidate123")
                .questionId("question456")
                .build();
        assertNotNull(candidateQuestionResult.toString());
    }
}