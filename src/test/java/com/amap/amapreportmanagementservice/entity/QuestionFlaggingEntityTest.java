package com.amap.amapreportmanagementservice.entity;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class QuestionFlaggingEntityTest { // Renamed to QuestionFlaggingEntityTest

    @Test
    void questionFlagging_BuilderAndGetterSetter_ShouldWork() {
        List<String> reasons = Arrays.asList("Reason 1", "Reason 2");

        QuestionFlagging questionFlagging = QuestionFlagging.builder()
                .questionId("question123")
                .testId("test456")
                .assessmentId("assess789")
                .testTakerId("taker101")
                .questionText("What is the capital?")
                .testTakerEmail("<EMAIL>")
                .reasonOfFlagging(reasons)
                .build();

        assertEquals("question123", questionFlagging.getQuestionId());
        assertEquals("test456", questionFlagging.getTestId());
        assertEquals("assess789", questionFlagging.getAssessmentId());
        assertEquals("taker101", questionFlagging.getTestTakerId());
        assertEquals("What is the capital?", questionFlagging.getQuestionText());
        assertEquals("<EMAIL>", questionFlagging.getTestTakerEmail());
        assertEquals(reasons, questionFlagging.getReasonOfFlagging());

        questionFlagging.setQuestionId("newQuestion123");
        assertEquals("newQuestion123", questionFlagging.getQuestionId());
    }

    @Test
    void questionFlagging_NoArgsConstructor_ShouldCreateObject() {
        QuestionFlagging questionFlagging = new QuestionFlagging();
        assertNotNull(questionFlagging);
    }

    @Test
    void questionFlagging_AllArgsConstructor_ShouldCreateObject() {
        List<String> reasons = Arrays.asList("Reason 1", "Reason 2");

        QuestionFlagging questionFlagging = new QuestionFlagging(
                "question123", "test456", "assess789", "taker101", "What is the capital?", "<EMAIL>", reasons
        );

        assertEquals("question123", questionFlagging.getQuestionId());
        assertEquals("test456", questionFlagging.getTestId());
        assertEquals("assess789", questionFlagging.getAssessmentId());
        assertEquals("taker101", questionFlagging.getTestTakerId());
        assertEquals("What is the capital?", questionFlagging.getQuestionText());
        assertEquals("<EMAIL>", questionFlagging.getTestTakerEmail());
        assertEquals(reasons, questionFlagging.getReasonOfFlagging());
    }

    @Test
    void questionFlagging_EqualsAndHashCode_ShouldWork() {
        List<String> reasons1 = Arrays.asList("Reason 1");
        List<String> reasons2 = Arrays.asList("Reason 1");

        QuestionFlagging flagging1 = QuestionFlagging.builder()
                .questionId("question123")
                .testId("test456")
                .assessmentId("assess789")
                .testTakerId("taker101")
                .reasonOfFlagging(reasons1)
                .build();

        QuestionFlagging flagging2 = QuestionFlagging.builder()
                .questionId("question123")
                .testId("test456")
                .assessmentId("assess789")
                .testTakerId("taker101")
                .reasonOfFlagging(reasons2)
                .build();

        QuestionFlagging flagging3 = QuestionFlagging.builder()
                .questionId("question789")
                .testId("test456")
                .assessmentId("assess789")
                .testTakerId("taker101")
                .reasonOfFlagging(reasons1)
                .build();

        assertEquals(flagging1, flagging2);
        assertEquals(flagging1.hashCode(), flagging2.hashCode());
        assertNotEquals(flagging1, flagging3);
        assertNotEquals(flagging1.hashCode(), flagging3.hashCode());
    }

    @Test
    void questionFlagging_ToString_ShouldNotReturnNull() {
        QuestionFlagging questionFlagging = QuestionFlagging.builder()
                .assessmentId("assess789")
                .testTakerId("taker101")
                .questionId("question123")
                .testId("test456")
                .build();
        assertNotNull(questionFlagging.toString());
    }
}