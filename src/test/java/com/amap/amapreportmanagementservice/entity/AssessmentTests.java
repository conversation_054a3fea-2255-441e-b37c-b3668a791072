package com.amap.amapreportmanagementservice.entity;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class AssessmentTests {

    @Test
    void assessment_BuilderAndGetterSetter_ShouldWork() {
        List<String> testIds = Arrays.asList("test1", "test2");

        Assessment assessment = Assessment.builder()
                .assessmentId("assess123")
                .assessmentDuration(120)
                .title("Java Assessment")
                .averageTimeTaken(60.5)
                .isSystem(true)
                .numberOfTakers(50)
                .numberOfUniqueTakers(45)
                .totalScore(100)
                .testsIds(testIds)
                .progressCount(20)
                .completedCount(30)
                .averagePercentage(75.0)
                .passMark(60.0)
                .instruction("Follow the instructions carefully.")
                .expireDate("2024-12-31")
                .commenceDate("2024-01-01")
                .numberOfFeedbacks(10)
                .numberOfCompletedStatus(30)
                .numberOfIncompleteStatus(20)
                .build();

        assertEquals("assess123", assessment.getAssessmentId());
        assertEquals(120, assessment.getAssessmentDuration());
        assertEquals("Java Assessment", assessment.getTitle());
        assertEquals(60.5, assessment.getAverageTimeTaken());
        assertTrue(assessment.isSystem());
        assertEquals(50, assessment.getNumberOfTakers());
        assertEquals(45, assessment.getNumberOfUniqueTakers());
        assertEquals(100, assessment.getTotalScore());
        assertEquals(testIds, assessment.getTestsIds());
        assertEquals(20, assessment.getProgressCount());
        assertEquals(30, assessment.getCompletedCount());
        assertEquals(75.0, assessment.getAveragePercentage());
        assertEquals(60.0, assessment.getPassMark());
        assertEquals("Follow the instructions carefully.", assessment.getInstruction());
        assertEquals("2024-12-31", assessment.getExpireDate());
        assertEquals("2024-01-01", assessment.getCommenceDate());
        assertEquals(10, assessment.getNumberOfFeedbacks());
        assertEquals(30, assessment.getNumberOfCompletedStatus());
        assertEquals(20, assessment.getNumberOfIncompleteStatus());

        assessment.setAssessmentId("newAssess123");
        assertEquals("newAssess123", assessment.getAssessmentId());
    }

    @Test
    void assessment_NoArgsConstructor_ShouldCreateObject() {
        Assessment assessment = new Assessment();
        assertNotNull(assessment);
    }

    @Test
    void assessment_AllArgsConstructor_ShouldCreateObject() {
        List<String> testIds = Arrays.asList("test1", "test2");

        Assessment assessment = new Assessment("assess123", 120, "Java Assessment", 60.5, true, 50, 45, 100, testIds, 20, 30, 75.0, 60.0, "Follow the instructions carefully.", "2024-12-31", "2024-01-01", 10, 30, 20);

        assertEquals("assess123", assessment.getAssessmentId());
        assertEquals(120, assessment.getAssessmentDuration());
        assertEquals("Java Assessment", assessment.getTitle());
        assertEquals(60.5, assessment.getAverageTimeTaken());
        assertTrue(assessment.isSystem());
        assertEquals(50, assessment.getNumberOfTakers());
        assertEquals(45, assessment.getNumberOfUniqueTakers());
        assertEquals(100, assessment.getTotalScore());
        assertEquals(testIds, assessment.getTestsIds());
        assertEquals(20, assessment.getProgressCount());
        assertEquals(30, assessment.getCompletedCount());
        assertEquals(75.0, assessment.getAveragePercentage());
        assertEquals(60.0, assessment.getPassMark());
        assertEquals("Follow the instructions carefully.", assessment.getInstruction());
        assertEquals("2024-12-31", assessment.getExpireDate());
        assertEquals("2024-01-01", assessment.getCommenceDate());
        assertEquals(10, assessment.getNumberOfFeedbacks());
        assertEquals(30, assessment.getNumberOfCompletedStatus());
        assertEquals(20, assessment.getNumberOfIncompleteStatus());
    }

    @Test
    void assessment_EqualsAndHashCode_ShouldWork() {
        List<String> testIds1 = Arrays.asList("test1", "test2");
        List<String> testIds2 = Arrays.asList("test1", "test2");

        Assessment assessment1 = Assessment.builder()
                .assessmentId("assess123")
                .testsIds(testIds1)
                .build();

        Assessment assessment2 = Assessment.builder()
                .assessmentId("assess123")
                .testsIds(testIds2)
                .build();

        Assessment assessment3 = Assessment.builder()
                .assessmentId("assess456")
                .testsIds(testIds1)
                .build();

        assertEquals(assessment1, assessment2);
        assertEquals(assessment1.hashCode(), assessment2.hashCode());
        assertNotEquals(assessment1, assessment3);
        assertNotEquals(assessment1.hashCode(), assessment3.hashCode());
    }

    @Test
    void assessment_ToString_ShouldNotReturnNull() {
        Assessment assessment = Assessment.builder()
                .assessmentId("assess123")
                .build();
        assertNotNull(assessment.toString());
    }
}