package com.amap.amapreportmanagementservice.entity;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CandidateFeedbackEntityTest { // Renamed to CandidateFeedbackEntityTest

    @Test
    void candidateFeedback_BuilderAndGetterSetter_ShouldWork() {
        List<Feedback> feedbackList = Arrays.asList(
                Feedback.builder().questionText("Question 1").questionAnswer("Answer 1").build(),
                Feedback.builder().questionText("Question 2").questionAnswer("Answer 2").build()
        );

        CandidateFeedback candidateFeedback = CandidateFeedback.builder()
                .candidateEmail("<EMAIL>")
                .candidateId("candidate123")
                .assessmentId("assess456")
                .feedbacks(feedbackList)
                .date("2024-01-01")
                .build();

        assertEquals("<EMAIL>", candidateFeedback.getCandidateEmail());
        assertEquals("candidate123", candidateFeedback.getCandidateId());
        assertEquals("assess456", candidateFeedback.getAssessmentId());
        assertEquals(feedbackList, candidateFeedback.getFeedbacks());
        assertEquals("2024-01-01", candidateFeedback.getDate());

        candidateFeedback.setCandidateId("newCandidate123");
        assertEquals("newCandidate123", candidateFeedback.getCandidateId());
    }

    @Test
    void candidateFeedback_NoArgsConstructor_ShouldCreateObject() {
        CandidateFeedback candidateFeedback = new CandidateFeedback();
        assertNotNull(candidateFeedback);
    }

    @Test
    void candidateFeedback_AllArgsConstructor_ShouldCreateObject() {
        List<Feedback> feedbackList = Arrays.asList(
                Feedback.builder().questionText("Question 1").questionAnswer("Answer 1").build(),
                Feedback.builder().questionText("Question 2").questionAnswer("Answer 2").build()
        );

        CandidateFeedback candidateFeedback = new CandidateFeedback("<EMAIL>", "candidate123", "assess456", feedbackList, "2024-01-01");

        assertEquals("<EMAIL>", candidateFeedback.getCandidateEmail());
        assertEquals("candidate123", candidateFeedback.getCandidateId());
        assertEquals("assess456", candidateFeedback.getAssessmentId());
        assertEquals(feedbackList, candidateFeedback.getFeedbacks());
        assertEquals("2024-01-01", candidateFeedback.getDate());
    }

    @Test
    void candidateFeedback_EqualsAndHashCode_ShouldWork() {
        List<Feedback> feedbackList1 = Arrays.asList(
                Feedback.builder().questionText("Question 1").questionAnswer("Answer 1").build()
        );
        List<Feedback> feedbackList2 = Arrays.asList(
                Feedback.builder().questionText("Question 1").questionAnswer("Answer 1").build()
        );

        CandidateFeedback feedback1 = CandidateFeedback.builder()
                .candidateId("candidate123")
                .assessmentId("assess456")
                .feedbacks(feedbackList1)
                .build();

        CandidateFeedback feedback2 = CandidateFeedback.builder()
                .candidateId("candidate123")
                .assessmentId("assess456")
                .feedbacks(feedbackList2)
                .build();

        CandidateFeedback feedback3 = CandidateFeedback.builder()
                .candidateId("candidate789")
                .assessmentId("assess456")
                .feedbacks(feedbackList1)
                .build();

        assertEquals(feedback1, feedback2);
        assertEquals(feedback1.hashCode(), feedback2.hashCode());
        assertNotEquals(feedback1, feedback3);
        assertNotEquals(feedback1.hashCode(), feedback3.hashCode());
    }

    @Test
    void candidateFeedback_ToString_ShouldNotReturnNull() {
        CandidateFeedback candidateFeedback = CandidateFeedback.builder()
                .candidateId("candidate123")
                .assessmentId("assess456")
                .build();
        assertNotNull(candidateFeedback.toString());
    }
}