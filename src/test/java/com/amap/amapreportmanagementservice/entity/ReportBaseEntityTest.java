package com.amap.amapreportmanagementservice.entity;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ReportBaseEntityTest {

    @Test
    void reportBaseEntity_GetterSetter_ShouldWork() {
        ReportBaseEntity entity = new ReportBaseEntity();

        entity.setPk("PK123");
        entity.setSk("SK456");
        entity.setOrganizationId("org789");
        entity.setCreatedAt("2024-01-01T10:00:00");
        entity.setUpdatedAt("2024-01-02T12:00:00");

        assertEquals("PK123", entity.getPk());
        assertEquals("SK456", entity.getSk());
        assertEquals("org789", entity.getOrganizationId());
        assertEquals("2024-01-01T10:00:00", entity.getCreatedAt());
        assertEquals("2024-01-02T12:00:00", entity.getUpdatedAt());
    }

    @Test
    void reportBaseEntity_AllArgsConstructor_ShouldCreateObject() {
        ReportBaseEntity entity = new ReportBaseEntity(
                "PK123", "SK456", "org789", "2024-01-01T10:00:00", "2024-01-02T12:00:00"
        );

        assertEquals("PK123", entity.getPk());
        assertEquals("SK456", entity.getSk());
        assertEquals("org789", entity.getOrganizationId());
        assertEquals("2024-01-01T10:00:00", entity.getCreatedAt());
        assertEquals("2024-01-02T12:00:00", entity.getUpdatedAt());
    }

    @Test
    void reportBaseEntity_NoArgsConstructor_ShouldCreateObject() {
        ReportBaseEntity entity = new ReportBaseEntity();
        assertNotNull(entity);
    }



    @Test
    void reportBaseEntity_NonNullOrganizationId_ShouldWork() {
        assertThrows(NullPointerException.class, () -> new ReportBaseEntity("pk", "sk", null, "createdAt", "updatedAt"));

        ReportBaseEntity entity = new ReportBaseEntity("pk", "sk", "orgId", "createdAt", "updatedAt");
        assertEquals("orgId", entity.getOrganizationId());
    }

    @Test
    void reportBaseEntity_ToString_ShouldNotReturnNull() {
        ReportBaseEntity entity = new ReportBaseEntity("pk", "sk", "orgId", "createdAt", "updatedAt");
        assertNotNull(entity.toString());
    }
}