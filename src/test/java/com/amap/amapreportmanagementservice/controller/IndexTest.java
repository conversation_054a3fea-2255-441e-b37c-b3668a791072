package com.amap.amapreportmanagementservice.controller;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class IndexTest {

    private final Index index = new Index();

    @Test
    void index_shouldReturnSuccessResponseWithWelcomeMessage() {
        ResponseEntity<Object> response = index.index();

        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());

        @SuppressWarnings("unchecked")
        Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
        assertNotNull(responseBody);
        assertEquals("Welcome to report service", responseBody.get("data"));
    }

    @Test
    void index_shouldReturnNonNullResponseEntity() {
        ResponseEntity<Object> response = index.index();
        assertNotNull(response);
    }
}