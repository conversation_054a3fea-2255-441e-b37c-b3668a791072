package com.amap.amapreportmanagementservice.controller;

import com.amap.amapreportmanagementservice.dto.*;
import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.AssessmentsDetails;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.AssessmentResultsDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateMetricsDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.ComparativeTestAnalysisDTO;
import com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221.GeneralDetailsDTO;
import com.amap.amapreportmanagementservice.entity.CandidateAssessment;
import com.amap.amapreportmanagementservice.entity.QuestionFlagging;
import com.amap.amapreportmanagementservice.service.ResponseService;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.amap.amapreportmanagementservice.TestConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

class AssessmentControllerTest {
    @Mock
    private ResponseService responseService;

    @InjectMocks
    private AssessmentController assessmentController;

    public AssessmentControllerTest() {
        MockitoAnnotations.openMocks(this);
    }
    @Test
    void testGetGeneralDetails() {
        // Arrange
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        GeneralDetailsDTO mockResponse = new GeneralDetailsDTO(); // Replace with the actual response DTO
        when(responseService.getGeneralDetails(organizationId, assessmentId)).thenReturn(mockResponse);

        // Act
        ResponseEntity<Object> response = assessmentController.getGeneralDetails(organizationId, assessmentId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);
    }



    @Test
    void testGetAssessmentDetails() {
        // Arrange
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        AssessmentsDetails mockResponse = new AssessmentsDetails();
        when(responseService.getAssessmentDetails(organizationId, assessmentId)).thenReturn(mockResponse);

        // Act
        ResponseEntity<Object> response = assessmentController.getAssessmentDetails(organizationId, assessmentId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);
    }

    @Test
    void testGetAssessmentTestDetails(){
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;

        CandidateMetricsDTO mockResponse= new CandidateMetricsDTO();

        when(responseService.getCandidateMetrics(organizationId, assessmentId)).thenReturn(mockResponse);

        ResponseEntity<Object> response= assessmentController.getAssessmentTestDetails(organizationId,assessmentId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);

    }

    @Test
    void testGetComparativeInformation() {
        // Arrange
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String candidateEmail = TESTEMAIL;
        String candidateId = CANDIDATEID;
        ComparativeTestAnalysisDTO mockResponse = new ComparativeTestAnalysisDTO(); // Replace with the actual response DTO
        when(responseService.getComparableMetrics(organizationId, assessmentId, candidateEmail, candidateId))
                .thenReturn(mockResponse);

        // Act
        ResponseEntity<Object> response = assessmentController.getComparativeInformation(
                organizationId, assessmentId, candidateEmail, candidateId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);
    }

    @Test
    void testGetFeedback(){
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;

        List<FeedbackResponseDTO> mockResponse = new ArrayList<>();

        when(responseService.getFeedbacks(organizationId, assessmentId)).thenReturn(mockResponse);

        // Act
        ResponseEntity<Object> response= assessmentController.getFeedbacks(organizationId, assessmentId);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);
    }

    @Test
    void testGetAllDetails(){
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        AllDetailsDTO mockResponse= new AllDetailsDTO();

        when(responseService.getAllDetails(organizationId, assessmentId)).thenReturn(mockResponse);
        ResponseEntity<Object> response= assessmentController.getAllDetails(organizationId, assessmentId);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);
    }

    @Test
    void testGetAllComparativeAnalysis(){
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;

        List<ComparedInfoDTO> mockResponse= new ArrayList<>();

        when(responseService.getComparativeAnalysis(organizationId, assessmentId)).thenReturn(mockResponse);
        ResponseEntity<Object> response= assessmentController.getAllComparativeAnalysis(organizationId, assessmentId);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);
    }

    @Test
    void testGetAllCandidatesComparativeAnalysis() {
        // Arrange
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        Map<String, List<ComparativeTestAnalysisDTO>> mockResponse = new HashMap<>(); // Replace with the actual response DTO
        when(responseService.getAllCandidatesComparativeAnalysis(organizationId, assessmentId)).thenReturn(mockResponse);

        // Act
        ResponseEntity<Object> response = assessmentController.getAllCandidatesComparativeAnalysis(organizationId, assessmentId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);
    }

    @Test
    void testGetAllCandidateTrajectories() {
        // Arrange
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        List<CandidateTrajectoryInfoDTO> mockResponse = new ArrayList<>();
        when(responseService.getCandidateTrajectory(organizationId, assessmentId)).thenReturn(mockResponse);

        // Act
        ResponseEntity<Object> response = assessmentController.getAllCandidateTrajectories(organizationId, assessmentId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);
    }

    @Test
    void testGetOrganizationAssessments() {
        // Arrange
        String organizationId = ORGANIZATIONID;
        List<AssessmentDTO> mockResponse = new ArrayList<>();
        when(responseService.getOrganizationAssessments(organizationId)).thenReturn(mockResponse);

        // Act
        ResponseEntity<Object> response = assessmentController.getOrganizationAssessments(organizationId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);
    }

    @Test
    void testGetAssessmentResult() {
        // Arrange
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String candidateId = CANDIDATEID;
        String email = TESTEMAIL;
        AssessmentResultsDTO mockResponse = new AssessmentResultsDTO();
        when(responseService.getCandidateResults(organizationId, assessmentId, candidateId, email)).thenReturn(mockResponse);

        // Act
        ResponseEntity<Object> response = assessmentController.getAssessmentResult(organizationId, assessmentId, candidateId, email);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);
    }

    @Test
    void testGetCandidatesResults() {
        // Arrange
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        List<CandidateAssessment> mockResponse = new ArrayList<>(); // Replace with the actual response DTO
        when(responseService.getCandidatesResults(organizationId, assessmentId)).thenReturn(mockResponse);

        // Act
        ResponseEntity<Object> response = assessmentController.getCandidatesResults(organizationId, assessmentId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);
    }

    @Test
    void testGetFlaggedQuestionReasons() {
        // Arrange
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String questionId = "qst123";
        List<QuestionFlagging> mockResponse = new ArrayList<>();
        when(responseService.getFlaggedQuestionReasons(organizationId, assessmentId, questionId)).thenReturn(mockResponse);

        // Act
        ResponseEntity<Object> response = assessmentController.getFlaggedQuestionReasons(organizationId, assessmentId, questionId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(ResponseHandler.successResponse(HttpStatus.OK, mockResponse), response);
    }
}
