package com.amap.amapreportmanagementservice.config.security.filters;

import com.amap.amapreportmanagementservice.exceptions.JwtAuthenticationException;
import com.amap.amapreportmanagementservice.service.BaseTestClass;
import com.amap.amapreportmanagementservice.service.JwtService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.mockito.Mockito.*;

class JwtAuthenticationFilterTest extends BaseTestClass {
    @InjectMocks
    private JwtAuthenticationFilter jwtAuthenticationFilter;
    @Mock
    private JwtService jwtService;

    @Test
    void testDoFilterInternal_ValidToken() throws Exception {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        FilterChain filterChain = mock(FilterChain.class);

        when(request.getHeader("Authorization")).thenReturn("Bearer validToken");
        when(jwtService.extractUserName("validToken")).thenReturn("testUser");
        when(jwtService.isTokenValid("validToken")).thenReturn(true);

        jwtAuthenticationFilter.doFilterInternal(request, response, filterChain);

        verify(filterChain).doFilter(request, response);
        verify(jwtService).extractUserName("validToken");
        verify(jwtService).isTokenValid("validToken");
        verify(jwtService).extractString("validToken", "userId");
        verify(jwtService).extractString("validToken", "role");

    }

    @Test
    void testDoFilterInternal_InvalidToken() throws Exception {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        FilterChain filterChain = mock(FilterChain.class);

        when(request.getHeader("Authorization")).thenReturn("Bearer invalidToken");
        doThrow(new JwtAuthenticationException("Invalid Token")).when(jwtService).extractUserName("invalidToken");

        jwtAuthenticationFilter.doFilterInternal(request, response, filterChain);

        verify(filterChain).doFilter(request, response);
        verify(jwtService).extractUserName("invalidToken");
    }
}
