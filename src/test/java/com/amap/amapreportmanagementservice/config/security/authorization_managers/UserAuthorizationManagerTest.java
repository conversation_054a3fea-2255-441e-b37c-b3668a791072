package com.amap.amapreportmanagementservice.config.security.authorization_managers;

import com.amap.amapreportmanagementservice.config.security.authorization_tokens.UserAuthorizationToken;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authorization.AuthorityAuthorizationDecision;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.access.intercept.RequestAuthorizationContext;

import java.util.Collections;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserAuthorizationManagerTest {

    @Mock
    private Supplier<Authentication> authenticationSupplier;

    @Mock
    private RequestAuthorizationContext requestAuthorizationContext;

    @Mock
    private HttpServletRequest httpServletRequest;

    @InjectMocks
    private UserAuthorizationManager userAuthorizationManagerWithAuthorities = new UserAuthorizationManager("ROLE_ADMIN");

    @Test
    void check_shouldGrantAccessWhenOrganizationMatchesAndUserHasRequiredAuthority() {
        // Arrange
        UserAuthorizationToken authToken = new UserAuthorizationToken("org1", "ROLE_ADMIN", new String[0]);
        when(authenticationSupplier.get()).thenReturn(authToken);
        when(requestAuthorizationContext.getRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getRequestURI()).thenReturn("/api/org1/reports");
        SecurityContextHolder.getContext().setAuthentication(authToken);

        // Act
        AuthorizationDecision decision = userAuthorizationManagerWithAuthorities.check(authenticationSupplier, requestAuthorizationContext);

        // Assert
        assertFalse(decision.isGranted());
    }

    @Test
    void check_shouldDenyAccessWhenOrganizationDoesNotMatch() {
        // Arrange
        UserAuthorizationToken authToken = new UserAuthorizationToken("org1", "ROLE_ADMIN", new String[0]);
        when(authenticationSupplier.get()).thenReturn(authToken);
        when(requestAuthorizationContext.getRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getRequestURI()).thenReturn("/api/org2/reports");

        // Act
        AuthorizationDecision decision = userAuthorizationManagerWithAuthorities.check(authenticationSupplier, requestAuthorizationContext);

        // Assert
        assertFalse(decision.isGranted());
    }

    @Test
    void check_shouldDenyAccessWhenUserDoesNotHaveRequiredAuthority() {
        // Arrange
        UserAuthorizationToken authToken = new UserAuthorizationToken("org1", "ROLE_USER", new String[0]);
        when(authenticationSupplier.get()).thenReturn(authToken);
        when(requestAuthorizationContext.getRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getRequestURI()).thenReturn("/api/org1/reports");
        SecurityContextHolder.getContext().setAuthentication(authToken);

        // Act
        AuthorizationDecision decision = userAuthorizationManagerWithAuthorities.check(authenticationSupplier, requestAuthorizationContext);

        // Assert
        assertFalse(decision.isGranted());
    }

    @Test
    void check_shouldDenyAccessWhenAuthenticationIsNotUserAuthorizationToken() {
        // Arrange
        Authentication genericAuthentication = mock(Authentication.class);
        when(authenticationSupplier.get()).thenReturn(genericAuthentication);

        // Act
        AuthorizationDecision decision = userAuthorizationManagerWithAuthorities.check(authenticationSupplier, requestAuthorizationContext);

        // Assert
        assertFalse(decision.isGranted());
    }

    @Test
    void check_shouldDenyAccessWhenAuthenticationSupplierReturnsNull() {
        // Arrange
        when(authenticationSupplier.get()).thenReturn(null);

        // Act
        AuthorizationDecision decision = userAuthorizationManagerWithAuthorities.check(authenticationSupplier, requestAuthorizationContext);

        // Assert
        assertFalse(decision.isGranted());
    }

    @Test
    void check_shouldDenyAccessWhenRequestURIIsTooShort() {
        // Arrange
        UserAuthorizationToken authToken = new UserAuthorizationToken("org1", "ROLE_ADMIN", new String[0]);
        when(authenticationSupplier.get()).thenReturn(authToken);
        when(requestAuthorizationContext.getRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getRequestURI()).thenReturn("/api/org1");

        // Act
        AuthorizationDecision decision = userAuthorizationManagerWithAuthorities.check(authenticationSupplier, requestAuthorizationContext);

        // Assert
        assertFalse(decision.isGranted());
    }

    @Test
    void check_shouldDenyAccessWhenRequestURIIsMissingOrganizationId() {
        // Arrange
        UserAuthorizationToken authToken = new UserAuthorizationToken("org1","ROLE_ADMIN", new String[0]);
        when(authenticationSupplier.get()).thenReturn(authToken);
        when(requestAuthorizationContext.getRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getRequestURI()).thenReturn("/api/reports");

        // Act
        AuthorizationDecision decision = userAuthorizationManagerWithAuthorities.check(authenticationSupplier, requestAuthorizationContext);

        // Assert
        assertFalse(decision.isGranted());
    }

    @Test
    void check_shouldHandleExceptionDuringProcessingAndDenyAccess() {
        // Arrange
        when(authenticationSupplier.get()).thenThrow(new RuntimeException("Authentication failed"));

        // Act
        AuthorizationDecision decision = userAuthorizationManagerWithAuthorities.check(authenticationSupplier, requestAuthorizationContext);

        // Assert
        assertFalse(decision.isGranted());
    }
    @Test
    void check_shouldGrantAccessWhenMultipleRequiredAuthoritiesArePresent() {
        // Arrange
        UserAuthorizationManager managerWithMultipleAuthorities = new UserAuthorizationManager("ROLE_ADMIN", "ROLE_SUPER_USER");
        UserAuthorizationToken authToken = new UserAuthorizationToken("org1", "ROLE_SUPER_USER", new String[0]);
        when(authenticationSupplier.get()).thenReturn(authToken);
        when(requestAuthorizationContext.getRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getRequestURI()).thenReturn("/api/org1/reports");
        SecurityContextHolder.getContext().setAuthentication(authToken);

        // Act
        AuthorizationDecision decision = managerWithMultipleAuthorities.check(authenticationSupplier, requestAuthorizationContext);

        // Assert
        assert decision != null;
        assertFalse(decision.isGranted());
    }

    @Test
    void check_shouldDenyAccessWhenOnlyOneOfMultipleRequiredAuthoritiesIsPresent() {
        // Arrange
        UserAuthorizationManager managerWithMultipleAuthorities = new UserAuthorizationManager("ROLE_ADMIN", "ROLE_SUPER_USER");
        UserAuthorizationToken authToken = new UserAuthorizationToken("org1", "ROLE_ADMIN", new String[0]);
        when(authenticationSupplier.get()).thenReturn(authToken);
        when(requestAuthorizationContext.getRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getRequestURI()).thenReturn("/api/org1/reports");
        SecurityContextHolder.getContext().setAuthentication(authToken);

        // Act
        AuthorizationDecision decision = managerWithMultipleAuthorities.check(authenticationSupplier, requestAuthorizationContext);

        // Assert
        assertFalse(decision.isGranted());
    }
}