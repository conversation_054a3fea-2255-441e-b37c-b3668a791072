package com.amap.amapreportmanagementservice.config.security;

import com.amap.amapreportmanagementservice.config.security.authentication_providers.JwtAuthenticationProvider;
import com.amap.amapreportmanagementservice.config.security.authorization_tokens.JwtAuthenticationToken;
import com.amap.amapreportmanagementservice.exceptions.JwtAuthenticationException;
import com.amap.amapreportmanagementservice.service.BaseTestClass;
import com.amap.amapreportmanagementservice.service.JwtService;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.proc.BadJOSEException;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;

import java.text.ParseException;
import java.util.UUID;

import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CustomAuthenticationProviderTest extends BaseTestClass {
    @InjectMocks
    private JwtAuthenticationProvider jwtAuthenticationProvider;

    @Mock
    private JwtService jwtService;

    @Mock
    private Authentication authentication;

    @Test
    void testAuthentication() throws BadJOSEException, ParseException, JOSEException {
//        Given
        String jwtToken = jwtService.generateToken();
        String userId = UUID.randomUUID().toString();
        String role = "Super Admin";
        when(authentication.getPrincipal()).thenReturn(jwtToken);
        when(jwtService.extractUserName(jwtToken)).thenReturn(userId);
        when(jwtService.extractString(jwtToken, "role")).thenReturn(role);
        when(jwtService.extract(jwtToken, "permissions")).thenReturn(new String[]{"admin"});

        Authentication authenticationToken = new JwtAuthenticationToken(userId, role, new String[]{"admin"});

        Authentication authentication1 = jwtAuthenticationProvider.authenticate(authentication);
        assertEquals(authenticationToken, authentication1);
    }

    @Test
    void testAuthenticationWithException() throws JOSEException, BadJOSEException, ParseException {
        String jwtToken = jwtService.generateToken();
        String role = "Super Admin";
        when(authentication.getPrincipal()).thenReturn(jwtToken);
        doThrow(new ParseException("Invalid JWT token", 2)).when(jwtService).extractUserName(jwtToken);
        when(jwtService.extractString(jwtToken, "role")).thenReturn(role);

        assertThatThrownBy(() -> jwtAuthenticationProvider.authenticate(authentication))
                .isInstanceOf(JwtAuthenticationException.class)
                .hasMessage("Invalid JWT token", 2);
        verify(jwtService, never()).extractString(jwtToken, "role");
    }

    @Test
    void testSupportsJwtAuthenticationToken() {
        assertTrue(jwtAuthenticationProvider.supports(JwtAuthenticationToken.class));
    }
    @Test
    void testSupportsOtherAuthenticationTokens() {
        assertFalse(jwtAuthenticationProvider.supports(UsernamePasswordAuthenticationToken.class));
        assertFalse(jwtAuthenticationProvider.supports(AnonymousAuthenticationToken.class));
    }
}
