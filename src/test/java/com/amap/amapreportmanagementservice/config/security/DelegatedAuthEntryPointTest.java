package com.amap.amapreportmanagementservice.config.security;

import com.amap.amapreportmanagementservice.exceptions.JwtAuthenticationException;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.proc.BadJOSEException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.servlet.HandlerExceptionResolver;

import java.text.ParseException;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class DelegatedAuthEntryPointTest {

    @Mock
    private HandlerExceptionResolver resolver;
    @Mock
    private HttpServletRequest request;
    @Mock
    private HttpServletResponse response;
    @Mock
    private AuthenticationException genericAuthException;
    @Mock
    private ParseException parseException;
    @Mock
    private BadJOSEException badJOSEException;
    @Mock
    private JOSEException joseException;
    @Mock
    private InsufficientAuthenticationException insufficientAuthException;
    @Mock
    private JwtAuthenticationException jwtAuthException;
    @Mock
    private AccessDeniedException accessDeniedException;

    private DelegatedAuthEntryPoint entryPoint;

    @BeforeEach
    void setUp() {
        entryPoint = new DelegatedAuthEntryPoint(resolver);
    }

    @Test
    void commence_nullErrorAttribute_shouldResolveGenericAuthException() {
        when(request.getAttribute("error")).thenReturn(null);

        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(request, response, null, genericAuthException);
    }

    @Test
    void commence_defaultCaseErrorAttribute_shouldResolveGenericAuthException() {
        when(request.getAttribute("error")).thenReturn(new Object()); // Some non-matching object

        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(request, response, null, genericAuthException);
    }

    @Test
    void commence_parseExceptionErrorAttribute_shouldResolveParseException() {
        when(request.getAttribute("error")).thenReturn(parseException);
        when(parseException.getMessage()).thenReturn("Parse error message");
        when(parseException.getErrorOffset()).thenReturn(10);

        ArgumentCaptor<ParseException> captor = ArgumentCaptor.forClass(ParseException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        ParseException resolvedException = captor.getValue();
        assertEquals("Parse error message", resolvedException.getMessage());
        assertEquals(10, resolvedException.getErrorOffset());
    }

    @Test
    void commence_badJOSEExceptionErrorAttribute_shouldResolveBadJOSEException() {
        when(request.getAttribute("error")).thenReturn(badJOSEException);
        when(badJOSEException.getMessage()).thenReturn("Bad JOSE message");

        ArgumentCaptor<BadJOSEException> captor = ArgumentCaptor.forClass(BadJOSEException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        assertEquals("Bad JOSE message", captor.getValue().getMessage());
    }

    @Test
    void commence_joseExceptionErrorAttribute_shouldResolveJOSEException() {
        when(request.getAttribute("error")).thenReturn(joseException);
        when(joseException.getMessage()).thenReturn("JOSE message");

        ArgumentCaptor<JOSEException> captor = ArgumentCaptor.forClass(JOSEException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        assertEquals("JOSE message", captor.getValue().getMessage());
    }

    @Test
    void commence_insufficientAuthenticationExceptionErrorAttribute_shouldResolveInsufficientAuthenticationException() {
        Throwable cause = new RuntimeException("Auth cause");
        when(request.getAttribute("error")).thenReturn(insufficientAuthException);
        when(insufficientAuthException.getMessage()).thenReturn("Insufficient auth message");
        when(insufficientAuthException.getCause()).thenReturn(cause);

        ArgumentCaptor<InsufficientAuthenticationException> captor = ArgumentCaptor.forClass(InsufficientAuthenticationException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        assertEquals("Insufficient auth message", captor.getValue().getMessage());
        assertEquals(cause, captor.getValue().getCause());
    }

    @Test
    void commence_jwtAuthenticationExceptionErrorAttribute_shouldResolveJwtAuthenticationException() {
        Throwable cause = new IllegalArgumentException("JWT cause");
        when(request.getAttribute("error")).thenReturn(jwtAuthException);
        when(jwtAuthException.getMessage()).thenReturn("JWT auth message");
        when(jwtAuthException.getCause()).thenReturn(cause);

        ArgumentCaptor<JwtAuthenticationException> captor = ArgumentCaptor.forClass(JwtAuthenticationException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        assertEquals("JWT auth message", captor.getValue().getMessage());
        assertEquals(cause, captor.getValue().getCause());
    }

    @Test
    void commence_accessDeniedExceptionErrorAttribute_shouldResolveAccessDeniedException() {
        Throwable cause = new SecurityException("Access denied cause");
        when(request.getAttribute("error")).thenReturn(accessDeniedException);
        when(accessDeniedException.getMessage()).thenReturn("Access denied message");
        when(accessDeniedException.getCause()).thenReturn(cause);

        ArgumentCaptor<AccessDeniedException> captor = ArgumentCaptor.forClass(AccessDeniedException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        assertEquals("Access denied message", captor.getValue().getMessage());
        assertEquals(cause, captor.getValue().getCause());
    }

    @Test
    void commence_exceptionWithoutCause_shouldResolveExceptionWithNullCause() {
        when(request.getAttribute("error")).thenReturn(insufficientAuthException);
        when(insufficientAuthException.getMessage()).thenReturn("Insufficient auth message without cause");
        when(insufficientAuthException.getCause()).thenReturn(null);

        ArgumentCaptor<InsufficientAuthenticationException> captor = ArgumentCaptor.forClass(InsufficientAuthenticationException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        assertEquals("Insufficient auth message without cause", captor.getValue().getMessage());
        assertNull(captor.getValue().getCause());
    }

    @Test
    void commence_differentParseExceptionSubclass_shouldStillResolveAsParseException() {
        ParseException customParseException = new ParseException("Custom parse error", 20);
        when(request.getAttribute("error")).thenReturn(customParseException);

        ArgumentCaptor<ParseException> captor = ArgumentCaptor.forClass(ParseException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        assertEquals("Custom parse error", captor.getValue().getMessage());
        assertEquals(20, captor.getValue().getErrorOffset());
    }

    @Test
    void commence_differentBadJOSESubclass_shouldStillResolveAsBadJOSEException() {
        BadJOSEException customBadJOSEException = new BadJOSEException("Custom bad JOSE");
        when(request.getAttribute("error")).thenReturn(customBadJOSEException);

        ArgumentCaptor<BadJOSEException> captor = ArgumentCaptor.forClass(BadJOSEException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        assertEquals("Custom bad JOSE", captor.getValue().getMessage());
    }

    @Test
    void commence_differentJOSESubclass_shouldStillResolveAsJOSEException() {
        JOSEException customJOSEException = new JOSEException("Custom JOSE error");
        when(request.getAttribute("error")).thenReturn(customJOSEException);

        ArgumentCaptor<JOSEException> captor = ArgumentCaptor.forClass(JOSEException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        assertEquals("Custom JOSE error", captor.getValue().getMessage());
    }

    @Test
    void commence_differentInsufficientAuthenticationSubclass_shouldStillResolveAsInsufficientAuthenticationException() {
        InsufficientAuthenticationException customInsufficient = new InsufficientAuthenticationException("Custom insufficient");
        when(request.getAttribute("error")).thenReturn(customInsufficient);

        ArgumentCaptor<InsufficientAuthenticationException> captor = ArgumentCaptor.forClass(InsufficientAuthenticationException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        assertEquals("Custom insufficient", captor.getValue().getMessage());
    }

    @Test
    void commence_differentJwtAuthenticationSubclass_shouldStillResolveAsJwtAuthenticationException() {
        JwtAuthenticationException customJwtAuth = new JwtAuthenticationException("Custom JWT auth");
        when(request.getAttribute("error")).thenReturn(customJwtAuth);

        ArgumentCaptor<JwtAuthenticationException> captor = ArgumentCaptor.forClass(JwtAuthenticationException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        assertEquals("Custom JWT auth", captor.getValue().getMessage());
    }

    @Test
    void commence_differentAccessDeniedSubclass_shouldStillResolveAsAccessDeniedException() {
        AccessDeniedException customAccessDenied = new AccessDeniedException("Custom access denied");
        when(request.getAttribute("error")).thenReturn(customAccessDenied);

        ArgumentCaptor<AccessDeniedException> captor = ArgumentCaptor.forClass(AccessDeniedException.class);
        entryPoint.commence(request, response, genericAuthException);

        verify(resolver).resolveException(eq(request), eq(response), eq(null), captor.capture());
        assertEquals("Custom access denied", captor.getValue().getMessage());
    }
}