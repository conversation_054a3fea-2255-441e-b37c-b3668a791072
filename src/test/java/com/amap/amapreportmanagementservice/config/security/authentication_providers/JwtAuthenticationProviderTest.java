package com.amap.amapreportmanagementservice.config.security.authentication_providers;

import com.amap.amapreportmanagementservice.config.security.authorization_tokens.JwtAuthenticationToken;
import com.amap.amapreportmanagementservice.exceptions.JwtAuthenticationException;
import com.amap.amapreportmanagementservice.service.JwtService;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.proc.BadJOSEException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.text.ParseException;
import java.util.Collection;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class JwtAuthenticationProviderTest {

    @Mock
    private JwtService jwtService;

    @InjectMocks
    private JwtAuthenticationProvider jwtAuthenticationProvider;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testAuthenticate_ValidToken_ShouldReturnJwtAuthenticationToken() throws BadJOSEException, ParseException, JOSEException {
        String token = "validToken";
        String userId = "user123";
        String role = "ADMIN";
        String[] permissions = {"READ", "WRITE"};

        Authentication authentication = mock(Authentication.class);
        when(authentication.getPrincipal()).thenReturn(token);
        when(jwtService.extractUserName(token)).thenReturn(userId);
        when(jwtService.extractString(token, "role")).thenReturn(role);
        when(jwtService.extract(token, "permissions")).thenReturn(permissions);

        Authentication result = jwtAuthenticationProvider.authenticate(authentication);

        assertNotNull(result);
        assertTrue(result instanceof JwtAuthenticationToken);
        JwtAuthenticationToken jwtResult = (JwtAuthenticationToken) result;
        assertEquals(userId, jwtResult.getName());
        assertEquals(role, jwtResult.getRole());

        // Verify permissions
        Collection<GrantedAuthority> authorities = jwtResult.getAuthorities();

        //Create the authorities that are expected.
        Collection<GrantedAuthority> expectedAuthorities = Stream.of(permissions).map(p -> new SimpleGrantedAuthority(p.toUpperCase())).collect(Collectors.toList());

        assertTrue(authorities.containsAll(expectedAuthorities));
        assertEquals(expectedAuthorities.size() , authorities.size());
    }

    @Test
    void testAuthenticate_InvalidToken_ShouldThrowJwtAuthenticationException() throws BadJOSEException, ParseException, JOSEException {
        String token = "invalidToken";

        Authentication authentication = mock(Authentication.class);
        when(authentication.getPrincipal()).thenReturn(token);
        when(jwtService.extractUserName(token)).thenThrow(new BadJOSEException("Invalid token"));

        assertThrows(JwtAuthenticationException.class, () -> jwtAuthenticationProvider.authenticate(authentication));
    }

    @Test
    void testSupports_JwtAuthenticationToken_ShouldReturnTrue() {
        assertTrue(jwtAuthenticationProvider.supports(JwtAuthenticationToken.class));
    }

    @Test
    void testSupports_OtherAuthentication_ShouldReturnFalse() {
        assertFalse(jwtAuthenticationProvider.supports(Authentication.class));
    }

    @Test
    void testAuthenticate_ParseException_ShouldThrowJwtAuthenticationException() throws BadJOSEException, ParseException, JOSEException {
        String token = "validToken";

        Authentication authentication = mock(Authentication.class);
        when(authentication.getPrincipal()).thenReturn(token);
        when(jwtService.extractUserName(token)).thenThrow(new ParseException("Parse error", 0));

        assertThrows(JwtAuthenticationException.class, () -> jwtAuthenticationProvider.authenticate(authentication));
    }

    @Test
    void testAuthenticate_JOSEException_ShouldThrowJwtAuthenticationException() throws BadJOSEException, ParseException, JOSEException {
        String token = "validToken";

        Authentication authentication = mock(Authentication.class);
        when(authentication.getPrincipal()).thenReturn(token);
        when(jwtService.extractUserName(token)).thenThrow(new JOSEException("JOSE error"));

        assertThrows(JwtAuthenticationException.class, () -> jwtAuthenticationProvider.authenticate(authentication));
    }
}