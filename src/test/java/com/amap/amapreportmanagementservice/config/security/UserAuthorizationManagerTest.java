package com.amap.amapreportmanagementservice.config.security;

import com.amap.amapreportmanagementservice.config.security.authorization_managers.UserAuthorizationManager;
import com.amap.amapreportmanagementservice.config.security.authorization_tokens.UserAuthorizationToken;
import com.amap.amapreportmanagementservice.service.BaseTestClass;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.access.intercept.RequestAuthorizationContext;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class UserAuthorizationManagerTest extends BaseTestClass {

    private final UserAuthorizationManager userAuthorizationManager = new UserAuthorizationManager("VIEW_REPORT", "MANAGE_DOMAIN", "ADMIN");

    @Mock
    private HttpServletRequest servletRequest;


    @Test
    void testAuthorizationManagerWithMatchingOrganizationIdAndAuthorities() {
        when(servletRequest.getRequestURI()).thenReturn("/organizations/1bf5694a-8452-4811-818f-dc6293f634ad/assessments/df098e48-6f08-4d8e-8294-077737881c9b/candidate-metrics");

        UserAuthorizationToken authentication = new UserAuthorizationToken("1bf5694a-8452-4811-818f-dc6293f634ad", "Super Admin", new String[]{"admin"});
        SecurityContextHolder.getContext().setAuthentication(authentication);

        AuthorizationDecision decision = userAuthorizationManager.check(() -> authentication, new RequestAuthorizationContext(servletRequest));

        assert decision != null;
        assertTrue(decision.isGranted());
    }

    @Test
    void testAuthorizationManagerWithNonMatchingOrganizationId() {
        when(servletRequest.getRequestURI()).thenReturn("/organizations/1bf5694a-8452-4811-818f-dc6293f634ad/assessments/df098e48-6f08-4d8e-8294-077737881c9b/candidate-metrics");

        UserAuthorizationToken authentication = new UserAuthorizationToken("1bf5694a-8452-4811-818f-hello", "Super Admin", new String[]{"admin"});
        SecurityContextHolder.getContext().setAuthentication(authentication);

        AuthorizationDecision decision = userAuthorizationManager.check(() -> authentication, new RequestAuthorizationContext(servletRequest));

        assert decision != null;
        assertFalse(decision.isGranted());
    }

    @Test
    void testAuthorizationManagerWithMatchingOrganizationIdButNoAuthorities() {
        when(servletRequest.getRequestURI()).thenReturn("/organizations/1bf5694a-8452-4811-818f-dc6293f634ad/assessments/df098e48-6f08-4d8e-8294-077737881c9b/candidate-metrics");

        UserAuthorizationToken authentication = new UserAuthorizationToken("1bf5694a-8452-4811-818f-dc6293f634ad", "Super Admin", new String[]{"user"});
        SecurityContextHolder.getContext().setAuthentication(authentication);

        AuthorizationDecision decision = userAuthorizationManager.check(() -> authentication, new RequestAuthorizationContext(servletRequest));

        assert decision != null;
        assertFalse(decision.isGranted());
    }

    @Test
    void testAuthorizationManagerWithWrongAuthenticationType() {
        when(servletRequest.getRequestURI()).thenReturn("/organizations/1bf5694a-8452-4811-818f-dc6293f634ad/assessments/df098e48-6f08-4d8e-8294-077737881c9b/candidate-metrics");

        Authentication authentication = mock(Authentication.class);
        SecurityContextHolder.getContext().setAuthentication(authentication);

        AuthorizationDecision decision = userAuthorizationManager.check(() -> authentication, new RequestAuthorizationContext(servletRequest));

        assert decision != null;
        assertFalse(decision.isGranted());
    }
}
