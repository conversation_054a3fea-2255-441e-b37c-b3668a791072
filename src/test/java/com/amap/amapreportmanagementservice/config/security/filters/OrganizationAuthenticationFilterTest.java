package com.amap.amapreportmanagementservice.config.security.filters;

import com.amap.amapreportmanagementservice.exceptions.JwtAuthenticationException;
import com.amap.amapreportmanagementservice.service.BaseTestClass;
import com.amap.amapreportmanagementservice.service.JwtService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.mockito.Mockito.*;

class OrganizationAuthenticationFilterTest extends BaseTestClass {

    @Mock
    private JwtService jwtService;

    @InjectMocks
    private OrganizationAuthenticationFilter organizationAuthenticationFilter;

    @Test
    void testDoFilterInternal_ValidTokenAndMatchingOrganization() throws Exception {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        FilterChain filterChain = mock(FilterChain.class);

        when(request.getHeader("Authorization")).thenReturn("Bearer validToken");
        when(jwtService.extractString("validToken", "organizationId")).thenReturn("org123");
        when(request.getRequestURI()).thenReturn("/api/organizations/org123");

        organizationAuthenticationFilter.doFilterInternal(request, response, filterChain);

        verify(filterChain, times(1)).doFilter(request, response);
    }

    @Test
    void testDoFilterInternal_ValidTokenAndMismatchedOrganization() throws Exception {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        FilterChain filterChain = mock(FilterChain.class);

        when(request.getHeader("Authorization")).thenReturn("Bearer validToken");
        when(jwtService.extractString("validToken", "organizationId")).thenReturn("org123");
        when(request.getRequestURI()).thenReturn("/api/organizations/org456");

        organizationAuthenticationFilter.doFilterInternal(request, response, filterChain);

        verify(filterChain, times(1)).doFilter(request, response);
    }

    @Test
    void testDoFilterInternal_InvalidToken() throws Exception {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        FilterChain filterChain = mock(FilterChain.class);

        when(request.getHeader("Authorization")).thenReturn("Bearer invalidToken");
        when(jwtService.extractString("invalidToken", "organizationId")).thenThrow(new JwtAuthenticationException("Invalid Token"));

        organizationAuthenticationFilter.doFilterInternal(request, response, filterChain);

        // Verify that the filter chain is called without setting authentication
        verify(filterChain).doFilter(request, response);

        // Verify that no interactions with jwtService occur (as the token is invalid)
        verify(jwtService).extractString("invalidToken", "organizationId");

        verify(request).setAttribute(eq("error"), any(JwtAuthenticationException.class));
    }

    // Add more test cases as needed for different scenarios
}
