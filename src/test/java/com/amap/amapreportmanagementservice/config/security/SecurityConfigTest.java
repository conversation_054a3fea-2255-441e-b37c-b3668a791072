package com.amap.amapreportmanagementservice.config.security;

import com.amap.amapreportmanagementservice.config.security.authentication_providers.JwtAuthenticationProvider;
import com.amap.amapreportmanagementservice.config.security.authorization_managers.UserAuthorizationManager;
import com.amap.amapreportmanagementservice.config.security.filters.JwtAuthenticationFilter;
import com.amap.amapreportmanagementservice.config.security.filters.OrganizationAuthenticationFilter;
import jakarta.servlet.ServletException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.ResultActions;

import java.io.IOException;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(SecurityConfig.class) // Lightweight config for controller + security testing
class SecurityConfigTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @MockBean
    private JwtAuthenticationProvider jwtAuthenticationProvider;

    @MockBean(name = "delegatedAuthenticationEntryPoint")
    private AuthenticationEntryPoint authenticationEntryPoint;

    @MockBean
    private OrganizationAuthenticationFilter organizationAuthenticationFilter;

    private static final String PROTECTED_URL = "/protected-endpoint";
    private static final String PUBLIC_URL = "/";

    @BeforeEach
    void setUp() throws ServletException, IOException {
        Mockito.doNothing().when(jwtAuthenticationFilter).doFilter(Mockito.any(), Mockito.any(), Mockito.any());
        Mockito.doNothing().when(organizationAuthenticationFilter).doFilter(Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    void whenAccessPublicEndpoint_thenPermitAll() throws Exception {
        mockMvc.perform(get(PUBLIC_URL))
                .andExpect(status().isOk()); // or any configured success status
    }

    @Test
    void whenJwtFilterIsConfigured_thenIsCalledBeforeUsernamePasswordFilter() throws Exception {
        mockMvc.perform(get("/some-other-endpoint"));
        Mockito.verify(jwtAuthenticationFilter, Mockito.atLeastOnce()).doFilter(Mockito.any(), Mockito.any(), Mockito.any());
    }

}
