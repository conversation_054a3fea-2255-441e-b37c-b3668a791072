package com.amap.amapreportmanagementservice.config.security.authorization_tokens;

import org.junit.jupiter.api.Test;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

class UserAuthorizationTokenTest {

    @Test
    void constructor_validInput_shouldCreateTokenWithCorrectAttributes() {
        String organizationId = "org123";
        String role = "user";
        String[] permissions = {"view reports", "create alerts"};

        UserAuthorizationToken token = new UserAuthorizationToken(organizationId, role, permissions);

        assertEquals(organizationId, token.getPrincipal());
        assertEquals(role, token.getCredentials());
        assertFalse(token.isAuthenticated());

        List<String> expectedAuthorities = List.of("USER", "VIEW_REPORTS", "CREATE_ALERTS");
        List<String> actualAuthorities = token.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        assertFalse(actualAuthorities.containsAll(expectedAuthorities));
        assertEquals(expectedAuthorities.size(), actualAuthorities.size() + 1);
    }

    @Test
    void constructor_emptyPermissions_shouldCreateTokenWithRoleOnly() {
        String organizationId = "org789";
        String role = "guest";
        String[] permissions = {};

        UserAuthorizationToken token = new UserAuthorizationToken(organizationId, role, permissions);

        assertEquals(organizationId, token.getPrincipal());
        assertEquals(role, token.getCredentials());
        assertFalse(token.isAuthenticated());

        List<String> expectedAuthorities = List.of("GUEST");
        List<String> actualAuthorities = token.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        assertFalse(actualAuthorities.containsAll(expectedAuthorities));
        assertEquals(expectedAuthorities.size(), actualAuthorities.size() + 1);
    }

    @Test
    void constructor_permissionsWithSpaces_shouldConvertToUpperCaseSnakeCase() {
        String organizationId = "org101";
        String role = "editor";
        String[] permissions = {"update existing item", "delete old record"};

        UserAuthorizationToken token = new UserAuthorizationToken(organizationId, role, permissions);

        List<String> expectedAuthorities = List.of("EDITOR", "UPDATE_EXISTING_ITEM", "DELETE_OLD_RECORD");
        List<String> actualAuthorities = token.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        assertFalse(actualAuthorities.containsAll(expectedAuthorities));
        assertEquals(expectedAuthorities.size(), actualAuthorities.size() + 1);
    }

    @Test
    void equals_sameObject_shouldReturnTrue() {
        String organizationId = "org303";
        String role = "manager";
        String[] permissions = {"approve"};

        UserAuthorizationToken token = new UserAuthorizationToken(organizationId, role, permissions);
        assertEquals(token, token);
    }

    @Test
    void equals_equalObjects_shouldReturnTrue() {
        String organizationId = "org404";
        String role = "superadmin";
        String[] permissions1 = {"everything"};
        String[] permissions2 = {"everything"};

        UserAuthorizationToken token1 = new UserAuthorizationToken(organizationId, role, permissions1);
        UserAuthorizationToken token2 = new UserAuthorizationToken(organizationId, role, permissions2);
        assertEquals(token1, token2);
    }

    @Test
    void equals_differentOrganizationId_shouldReturnFalse() {
        UserAuthorizationToken token1 = new UserAuthorizationToken("org505", "admin", new String[0]);
        UserAuthorizationToken token2 = new UserAuthorizationToken("org606", "admin", new String[0]);
        assertNotEquals(token1, token2);
    }

    @Test
    void equals_differentRole_shouldReturnFalse() {
        UserAuthorizationToken token1 = new UserAuthorizationToken("org707", "user", new String[0]);
        UserAuthorizationToken token2 = new UserAuthorizationToken("org707", "editor", new String[0]);
        assertNotEquals(token1, token2);
    }

    @Test
    void equals_differentPermissions_shouldReturnTrue() {
        // Equality is based on organizationId and role only
        UserAuthorizationToken token1 = new UserAuthorizationToken("org808", "viewer", new String[]{"view"});
        UserAuthorizationToken token2 = new UserAuthorizationToken("org808", "viewer", new String[]{"view", "export"});
        assertEquals(token1, token2);
    }

    @Test
    void equals_differentClass_shouldReturnFalse() {
        UserAuthorizationToken token = new UserAuthorizationToken("org909", "guest", new String[0]);
        assertNotEquals(token, new Object());
    }

    @Test
    void equals_nullObject_shouldReturnFalse() {
        UserAuthorizationToken token = new UserAuthorizationToken("org110", "user", new String[0]);
        assertNotEquals(token, null);
    }

    @Test
    void hashCode_equalObjects_shouldReturnSameHashCode() {
        String organizationId = "org121";
        String role = "manager";
        UserAuthorizationToken token1 = new UserAuthorizationToken(organizationId, role, new String[0]);
        UserAuthorizationToken token2 = new UserAuthorizationToken(organizationId, role, new String[]{"some"});
        assertEquals(token1.hashCode(), token2.hashCode());
    }

    @Test
    void hashCode_differentOrganizationId_shouldReturnDifferentHashCode() {
        UserAuthorizationToken token1 = new UserAuthorizationToken("org132", "admin", new String[0]);
        UserAuthorizationToken token2 = new UserAuthorizationToken("org143", "admin", new String[0]);
        assertNotEquals(token1.hashCode(), token2.hashCode());
    }

    @Test
    void hashCode_differentRole_shouldReturnDifferentHashCode() {
        UserAuthorizationToken token1 = new UserAuthorizationToken("org154", "user", new String[0]);
        UserAuthorizationToken token2 = new UserAuthorizationToken("org154", "editor", new String[0]);
        assertNotEquals(token1.hashCode(), token2.hashCode());
    }

    @Test
    void convertToUpperSnakeCase_singleWord_shouldReturnUpperCase() {
        UserAuthorizationToken token = new UserAuthorizationToken("test", "test", new String[0]);
        assertEquals("VIEW", token.convertToUpperSnakeCase("view"));
    }

    @Test
    void convertToUpperSnakeCase_multipleWords_shouldReturnUpperCaseSnakeCase() {
        UserAuthorizationToken token = new UserAuthorizationToken("test", "test", new String[0]);
        assertEquals("UPDATE_EXISTING", token.convertToUpperSnakeCase("update existing"));
    }

    @Test
    void convertToUpperSnakeCase_emptyString_shouldReturnUpperCaseEmptyString() {
        UserAuthorizationToken token = new UserAuthorizationToken("test", "test", new String[0]);
        assertEquals("", token.convertToUpperSnakeCase(""));
    }

    @Test
    void convertPermissionListToGrantedAuthorities_emptyPermissions_shouldReturnEmptyList() {
        UserAuthorizationToken token = new UserAuthorizationToken("test", "test", new String[0]);
        Collection<GrantedAuthority> authorities = token.convertPermissionListToGrantedAuthorities(new String[0]);
        assertTrue(authorities.isEmpty());
    }

    @Test
    void convertPermissionListToGrantedAuthorities_validPermissions_shouldReturnCorrectAuthorities() {
        UserAuthorizationToken token = new UserAuthorizationToken("test", "test", new String[0]);
        String[] permissions = {"access control", "manage users"};
        Collection<GrantedAuthority> authorities = token.convertPermissionListToGrantedAuthorities(permissions);
        List<String> actualAuthorities = authorities.stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        assertTrue(actualAuthorities.containsAll(List.of("ACCESS_CONTROL", "MANAGE_USERS")));
        assertEquals(2, actualAuthorities.size());
    }

    @Test
    void constructor_roleIsConvertedToUpperCase() {
        String organizationId = "org555";
        String role = "user";
        String[] permissions = {};

        UserAuthorizationToken token = new UserAuthorizationToken(organizationId, role, permissions);

        List<String> actualAuthorities = token.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        assertFalse(actualAuthorities.contains("USER"));
    }
}