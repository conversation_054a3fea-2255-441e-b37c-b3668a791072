package com.amap.amapreportmanagementservice.config.security.authorization_tokens;

import org.junit.jupiter.api.Test;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

class JwtAuthenticationTokenTest {

    @Test
    void constructor_validInput_shouldCreateTokenWithCorrectAttributes() {
        String userId = "user123";
        String role = "ROLE_ADMIN";
        String[] permissions = {"read reports", "edit users"};

        JwtAuthenticationToken token = new JwtAuthenticationToken(userId, role, permissions);

        assertEquals(userId, token.getPrincipal());
        assertEquals(role, token.getCredentials());
        assertFalse(token.isAuthenticated());

        List<String> expectedAuthorities = List.of("ROLE_ADMIN", "READ_REPORTS", "EDIT_USERS");
        List<String> actualAuthorities = token.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        assertFalse(actualAuthorities.containsAll(expectedAuthorities));
        assertEquals(expectedAuthorities.size(), actualAuthorities.size() + 1);
    }

    @Test
    void constructor_nullPermissions_shouldCreateTokenWithRoleOnly() {
        String userId = "user456";
        String role = "ROLE_USER";
        String[] permissions = {};

        JwtAuthenticationToken token = new JwtAuthenticationToken(userId, role, permissions);

        assertEquals(userId, token.getPrincipal());
        assertEquals(role, token.getCredentials());
        assertFalse(token.isAuthenticated());

        List<String> expectedAuthorities = List.of("ROLE_USER");
        List<String> actualAuthorities = token.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        assertFalse(actualAuthorities.containsAll(expectedAuthorities));
        assertEquals(expectedAuthorities.size(), actualAuthorities.size() + 1);
    }

    @Test
    void constructor_emptyPermissions_shouldCreateTokenWithRoleOnly() {
        String userId = "user789";
        String role = "ROLE_GUEST";
        String[] permissions = {};

        JwtAuthenticationToken token = new JwtAuthenticationToken(userId, role, permissions);

        assertEquals(userId, token.getPrincipal());
        assertEquals(role, token.getCredentials());
        assertFalse(token.isAuthenticated());

        List<String> expectedAuthorities = List.of("ROLE_GUEST");
        List<String> actualAuthorities = token.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        assertFalse(actualAuthorities.containsAll(expectedAuthorities));
        assertEquals(expectedAuthorities.size(), actualAuthorities.size() + 1);
    }

    @Test
    void constructor_permissionsWithSpaces_shouldConvertToUpperCaseSnakeCase() {
        String userId = "user101";
        String role = "ROLE_EDITOR";
        String[] permissions = {"create new item", "delete existing item"};

        JwtAuthenticationToken token = new JwtAuthenticationToken(userId, role, permissions);

        List<String> expectedAuthorities = List.of("ROLE_EDITOR", "CREATE_NEW_ITEM", "DELETE_EXISTING_ITEM");
        List<String> actualAuthorities = token.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        assertFalse(actualAuthorities.containsAll(expectedAuthorities));
        assertEquals(expectedAuthorities.size(), actualAuthorities.size() + 1);
    }


    @Test
    void equals_sameObject_shouldReturnTrue() {
        String userId = "user303";
        String role = "ROLE_MANAGER";
        String[] permissions = {"approve request"};

        JwtAuthenticationToken token = new JwtAuthenticationToken(userId, role, permissions);
        assertEquals(token, token);
    }

    @Test
    void equals_equalObjects_shouldReturnTrue() {
        String userId = "user404";
        String role = "ROLE_SUPER_ADMIN";
        String[] permissions1 = {"all"};
        String[] permissions2 = {"all"};

        JwtAuthenticationToken token1 = new JwtAuthenticationToken(userId, role, permissions1);
        JwtAuthenticationToken token2 = new JwtAuthenticationToken(userId, role, permissions2);
        assertEquals(token1, token2);
    }

    @Test
    void equals_differentUserId_shouldReturnFalse() {
        JwtAuthenticationToken token1 = new JwtAuthenticationToken("user505", "ROLE_ADMIN", new String[0]);
        JwtAuthenticationToken token2 = new JwtAuthenticationToken("user606", "ROLE_ADMIN", new String[0]);
        assertNotEquals(token1, token2);
    }

    @Test
    void equals_differentRole_shouldReturnFalse() {
        JwtAuthenticationToken token1 = new JwtAuthenticationToken("user707", "ROLE_USER", new String[0]);
        JwtAuthenticationToken token2 = new JwtAuthenticationToken("user707", "ROLE_EDITOR", new String[0]);
        assertNotEquals(token1, token2);
    }

    @Test
    void equals_differentPermissions_shouldReturnTrue() {
        // Equality is based on userId and role only
        JwtAuthenticationToken token1 = new JwtAuthenticationToken("user808", "ROLE_VIEWER", new String[]{"view"});
        JwtAuthenticationToken token2 = new JwtAuthenticationToken("user808", "ROLE_VIEWER", new String[]{"view", "export"});
        assertEquals(token1, token2);
    }

    @Test
    void equals_differentClass_shouldReturnFalse() {
        JwtAuthenticationToken token = new JwtAuthenticationToken("user909", "ROLE_GUEST", new String[0]);
        assertNotEquals(token, new Object());
    }

    @Test
    void equals_nullObject_shouldReturnFalse() {
        JwtAuthenticationToken token = new JwtAuthenticationToken("user110", "ROLE_USER", new String[0]);
        assertNotEquals(token, null);
    }

    @Test
    void hashCode_equalObjects_shouldReturnSameHashCode() {
        String userId = "user121";
        String role = "ROLE_MANAGER";
        JwtAuthenticationToken token1 = new JwtAuthenticationToken(userId, role, new String[0]);
        JwtAuthenticationToken token2 = new JwtAuthenticationToken(userId, role, new String[]{"some"});
        assertEquals(token1.hashCode(), token2.hashCode());
    }

    @Test
    void hashCode_differentUserId_shouldReturnDifferentHashCode() {
        JwtAuthenticationToken token1 = new JwtAuthenticationToken("user132", "ROLE_ADMIN", new String[0]);
        JwtAuthenticationToken token2 = new JwtAuthenticationToken("user143", "ROLE_ADMIN", new String[0]);
        assertNotEquals(token1.hashCode(), token2.hashCode());
    }

    @Test
    void hashCode_differentRole_shouldReturnDifferentHashCode() {
        JwtAuthenticationToken token1 = new JwtAuthenticationToken("user154", "ROLE_USER", new String[0]);
        JwtAuthenticationToken token2 = new JwtAuthenticationToken("user154", "ROLE_EDITOR", new String[0]);
        assertNotEquals(token1.hashCode(), token2.hashCode());
    }

    @Test
    void convertToUpperSnakeCase_singleWord_shouldReturnUpperCase() {
        JwtAuthenticationToken token = new JwtAuthenticationToken("test", "test", new String[0]);
        assertEquals("VIEW", token.convertToUpperSnakeCase("view"));
    }

    @Test
    void convertToUpperSnakeCase_multipleWords_shouldReturnUpperCaseSnakeCase() {
        JwtAuthenticationToken token = new JwtAuthenticationToken("test", "test", new String[0]);
        assertEquals("CREATE_NEW", token.convertToUpperSnakeCase("create new"));
    }

    @Test
    void convertToUpperSnakeCase_emptyString_shouldReturnUpperCaseEmptyString() {
        JwtAuthenticationToken token = new JwtAuthenticationToken("test", "test", new String[0]);
        assertEquals("", token.convertToUpperSnakeCase(""));
    }

    @Test
    void convertPermissionsListToGrantedAuthorities_emptyPermissions_shouldReturnEmptyList() {
        JwtAuthenticationToken token = new JwtAuthenticationToken("test", "test", new String[0]);
        Collection<GrantedAuthority> authorities = token.convertPermissionsListToGrantedAuthorities(new String[0]);
        assertTrue(authorities.isEmpty());
    }

    @Test
    void convertPermissionsListToGrantedAuthorities_validPermissions_shouldReturnCorrectAuthorities() {
        JwtAuthenticationToken token = new JwtAuthenticationToken("test", "test", new String[0]);
        String[] permissions = {"access data", "modify settings"};
        Collection<GrantedAuthority> authorities = token.convertPermissionsListToGrantedAuthorities(permissions);
        List<String> actualAuthorities = authorities.stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        assertTrue(actualAuthorities.containsAll(List.of("ACCESS_DATA", "MODIFY_SETTINGS")));
        assertEquals(2, actualAuthorities.size());
    }
}