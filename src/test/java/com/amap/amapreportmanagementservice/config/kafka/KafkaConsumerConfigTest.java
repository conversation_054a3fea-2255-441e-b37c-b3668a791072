package com.amap.amapreportmanagementservice.config.kafka;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class KafkaConsumerConfigTest {

    @Value("${spring.kafka.bootstrap-servers}") // This won't be injected in a unit test
    private String bootstrapServers;

    @InjectMocks
    private KafkaConsumerConfig kafkaConsumerConfig;

    @Mock
    private ConsumerFactory<String, Object> consumerFactory;

    @Test
    void consumerConfig_shouldReturnCorrectProperties() {
        String testBootstrapServers = "localhost:9092";
        ReflectionTestUtils.setField(kafkaConsumerConfig, "boostrapServers", testBootstrapServers);

        Map<String, Object> config = kafkaConsumerConfig.consumerConfig();

        assertNotNull(config);
        assertEquals(testBootstrapServers, config.get(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG));
        assertEquals(org.springframework.kafka.support.serializer.JsonSerializer.class, config.get(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG));
        assertEquals(org.springframework.kafka.support.serializer.JsonSerializer.class, config.get(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG));
        assertEquals("*", config.get(org.springframework.kafka.support.serializer.JsonDeserializer.TRUSTED_PACKAGES));
    }

    @Test
    void consumerFactory_shouldCreateDefaultKafkaConsumerFactory() {
        String testBootstrapServers = "localhost:9092";
        ReflectionTestUtils.setField(kafkaConsumerConfig, "boostrapServers", testBootstrapServers);

        ConsumerFactory<String, Object> factory = kafkaConsumerConfig.consumerFactory();

        assertNotNull(factory);
        assertTrue(factory instanceof DefaultKafkaConsumerFactory);

        DefaultKafkaConsumerFactory<?, ?> defaultFactory = (DefaultKafkaConsumerFactory<?, ?>) factory;
        Map<String, Object> config = defaultFactory.getConfigurationProperties();

        assertNotNull(config);
        assertEquals(testBootstrapServers, config.get(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG));
        assertEquals(org.springframework.kafka.support.serializer.JsonSerializer.class, config.get(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG));
        assertEquals(org.springframework.kafka.support.serializer.JsonSerializer.class, config.get(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG));
        assertEquals("*", config.get(org.springframework.kafka.support.serializer.JsonDeserializer.TRUSTED_PACKAGES));
    }

    @Test
    void factory_shouldCreateConcurrentKafkaListenerContainerFactory() {
        KafkaListenerContainerFactory<?> factory = kafkaConsumerConfig.factory(consumerFactory);

        assertNotNull(factory);
        assertTrue(factory instanceof ConcurrentKafkaListenerContainerFactory);

        ConcurrentKafkaListenerContainerFactory<?, ?> concurrentFactory = (ConcurrentKafkaListenerContainerFactory<?, ?>) factory;
        assertEquals(consumerFactory, concurrentFactory.getConsumerFactory());
    }

    @Test
    void kafkaConsumerConfig_constructor_doesNotThrowException() {
        assertDoesNotThrow(() -> new KafkaConsumerConfig());
    }
}