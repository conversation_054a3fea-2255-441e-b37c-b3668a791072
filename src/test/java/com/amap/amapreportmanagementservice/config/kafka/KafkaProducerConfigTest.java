package com.amap.amapreportmanagementservice.config.kafka;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class KafkaProducerConfigTest {

    @Value("${spring.kafka.bootstrap-servers}") // Not directly injected in unit tests
    private String bootstrapServers;

    @InjectMocks
    private KafkaProducerConfig kafkaProducerConfig;

    @Mock
    private ProducerFactory<String, Object> producerFactory;

    @Test
    void producerConfig_shouldReturnCorrectProperties() {
        String testBootstrapServers = "localhost:9092";
        ReflectionTestUtils.setField(kafkaProducerConfig, "boostrapServers", testBootstrapServers);

        Map<String, Object> config = kafkaProducerConfig.producerConfig();

        assertNotNull(config);
        assertEquals(testBootstrapServers, config.get(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG));
        assertEquals(StringSerializer.class, config.get(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG));
        assertEquals(JsonSerializer.class, config.get(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG));
    }

    @Test
    void producerFactory_shouldCreateDefaultKafkaProducerFactoryWithCorrectConfig() {
        String testBootstrapServers = "localhost:9092";
        ReflectionTestUtils.setField(kafkaProducerConfig, "boostrapServers", testBootstrapServers);

        ProducerFactory<String, Object> factory = kafkaProducerConfig.producerFactory();

        assertNotNull(factory);
        assertTrue(factory instanceof DefaultKafkaProducerFactory);

        DefaultKafkaProducerFactory<?, ?> defaultFactory = (DefaultKafkaProducerFactory<?, ?>) factory;
        Map<String, Object> config = defaultFactory.getConfigurationProperties();

        assertNotNull(config);
        assertEquals(testBootstrapServers, config.get(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG));
        assertEquals(StringSerializer.class, config.get(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG));
        assertEquals(JsonSerializer.class, config.get(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG));
    }

    @Test
    void kafkaTemplate_shouldCreateKafkaTemplateWithProvidedProducerFactory() {
        KafkaTemplate<?, ?> template = kafkaProducerConfig.kafkaTemplate(producerFactory);

        assertNotNull(template);
        assertEquals(producerFactory, template.getProducerFactory());
    }

    @Test
    void kafkaProducerConfig_constructor_doesNotThrowException() {
        assertDoesNotThrow(() -> new KafkaProducerConfig());
    }
}