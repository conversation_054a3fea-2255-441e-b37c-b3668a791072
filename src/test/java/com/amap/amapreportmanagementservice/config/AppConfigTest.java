package com.amap.amapreportmanagementservice.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AppConfigTest {

    @InjectMocks
    private AppConfig appConfig;

    @Mock
    private AuthenticationConfiguration authenticationConfiguration;

    @Test
    void authenticationManager_shouldReturnAuthenticationManagerSuccessfully() throws Exception {
        // Arrange
        AuthenticationManager mockAuthenticationManager = mock(AuthenticationManager.class);
        when(authenticationConfiguration.getAuthenticationManager()).thenReturn(mockAuthenticationManager);

        // Act
        AuthenticationManager authenticationManager = appConfig.authenticationManager(authenticationConfiguration);

        // Assert
        assertNotNull(authenticationManager);
        assertEquals(mockAuthenticationManager, authenticationManager);
    }

    @Test
    void authenticationManager_shouldThrowExceptionIfAuthenticationConfigurationThrowsException() throws Exception {
        // Arrange
        Exception mockException = new Exception("Error getting AuthenticationManager");
        when(authenticationConfiguration.getAuthenticationManager()).thenThrow(mockException);

        // Act & Assert
        Exception thrownException = assertThrows(Exception.class, () -> {
            appConfig.authenticationManager(authenticationConfiguration);
        });

        assertEquals("Error getting AuthenticationManager", thrownException.getMessage());
    }

    @Test
    void appConfig_constructor_doesNotThrowException() {
        // Arrange & Act & Assert
        assertDoesNotThrow(AppConfig::new); // Constructor with null AuthenticationConfiguration is acceptable due to @RequiredArgsConstructor
    }

    @Test
    void authenticationManager_shouldHandleNullAuthenticationConfiguration() throws Exception {
        // Arrange
        // authenticationConfiguration is already mocked, and by default, mock methods return null.
        // However, we explicitly set it to return null for clarity.
        when(authenticationConfiguration.getAuthenticationManager()).thenReturn(null);

        // Act
        AuthenticationManager authenticationManager = appConfig.authenticationManager(authenticationConfiguration);

        // Assert
        assertNull(authenticationManager);
    }
}