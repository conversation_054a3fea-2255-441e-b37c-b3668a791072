package com.amap.amapreportmanagementservice.config.dynamo;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

import java.net.URI;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DynamoDbConfigTest {

    @Mock
    private Environment environment;

    @InjectMocks
    private DynamoDbConfig dynamoDbConfig;

    @BeforeEach
    void setUp() {
        when(environment.getRequiredProperty("AWS_ACCESS_KEY")).thenReturn("testAccessKey");
        when(environment.getRequiredProperty("AWS_SECRET_KEY")).thenReturn("testSecretKey");
        when(environment.getRequiredProperty("AWS_REGION")).thenReturn("us-east-1");
    }



    @Test
    void productionDynamoDbClient_shouldCreateClientWithoutEndpointOverride() {
        dynamoDbConfig.dynamoClient = "prod";
        DynamoDbClient client = dynamoDbConfig.productionDynamoDbClient();

        assertNotNull(client);
        // Add more assertions to verify client configuration if needed
    }



    @Test
    void enhancedClient_shouldCreateEnhancedClientWithProductionClient() {
        dynamoDbConfig.dynamoClient = "prod";

        DynamoDbEnhancedClient enhancedClient = dynamoDbConfig.enhancedClient();

        assertNotNull(enhancedClient);
    }



    @Test
    void productionDynamoDbClient_shouldUseCorrectCredentials() {
        dynamoDbConfig.dynamoClient = "prod";
        DynamoDbClient client = dynamoDbConfig.productionDynamoDbClient();

        assertNotNull(client);
        // Verify credentials used in the client creation if possible.
        // This might be tricky because AWS SDK typically handles credentials internally.
    }




}