package com.amap.amapreportmanagementservice.config.dynamo;

import com.amap.amapreportmanagementservice.entity.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DynamoDBTablesTest {

    @Mock
    private DynamoDbEnhancedClient dynamoDbEnhancedClient;

    @Mock
    private Environment environment;


    @Mock
    private DynamoDbTable<ReportBaseEntity> reportTable;

    @Mock
    private DynamoDbTable<Assessment> assessmentTable;

    @Mock
    private DynamoDbTable<CandidateAssessment> candidateAssessmentTable;

    @Mock
    private DynamoDbTable<AssessmentTest> assessmentTestTable;

    @Mock
    private DynamoDbTable<CandidateQuestionResult> candidateQuestionResultTable;

    @Mock
    private DynamoDbTable<CandidateTest> candidateTestTable;

    @Mock
    private DynamoDbTable<TestQuestion> testQuestionTable;

    @Mock
    private DynamoDbTable<CandidateFeedback> candidateFeedbackTable;

    @Mock
    private DynamoDbTable<QuestionFlagging> questionFlaggingTable;

    @Mock
    private DynamoDbTable<WebhookReportJob> webhookReportJobTable;

    @InjectMocks
    private DynamoDBTables dynamoDBTables;

    private final String TEST_TABLE_NAME = "testTableName";

    @BeforeEach
    void setUp() {
        when(environment.getRequiredProperty("DYNAMODB_TABLENAME")).thenReturn(TEST_TABLE_NAME);
    }

    @Test
    void getTableName_shouldReturnConfiguredTableName() {
        String tableName = dynamoDBTables.getTableName();
        assertEquals(TEST_TABLE_NAME, tableName);
        verify(environment).getRequiredProperty("DYNAMODB_TABLENAME");
    }

    @Test
    void reportTable_shouldCreateReportTable() {
        // Setup
        when(dynamoDbEnhancedClient.table(eq(TEST_TABLE_NAME), any(TableSchema.class))).thenReturn(reportTable);

        // Execute
        DynamoDbTable<ReportBaseEntity> table = dynamoDBTables.reportTable();

        // Verify
        assertNotNull(table);
        verify(dynamoDbEnhancedClient).table(eq(TEST_TABLE_NAME), any(TableSchema.class));
    }

    @Test
    void assessmentTable_shouldCreateAssessmentTable() {
        // Setup
        when(dynamoDbEnhancedClient.table(eq(TEST_TABLE_NAME), any(TableSchema.class))).thenReturn(assessmentTable);

        // Execute
        DynamoDbTable<Assessment> table = dynamoDBTables.assessmentTable();

        // Verify
        assertNotNull(table);
        verify(dynamoDbEnhancedClient).table(eq(TEST_TABLE_NAME), any(TableSchema.class));
    }

    @Test
    void candidateAssessmentTable_shouldCreateCandidateAssessmentTable() {
        // Setup
        when(dynamoDbEnhancedClient.table(eq(TEST_TABLE_NAME), any(TableSchema.class))).thenReturn(candidateAssessmentTable);

        // Execute
        DynamoDbTable<CandidateAssessment> table = dynamoDBTables.candidateAssessmentTable();

        // Verify
        assertNotNull(table);
        verify(dynamoDbEnhancedClient).table(eq(TEST_TABLE_NAME), any(TableSchema.class));
    }

    @Test
    void assessmentTestTable_shouldCreateAssessmentTestTable() {
        // Setup
        when(dynamoDbEnhancedClient.table(eq(TEST_TABLE_NAME), any(TableSchema.class))).thenReturn(assessmentTestTable);

        // Execute
        DynamoDbTable<AssessmentTest> table = dynamoDBTables.assessmentTestTable();

        // Verify
        assertNotNull(table);
        verify(dynamoDbEnhancedClient).table(eq(TEST_TABLE_NAME), any(TableSchema.class));
    }

    @Test
    void candidateQuestionResultTable_shouldCreateCandidateQuestionResultTable() {
        // Setup
        when(dynamoDbEnhancedClient.table(eq(TEST_TABLE_NAME), any(TableSchema.class))).thenReturn(candidateQuestionResultTable);

        // Execute
        DynamoDbTable<CandidateQuestionResult> table = dynamoDBTables.candidateQuestionResultTable();

        // Verify
        assertNotNull(table);
        verify(dynamoDbEnhancedClient).table(eq(TEST_TABLE_NAME), any(TableSchema.class));
        // Verify that the converters are used (implicitly verified through the InjectMocks annotation)
    }

    @Test
    void candidateTestTable_shouldCreateCandidateTestTable() {
        // Setup
        when(dynamoDbEnhancedClient.table(eq(TEST_TABLE_NAME), any(TableSchema.class))).thenReturn(candidateTestTable);

        // Execute
        DynamoDbTable<CandidateTest> table = dynamoDBTables.candidateTestTable();

        // Verify
        assertNotNull(table);
        verify(dynamoDbEnhancedClient).table(eq(TEST_TABLE_NAME), any(TableSchema.class));
    }

    @Test
    void testQuestionTable_shouldCreateTestQuestionTable() {
        // Setup
        when(dynamoDbEnhancedClient.table(eq(TEST_TABLE_NAME), any(TableSchema.class))).thenReturn(testQuestionTable);

        // Execute
        DynamoDbTable<TestQuestion> table = dynamoDBTables.testQuestionTable();

        // Verify
        assertNotNull(table);
        verify(dynamoDbEnhancedClient).table(eq(TEST_TABLE_NAME), any(TableSchema.class));
    }

    @Test
    void candidateFeedbackTable_shouldCreateCandidateFeedbackTable() {
        // Setup
        when(dynamoDbEnhancedClient.table(eq(TEST_TABLE_NAME), any(TableSchema.class))).thenReturn(candidateFeedbackTable);

        // Execute
        DynamoDbTable<CandidateFeedback> table = dynamoDBTables.candidateFeedbackTable();

        // Verify
        assertNotNull(table);
        verify(dynamoDbEnhancedClient).table(eq(TEST_TABLE_NAME), any(TableSchema.class));
    }

    @Test
    void questionFlaggingTable_shouldCreateQuestionFlaggingTable() {
        // Setup
        when(dynamoDbEnhancedClient.table(eq(TEST_TABLE_NAME), any(TableSchema.class))).thenReturn(questionFlaggingTable);

        // Execute
        DynamoDbTable<QuestionFlagging> table = dynamoDBTables.questionFlaggingTable();

        // Verify
        assertNotNull(table);
        verify(dynamoDbEnhancedClient).table(eq(TEST_TABLE_NAME), any(TableSchema.class));
    }

    @Test
    void webhookReportJobDynamoDbTable_shouldCreateWebhookReportJobTable() {
        // Setup
        when(dynamoDbEnhancedClient.table(eq(TEST_TABLE_NAME), any(TableSchema.class))).thenReturn(webhookReportJobTable);

        // Execute
        DynamoDbTable<WebhookReportJob> table = dynamoDBTables.webhookReportJobDynamoDbTable();

        // Verify
        assertNotNull(table);
        verify(dynamoDbEnhancedClient).table(eq(TEST_TABLE_NAME), any(TableSchema.class));
    }
}