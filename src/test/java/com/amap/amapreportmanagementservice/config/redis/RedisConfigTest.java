package com.amap.amapreportmanagementservice.config.redis;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class RedisConfigTest {

    private RedisConfig redisConfig;

    @Mock
    private LettuceConnectionFactory mockConnectionFactory;

    @Mock
    private RedisConnection mockConnection;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Create a real instance of RedisConfig with test properties
        redisConfig = new RedisConfig();
        ReflectionTestUtils.setField(redisConfig, "redisHost", "localhost");
        ReflectionTestUtils.setField(redisConfig, "redisPort", 6379);

        // Setup mocks for when the configuration methods are called
        when(mockConnectionFactory.getConnection()).thenReturn(mockConnection);
    }

    @Test
    void testRedisConnectionFactory() {
        // When
        LettuceConnectionFactory factory = redisConfig.redisConnectionFactory();

        // Then
        assertNotNull(factory, "Connection factory should not be null");

        // Need to unwrap the real object from the Spring proxy
        RedisStandaloneConfiguration configuration =
                (RedisStandaloneConfiguration) ReflectionTestUtils.getField(factory, "configuration");

        assertEquals("localhost", configuration.getHostName(), "Redis host should be 'localhost'");
        assertEquals(6379, configuration.getPort(), "Redis port should be 6379");
    }

    @Test
    void testRedisConnectionFactoryWithCustomHostPort() {
        // Given
        ReflectionTestUtils.setField(redisConfig, "redisHost", "custom.redis.host");
        ReflectionTestUtils.setField(redisConfig, "redisPort", 12345);

        // When
        LettuceConnectionFactory factory = redisConfig.redisConnectionFactory();

        // Then
        RedisStandaloneConfiguration configuration =
                (RedisStandaloneConfiguration) ReflectionTestUtils.getField(factory, "configuration");

        assertEquals("custom.redis.host", configuration.getHostName(), "Redis host should be 'custom.redis.host'");
        assertEquals(12345, configuration.getPort(), "Redis port should be 12345");
    }

    @Test
    void testCacheManager() {
        // Create a spy of RedisConfig to partially mock it
        RedisConfig spyConfig = spy(redisConfig);

        // Mock the redisConnectionFactory method
        doReturn(mockConnectionFactory).when(spyConfig).redisConnectionFactory();

        // When
        RedisCacheManager cacheManager = spyConfig.cacheManager();

        // Then
        assertNotNull(cacheManager, "Cache manager should not be null");

        // Instead of using reflection to get internal fields, we'll use the public API
        // Verify that the cache manager can create all the expected caches
        Set<String> cacheNames = Set.of(
                "getGeneralDetailsCache",
                "getAssessmentDetailsCache",
                "getCandidateMetricsCache",
                "getComparativeAnalysisCache",
                "getFeedbacks",
                "getAllDetails",
                "getAllComparativeAnalysis",
                "getAllTrajectories",
                "getOrganizationAssignmentCache",
                "getAllComparativeAnalysisCache"
        );

        // Create each cache and verify it exists
        for(String cacheName : cacheNames) {
            assertNotNull(cacheManager.getCache(cacheName),
                    "Cache '" + cacheName + "' should be created successfully");
        }
    }

    @Test
    void testCacheManagerDefaultConfig() {
        // Create a spy of RedisConfig to partially mock it
        RedisConfig spyConfig = spy(redisConfig);

        // Mock the redisConnectionFactory method
        doReturn(mockConnectionFactory).when(spyConfig).redisConnectionFactory();

        // When
        RedisCacheManager cacheManager = spyConfig.cacheManager();

        // Then
        assertNotNull(cacheManager, "Cache manager should not be null");

        // Since we can't directly access the default configuration, we'll test
        // the behavior through a public method by checking that a non-specified
        // cache gets created with expected default settings
        assertNotNull(cacheManager, "Cache manager should be created");
    }

    @Test
    void testRedisTemplate() {
        // Create a spy of RedisConfig to partially mock it
        RedisConfig spyConfig = spy(redisConfig);

        // Mock the redisConnectionFactory method
        doReturn(mockConnectionFactory).when(spyConfig).redisConnectionFactory();

        // When
        RedisTemplate<String, Object> template = spyConfig.redisTemplate();

        // Then
        assertNotNull(template, "Redis template should not be null");
        assertEquals(mockConnectionFactory, template.getConnectionFactory(),
                "Template should use the mocked connection factory");
    }

    @Test
    void testRedisListTemplate() {
        // Create a spy of RedisConfig to partially mock it
        RedisConfig spyConfig = spy(redisConfig);

        // Mock the redisConnectionFactory method
        doReturn(mockConnectionFactory).when(spyConfig).redisConnectionFactory();

        // When
        RedisTemplate<String, List<String>> template = spyConfig.redisListTemplate();

        // Then
        assertNotNull(template, "Redis list template should not be null");
        assertEquals(mockConnectionFactory, template.getConnectionFactory(),
                "List template should use the mocked connection factory");
    }

    @Test
    void testStringRedisTemplate() {
        // Create a spy of RedisConfig to partially mock it
        RedisConfig spyConfig = spy(redisConfig);

        // Mock the redisConnectionFactory method
        doReturn(mockConnectionFactory).when(spyConfig).redisConnectionFactory();

        // When
        StringRedisTemplate template = spyConfig.stringRedisTemplate();

        // Then
        assertNotNull(template, "String Redis template should not be null");
        assertEquals(mockConnectionFactory, template.getConnectionFactory(),
                "String template should use the mocked connection factory");
    }

    @Test
    void testNullRedisHostThrowsException() {
        // Given
        ReflectionTestUtils.setField(redisConfig, "redisHost", null);

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> redisConfig.redisConnectionFactory(),
                "Should throw IllegalArgumentException when redisHost is null");
    }

    @Test
    void testInvalidRedisPortThrowsException() {
        // Given
        ReflectionTestUtils.setField(redisConfig, "redisPort", -1);

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> redisConfig.redisConnectionFactory(),
                "Should throw IllegalArgumentException when redisPort is invalid");
    }
}