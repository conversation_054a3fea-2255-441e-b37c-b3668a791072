package com.amap.amapreportmanagementservice.config.cors;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import org.springframework.web.servlet.config.annotation.CorsRegistration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CorsConfigTest {

    @Mock
    private Environment environment;

    @Mock
    private CorsRegistry corsRegistry;

    @Mock
    private CorsRegistration corsRegistration;

    private CorsConfig corsConfig;

    @BeforeEach
    void setUp() {
        corsConfig = new CorsConfig(environment);
    }

    @Test
    void addCorsMappings_shouldConfigureCorsRegistry() {
        String allowedOrigins = "http://example.com";
        when(environment.getProperty("ALLOWED_ORIGINS")).thenReturn(allowedOrigins);
        when(corsRegistry.addMapping("/**")).thenReturn(corsRegistration);

        corsConfig.addCorsMappings(corsRegistry);

        verify(corsRegistry).addMapping("/**");
        verify(corsRegistration).allowedOrigins(allowedOrigins);
    }

    @Test
    void addCorsMappings_shouldHandleNullAllowedOrigins() {
        when(environment.getProperty("ALLOWED_ORIGINS")).thenReturn(null);
        when(corsRegistry.addMapping("/**")).thenReturn(corsRegistration);

        corsConfig.addCorsMappings(corsRegistry);

        verify(corsRegistry).addMapping("/**");
        verify(corsRegistration).allowedOrigins((String) null);
    }

    @Test
    void addCorsMappings_shouldHandleEmptyAllowedOrigins() {
        String allowedOrigins = "";
        when(environment.getProperty("ALLOWED_ORIGINS")).thenReturn(allowedOrigins);
        when(corsRegistry.addMapping("/**")).thenReturn(corsRegistration);

        corsConfig.addCorsMappings(corsRegistry);

        verify(corsRegistry).addMapping("/**");
        verify(corsRegistration).allowedOrigins(allowedOrigins);
    }

    @Test
    void corsConfig_constructor_doesNotThrowException() {
        assertDoesNotThrow(() -> new CorsConfig(environment));
    }

    @Test
    void addCorsMappings_shouldHandleMultipleAllowedOrigins() {
        String allowedOrigins = "http://example.com,http://another.com";
        when(environment.getProperty("ALLOWED_ORIGINS")).thenReturn(allowedOrigins);
        when(corsRegistry.addMapping("/**")).thenReturn(corsRegistration);

        corsConfig.addCorsMappings(corsRegistry);

        verify(corsRegistry).addMapping("/**");
        verify(corsRegistration).allowedOrigins("http://example.com,http://another.com");
    }

    @Test
    void addCorsMappings_shouldHandleWhitespaceInAllowedOrigins() {
        String allowedOrigins = " http://example.com , http://another.com ";
        when(environment.getProperty("ALLOWED_ORIGINS")).thenReturn(allowedOrigins);
        when(corsRegistry.addMapping("/**")).thenReturn(corsRegistration);

        corsConfig.addCorsMappings(corsRegistry);

        verify(corsRegistry).addMapping("/**");
        verify(corsRegistration).allowedOrigins(" http://example.com , http://another.com ");
    }

    @Test
    void addCorsMappings_shouldHandleSpecialCharactersInAllowedOrigins() {
        String allowedOrigins = "http://example.com?param=value&other=test";
        when(environment.getProperty("ALLOWED_ORIGINS")).thenReturn(allowedOrigins);
        when(corsRegistry.addMapping("/**")).thenReturn(corsRegistration);

        corsConfig.addCorsMappings(corsRegistry);

        verify(corsRegistry).addMapping("/**");
        verify(corsRegistration).allowedOrigins(allowedOrigins);
    }

    @Test
    void addCorsMappings_shouldHandleWildcardAllowedOrigins() {
        String allowedOrigins = "*";
        when(environment.getProperty("ALLOWED_ORIGINS")).thenReturn(allowedOrigins);
        when(corsRegistry.addMapping("/**")).thenReturn(corsRegistration);

        corsConfig.addCorsMappings(corsRegistry);

        verify(corsRegistry).addMapping("/**");
        verify(corsRegistration).allowedOrigins(allowedOrigins);
    }

    @Test
    void addCorsMappings_shouldHandleEmptyStringWhenAllowedOriginsIsNotSet() {
        when(environment.getProperty("ALLOWED_ORIGINS")).thenReturn(null);
        when(corsRegistry.addMapping("/**")).thenReturn(corsRegistration);

        corsConfig.addCorsMappings(corsRegistry);

        verify(corsRegistry).addMapping("/**");
        verify(corsRegistration).allowedOrigins((String) null);
    }
}