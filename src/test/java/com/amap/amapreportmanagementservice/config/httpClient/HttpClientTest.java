package com.amap.amapreportmanagementservice.config.httpClient;

import org.junit.jupiter.api.Test;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;

class HttpClientTest {

    private final HttpClient httpClient = new HttpClient();

    @Test
    void restTemplate_shouldCreateRestTemplateBean() {
        RestTemplate restTemplate = httpClient.restTemplate();
        assertNotNull(restTemplate);
    }

    @Test
    void restTemplate_shouldCreateNewInstanceOnEachCall() {
        RestTemplate restTemplate1 = httpClient.restTemplate();
        RestTemplate restTemplate2 = httpClient.restTemplate();
        assertNotSame(restTemplate1, restTemplate2);
    }

    @Test
    void httpClient_constructor_doesNotThrowException() {
        assertDoesNotThrow(HttpClient::new);
    }
}