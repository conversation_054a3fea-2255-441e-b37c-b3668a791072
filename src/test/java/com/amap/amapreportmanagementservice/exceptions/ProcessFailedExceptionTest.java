package com.amap.amapreportmanagementservice.exceptions;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

@Slf4j(topic = "application_logger")
public class ProcessFailedExceptionTest extends RuntimeException {

    @Test
    void constructorWithMessage_setsMessageCorrectly() {
        String errorMessage = "Data processing encountered an error.";
        ProcessFailedException exception = new ProcessFailedException(errorMessage);
        assertEquals(errorMessage, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    void constructorWithMessageAndCause_setsMessageAndCauseCorrectly() {
        String errorMessage = "Failed to generate the report.";
        Throwable cause = new IllegalStateException("Database connection lost.");
        ProcessFailedException exception = new ProcessFailedException(errorMessage, cause);
        assertEquals(errorMessage, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }
}