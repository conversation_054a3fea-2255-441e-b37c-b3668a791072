package com.amap.amapreportmanagementservice.exceptions;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

@ResponseStatus(HttpStatus.UNAUTHORIZED)
@Slf4j(topic = "application_logger")
public class JwtAuthenticationExceptionTest extends RuntimeException {

    @Test
    void constructorWithMessage_setsMessageCorrectly() {
        String errorMessage = "JWT token is invalid.";
        JwtAuthenticationException exception = new JwtAuthenticationException(errorMessage);
        assertEquals(errorMessage, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    void constructorWithMessageAndCause_setsMessageAndCauseCorrectly() {
        String errorMessage = "JWT signature verification failed.";
        Throwable cause = new RuntimeException("Underlying crypto error.");
        JwtAuthenticationException exception = new JwtAuthenticationException(errorMessage, cause);
        assertEquals(errorMessage, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    void responseStatusAnnotation_isUnauthorized() throws NoSuchMethodException {
        ResponseStatus annotation = JwtAuthenticationException.class.getAnnotation(ResponseStatus.class);
        assertEquals(HttpStatus.UNAUTHORIZED, annotation.value());
    }
}