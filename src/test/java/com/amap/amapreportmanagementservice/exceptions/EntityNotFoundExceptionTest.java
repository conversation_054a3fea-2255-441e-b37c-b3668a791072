package com.amap.amapreportmanagementservice.exceptions;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

@ResponseStatus(HttpStatus.NOT_FOUND)
@Slf4j(topic = "application_logger")
public class EntityNotFoundExceptionTest extends RuntimeException {

    @Test
    void constructor_setsMessageCorrectly() {
        String errorMessage = "Entity with ID 'abc' not found.";
        EntityNotFoundException exception = new EntityNotFoundException(errorMessage);
        assertEquals(errorMessage, exception.getMessage());
    }

    @Test
    void constructor_setsCauseToNullByDefault() {
        EntityNotFoundException exception = new EntityNotFoundException("Another entity not found.");
        assertNull(exception.getCause());
    }

    @Test
    void responseStatusAnnotation_isNotFound() throws NoSuchMethodException {
        ResponseStatus annotation = EntityNotFoundException.class.getAnnotation(ResponseStatus.class);
        assertEquals(HttpStatus.NOT_FOUND, annotation.value());
    }
}