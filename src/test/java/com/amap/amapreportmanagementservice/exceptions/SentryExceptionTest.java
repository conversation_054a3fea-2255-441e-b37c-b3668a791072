package com.amap.amapreportmanagementservice.exceptions;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class SentryExceptionTest extends RuntimeException {

    @Test
    void constructor_setsMessageCorrectly() {
        String errorMessage = "Error reported to Sentry.";
        SentryException exception = new SentryException(errorMessage);
        assertEquals(errorMessage, exception.getMessage());
        assertNull(exception.getCause());
    }
}