package com.amap.amapreportmanagementservice.exceptions;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class ErrorResponseTest {

    @Test
    void constructor_setsAllFieldsCorrectly() {
        LocalDateTime now = LocalDateTime.now();
        String message = "An error occurred.";
        String description = "Detailed description of the error.";

        ErrorResponse errorResponse = new ErrorResponse(now, message, description);

        assertEquals(now, errorResponse.getTimestamp());
        assertEquals(message, errorResponse.getMessage());
        assertEquals(description, errorResponse.getDescription());
    }

    @Test
    void timestamp_isNotNullAfterConstruction() {
        ErrorResponse errorResponse = new ErrorResponse(LocalDateTime.now(), "Test Message", "Test Description");
        assertNotNull(errorResponse.getTimestamp());
    }

    @Test
    void message_isSetCorrectly() {
        String testMessage = "Validation failed.";
        ErrorResponse errorResponse = new ErrorResponse(LocalDateTime.now(), testMessage, "Details");
        assertEquals(testMessage, errorResponse.getMessage());
    }

    @Test
    void description_isSetCorrectly() {
        String testDescription = "Input data was not in the expected format.";
        ErrorResponse errorResponse = new ErrorResponse(LocalDateTime.now(), "Invalid Input", testDescription);
        assertEquals(testDescription, errorResponse.getDescription());
    }
}