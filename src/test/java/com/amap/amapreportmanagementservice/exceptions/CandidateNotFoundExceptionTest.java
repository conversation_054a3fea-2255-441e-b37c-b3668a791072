package com.amap.amapreportmanagementservice.exceptions;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

@ResponseStatus(HttpStatus.NOT_FOUND)
@Slf4j(topic = "application_logger")
public class CandidateNotFoundExceptionTest extends RuntimeException {

    @Test
    void constructor_setsMessageCorrectly() {
        String errorMessage = "Candidate with ID '123' not found.";
        CandidateNotFoundException exception = new CandidateNotFoundException(errorMessage);
        assertEquals(errorMessage, exception.getMessage());
    }

    @Test
    void constructor_setsCauseToNullByDefault() {
        CandidateNotFoundException exception = new CandidateNotFoundException("Test message");
        assertNull(exception.getCause());
    }

    @Test
    void responseStatusAnnotation_isNotFound() throws NoSuchMethodException {
        ResponseStatus annotation = CandidateNotFoundException.class.getAnnotation(ResponseStatus.class);
        assertEquals(HttpStatus.NOT_FOUND, annotation.value());
    }
}