package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.QuestionFlagging;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import static com.amap.amapreportmanagementservice.TestConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class QuestionFlaggingRepositoryImplTest extends RepositoryBaseTestClass {
    @Mock
    private DynamoDbTable<QuestionFlagging> questionFlaggingTable;

    @InjectMocks
    private QuestionFlaggingRepositoryImpl questionFlaggingRepository;

    @Test
    void saveQuestionFlagging_Success() {
        QuestionFlagging questionFlagging = new QuestionFlagging();
        doNothing().when(questionFlaggingTable).putItem(questionFlagging);

        assertDoesNotThrow(() -> questionFlaggingRepository.saveQuestionFlagging(questionFlagging));
        verify(questionFlaggingTable, times(1)).putItem(questionFlagging);
    }

    @Test
    void saveQuestionFlagging_ExceptionThrown() {
        QuestionFlagging questionFlagging = new QuestionFlagging();
        doThrow(RuntimeException.class).when(questionFlaggingTable).putItem(questionFlagging);

        assertThrows(ProcessFailedException.class, () -> questionFlaggingRepository.saveQuestionFlagging(questionFlagging));
        verify(questionFlaggingTable, times(1)).putItem(questionFlagging);
    }

    @Test
    void checkIfExist_ItemExists() {
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String questionId = QUESTIONID;
        String pk = KeyBuilder.questionFlaggingPK(organizationId, assessmentId);
        String sk = KeyBuilder.questionFlaggingSK(questionId);

        Key key = Key.builder().partitionValue(pk).sortValue(sk).build();
        QuestionFlagging existingItem = new QuestionFlagging();
        when(questionFlaggingTable.getItem(key)).thenReturn(existingItem);

        boolean exists = questionFlaggingRepository.checkIfExist(organizationId, assessmentId, questionId);
        assertTrue(exists);
        verify(questionFlaggingTable, times(1)).getItem(key);
    }

    @Test
    void checkIfExist_ItemDoesNotExist() {
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String questionId = QUESTIONID;
        String pk = KeyBuilder.questionFlaggingPK(organizationId, assessmentId);
        String sk = KeyBuilder.questionFlaggingSK(questionId);

        Key key = Key.builder().partitionValue(pk).sortValue(sk).build();
        when(questionFlaggingTable.getItem(key)).thenReturn(null);

        boolean exists = questionFlaggingRepository.checkIfExist(organizationId, assessmentId, questionId);
        assertFalse(exists);
        verify(questionFlaggingTable, times(1)).getItem(key);
    }

    @Test
    void checkIfExist_ExceptionThrown() {
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String questionId = QUESTIONID;
        String pk = KeyBuilder.questionFlaggingPK(organizationId, assessmentId);
        String sk = KeyBuilder.questionFlaggingSK(questionId);

        Key key = Key.builder().partitionValue(pk).sortValue(sk).build();
        when(questionFlaggingTable.getItem(key)).thenThrow(RuntimeException.class);

        assertThrows(ProcessFailedException.class, () -> questionFlaggingRepository.checkIfExist(organizationId, assessmentId, questionId));
        verify(questionFlaggingTable, times(1)).getItem(key);
    }

}
