package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.Assessment;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.core.pagination.sync.SdkIterable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.ScanEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;

import java.util.Collections;
import java.util.List;

import static com.amap.amapreportmanagementservice.TestConstants.ASSESSMENTID;
import static com.amap.amapreportmanagementservice.TestConstants.ORGANIZATIONID;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class AssessmentRepositoryImplTest extends RepositoryBaseTestClass {

    @Mock
    private DynamoDbTable<Assessment> assessmentTable;

    @InjectMocks
    private AssessmentRepositoryImpl assessmentRepository;

    @Test
    void saveAssessment_shouldSaveAssessmentSuccessfully() {
        Assessment assessment = new Assessment();

        assertDoesNotThrow(() -> assessmentRepository.saveAssessment(assessment));

        verify(assessmentTable, times(1)).putItem(assessment);
    }

    @Test
    void saveAssessment_shouldThrowProcessFailedExceptionOnFailure() {
        Assessment assessment = new Assessment();
        doThrow(RuntimeException.class).when(assessmentTable).putItem(assessment);

        assertThrows(ProcessFailedException.class, () -> assessmentRepository.saveAssessment(assessment));
    }

    @Test
    void updateAssessment_shouldUpdateAssessmentSuccessfully() {
        Assessment assessment = new Assessment();
        UpdateItemEnhancedRequest<Assessment> updateRequest = UpdateItemEnhancedRequest.builder(Assessment.class)
                .item(assessment)
                .ignoreNulls(true)
                .build();

        assertDoesNotThrow(() -> assessmentRepository.updateAssessment(assessment));

        verify(assessmentTable, times(1)).updateItem(eq(updateRequest));
    }

    @Test
    void updateAssessment_shouldThrowProcessFailedExceptionOnFailure(){
        Assessment assessment = new Assessment();
        UpdateItemEnhancedRequest<Assessment> updateRequest = UpdateItemEnhancedRequest.builder(Assessment.class)
                .item(assessment)
                .ignoreNulls(true)
                .build();
        doThrow(RuntimeException.class).when(assessmentTable).updateItem(updateRequest);


        assertThrows(ProcessFailedException.class,()->assessmentRepository.updateAssessment(assessment));
    }

    @Test
    void getAssessment_shouldRetrieveAssessmentSuccessfully() {
        Key key = mock(Key.class);
        Assessment expectedAssessment = new Assessment();
        when(assessmentTable.getItem(key)).thenReturn(expectedAssessment);

        Assessment result = assessmentRepository.getAssessment(key);

        assertEquals(expectedAssessment, result);
        verify(assessmentTable, times(1)).getItem(key);
    }

    @Test
    void getAssessment_shouldThrowProcessFailedExceptionOnFailure(){
        Key key = mock(Key.class);
        doThrow(RuntimeException.class).when(assessmentTable).getItem(key);

        assertThrows(ProcessFailedException.class, ()->assessmentRepository.getAssessment(key));
    }


    void getAssessments_shouldRetrieveListOfAssessments() {
        Key key = mock(Key.class);
        PageIterable<Assessment> pageIterable = mock(PageIterable.class);
        Assessment assessment = new Assessment();
        when(pageIterable.items()).thenReturn((SdkIterable<Assessment>) Collections.singletonList(assessment));
        when(assessmentTable.query(any(QueryEnhancedRequest.class))).thenReturn(pageIterable);

        List<Assessment> result = assessmentRepository.getAssessments(key);

        assertEquals(1, result.size());
        assertEquals(assessment, result.get(0));
        verify(assessmentTable, times(1)).query(any(QueryEnhancedRequest.class));
    }

//    @Test
    void queryUsingGSI_shouldRetrieveListOfAssessments() {
        String organizationId = "org123";
        PageIterable<Assessment> pageIterable = mock(PageIterable.class);
        ScanEnhancedRequest scanEnhancedRequest= ScanEnhancedRequest.builder().build();
        Assessment assessment = new Assessment();
        when(pageIterable.items()).thenReturn((SdkIterable<Assessment>) Collections.singletonList(assessment));
        when(assessmentTable.scan(scanEnhancedRequest)).thenReturn(pageIterable);

        List<Assessment> result = assessmentRepository.queryUsingGSI(organizationId);

        assertEquals(1, result.size());
        assertEquals(assessment, result.get(0));
        verify(assessmentTable, times(1)).scan(scanEnhancedRequest);
    }

    @Test
    void checkIfExist_shouldReturnTrueWhenAssessmentExists() {
        Assessment expectedAssessment = new Assessment();
        when(assessmentTable.getItem(any(Key.class))).thenReturn(expectedAssessment);

        boolean result = assessmentRepository.checkIfExist(ORGANIZATIONID, ASSESSMENTID);

        assertTrue(result);
        verify(assessmentTable, times(1)).getItem(any(Key.class));
    }

    @Test
    void checkIfExist_shouldReturnFalseWhenAssessmentDoesNotExist() {
        when(assessmentTable.getItem(any(Key.class))).thenReturn(null);

        boolean result = assessmentRepository.checkIfExist(ORGANIZATIONID, ASSESSMENTID);

        assertFalse(result);
        verify(assessmentTable, times(1)).getItem(any(Key.class));
    }

    @Test
    void checkIfExist_shouldThrowProcessFailedExceptionOnFailure(){

        doThrow(RuntimeException.class).when(assessmentTable).getItem(any(Key.class));

        assertThrows(ProcessFailedException.class, ()->assessmentRepository.checkIfExist(ORGANIZATIONID, ASSESSMENTID));
    }
}
