package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.TestQuestion;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;

import static com.amap.amapreportmanagementservice.TestConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class TestQuestionRepositoryImplTest extends RepositoryBaseTestClass{
    @Mock
    private DynamoDbTable<TestQuestion> testQuestionTable;

    @InjectMocks
    private TestQuestionRepositoryImpl testQuestionRepository;

    @Test
    void saveTestQuestion_Success() {
        TestQuestion testQuestion = new TestQuestion();
        doNothing().when(testQuestionTable).putItem(testQuestion);

        assertDoesNotThrow(() -> testQuestionRepository.saveTestQuestion(testQuestion));
        verify(testQuestionTable, times(1)).putItem(testQuestion);
    }

    @Test
    void saveTestQuestion_ExceptionThrown() {
        TestQuestion testQuestion = new TestQuestion();
        doThrow(RuntimeException.class).when(testQuestionTable).putItem(testQuestion);

        assertThrows(ProcessFailedException.class, () -> testQuestionRepository.saveTestQuestion(testQuestion));
        verify(testQuestionTable, times(1)).putItem(testQuestion);
    }

    @Test
    void updateTestQuestion_Success() {
        TestQuestion testQuestion = new TestQuestion();
        UpdateItemEnhancedRequest<TestQuestion> updateRequest = UpdateItemEnhancedRequest.builder(TestQuestion.class)
                .item(testQuestion)
                .ignoreNulls(true)
                .build();

        when(testQuestionTable.updateItem(updateRequest)).thenReturn(testQuestion);

        assertDoesNotThrow(() -> testQuestionRepository.updateTestQuestion(testQuestion));
        verify(testQuestionTable, times(1)).updateItem(updateRequest);
    }
    @Test
    void getTestQuestion_Success() {
        Key key = Key.builder().partitionValue("testPK").sortValue("testSK").build();
        TestQuestion testQuestion = new TestQuestion();
        when(testQuestionTable.getItem(key)).thenReturn(testQuestion);

        TestQuestion result = testQuestionRepository.getTestQuestion(key);

        assertNotNull(result);
        assertEquals(testQuestion, result);
        verify(testQuestionTable, times(1)).getItem(key);
    }

    @Test
    void getTestQuestion_ExceptionThrown() {
        Key key = Key.builder().partitionValue("testPK").sortValue("testSK").build();
        when(testQuestionTable.getItem(key)).thenThrow(RuntimeException.class);

        assertThrows(ProcessFailedException.class, () -> testQuestionRepository.getTestQuestion(key));
        verify(testQuestionTable, times(1)).getItem(key);
    }
    @Test
    void updateTestQuestion_ExceptionThrown() {
        TestQuestion testQuestion = new TestQuestion();
        UpdateItemEnhancedRequest<TestQuestion> updateRequest = UpdateItemEnhancedRequest.builder(TestQuestion.class)
                .item(testQuestion)
                .ignoreNulls(true)
                .build();

        doThrow(RuntimeException.class).when(testQuestionTable).updateItem(updateRequest);

        assertThrows(ProcessFailedException.class, () -> testQuestionRepository.updateTestQuestion(testQuestion));
        verify(testQuestionTable, times(1)).updateItem(updateRequest);
    }



    @Test
    void checkIfExist_ItemExists() {
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String testId = TESTID;
        String questionId = QUESTIONID;
        String pk = KeyBuilder.testQuestionPK(organizationId, assessmentId, testId);
        String sk = KeyBuilder.testQuestionSK(questionId);

        Key key = Key.builder().partitionValue(pk).sortValue(sk).build();
        TestQuestion existingItem = new TestQuestion();
        when(testQuestionTable.getItem(key)).thenReturn(existingItem);

        boolean exists = testQuestionRepository.checkIfExist(organizationId, assessmentId, testId, questionId);
        assertTrue(exists);
        verify(testQuestionTable, times(1)).getItem(key);
    }

    @Test
    void checkIfExist_ItemDoesNotExist() {
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String testId = TESTID;
        String questionId = QUESTIONID;
        String pk = KeyBuilder.testQuestionPK(organizationId, assessmentId, testId);
        String sk = KeyBuilder.testQuestionSK(questionId);

        Key key = Key.builder().partitionValue(pk).sortValue(sk).build();
        when(testQuestionTable.getItem(key)).thenReturn(null);

        boolean exists = testQuestionRepository.checkIfExist(organizationId, assessmentId, testId, questionId);
        assertFalse(exists);
        verify(testQuestionTable, times(1)).getItem(key);
    }
}
