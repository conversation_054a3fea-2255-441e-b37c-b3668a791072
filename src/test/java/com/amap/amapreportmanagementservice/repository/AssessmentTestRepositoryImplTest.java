package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.AssessmentTest;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;

import static com.amap.amapreportmanagementservice.TestConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class AssessmentTestRepositoryImplTest extends RepositoryBaseTestClass {
    @Mock
    DynamoDbTable<AssessmentTest> assessmentTestTable;

    @InjectMocks
    AssessmentTestRepositoryImpl assessmentTestRepository;

    @Test
    void saveAssessmentTest_shouldSaveSuccessfully() {
        AssessmentTest assessmentTest = new AssessmentTest();

        assertDoesNotThrow(()->assessmentTestRepository.saveAssessmentTest(assessmentTest));

        verify(assessmentTestTable, times(1)).putItem(assessmentTest);

    }

    @Test
    void saveAssessmentTest_shouldThrowProcessFailedExceptionWhenSaveFails() {
        AssessmentTest assessmentTest = new AssessmentTest();
        doThrow(RuntimeException.class).when(assessmentTestTable).putItem(assessmentTest);

        assertThrows(ProcessFailedException.class, ()->assessmentTestRepository.saveAssessmentTest(assessmentTest));
    }


    @Test
    void updateAssessmentTest_shouldUpdateAssessmentTestSuccessfully() {
        // Arrange
        AssessmentTest assessmentTest = new AssessmentTest();
        UpdateItemEnhancedRequest<AssessmentTest> updateItemEnhancedRequest = UpdateItemEnhancedRequest.builder(AssessmentTest.class)
                .item(assessmentTest)
                .ignoreNulls(true)
                .build();

        assertDoesNotThrow(()->assessmentTestRepository.updateAssessmentTest(assessmentTest));

        verify(assessmentTestTable, times(1)).updateItem(updateItemEnhancedRequest);
    }

    @Test
    void updateAssessmentTest_shouldThrowProcessFailedExceptionWhenUpdateFails() {
        // Arrange
        AssessmentTest assessmentTest = new AssessmentTest();
        UpdateItemEnhancedRequest<AssessmentTest> updateItemEnhancedRequest = UpdateItemEnhancedRequest.builder(AssessmentTest.class)
                .item(assessmentTest)
                .ignoreNulls(true)
                .build();
        doThrow(RuntimeException.class).when(assessmentTestTable).updateItem(updateItemEnhancedRequest);

        // Act & Assert
        assertThrows(ProcessFailedException.class, () ->
                assessmentTestRepository.updateAssessmentTest(assessmentTest));
    }

    @Test
    void getAssessmentTest_shouldReturnAssessmentTest() {
        // Arrange
        Key key = Key.builder().partitionValue("pk").sortValue("sk").build();
        AssessmentTest expectedAssessmentTest = new AssessmentTest();
        when(assessmentTestTable.getItem(key)).thenReturn(expectedAssessmentTest);

        // Act
        AssessmentTest result = assessmentTestRepository.getAssessmentTest(key);

        // Assert
        assertEquals(expectedAssessmentTest, result);
        verify(assessmentTestTable, times(1)).getItem(key);
    }

    @Test
    void getAssessmentTest_shouldThrowProcessFailedExceptionWhenGetFails() {
        // Arrange
        Key key = Key.builder().partitionValue("pk").sortValue("sk").build();

        doThrow(RuntimeException.class).when(assessmentTestTable).getItem(key);

        assertThrows(ProcessFailedException.class, ()->assessmentTestRepository.getAssessmentTests(key));
    }

    @Test
    void checkIfExists_shouldReturnTrueWhenAssessmentTestExists() {
        // Arrange
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String testId=  TESTID;

        String pk = KeyBuilder.assessmentTestPK(organizationId, assessmentId);
        String sk = KeyBuilder.assessmentTestSK(assessmentId, testId);

        Key key = Key.builder().partitionValue(pk).sortValue(sk).build();

        AssessmentTest expectedAssessmentTest = new AssessmentTest();
        when(assessmentTestTable.getItem(key)).thenReturn(expectedAssessmentTest);

        // Act
        boolean exists = assessmentTestRepository.checkIfExists(organizationId, assessmentId, testId);

        // Assert
        assertTrue(exists);
        verify(assessmentTestTable, times(1)).getItem(key);
    }

    @Test
    void checkIfExists_shouldReturnFalseWhenAssessmentTestDoesNotExist() {
        // Arrange
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String testId = TESTID;

        String pk = KeyBuilder.assessmentTestPK(organizationId, assessmentId);
        String sk = KeyBuilder.assessmentTestSK(assessmentId, testId);

        Key key = Key.builder().partitionValue(pk).sortValue(sk).build();

        when(assessmentTestTable.getItem(key)).thenReturn(null);

        // Act
        boolean exists = assessmentTestRepository.checkIfExists(organizationId, assessmentId, testId);

        // Assert
        assertFalse(exists);
        verify(assessmentTestTable, times(1)).getItem(key);
    }

    @Test
    void checkIfExists_shouldThrowProcessFailedExceptionWhenExceptionOccurs() {
        // Arrange
        String organizationId = ORGANIZATIONID;
        String assessmentId = ASSESSMENTID;
        String testId = TESTID;

        String pk = KeyBuilder.assessmentTestPK(organizationId, assessmentId);
        String sk = KeyBuilder.assessmentTestSK(assessmentId, testId);

        Key key = Key.builder().partitionValue(pk).sortValue(sk).build();
    doThrow(RuntimeException.class).when(assessmentTestTable).getItem(key);

    assertThrows(ProcessFailedException.class, ()->assessmentTestRepository.checkIfExists(organizationId, assessmentId, testId));
    }
}
