package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.CandidateQuestionResult;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CandidateQuestionRepositoryImplTest extends RepositoryBaseTestClass {
    @Mock
    private DynamoDbTable<CandidateQuestionResult> candidateQuestionResultTable;

    @InjectMocks
    private CandidateQuestionRepositoryImpl candidateQuestionRepository;

    @Test
    void saveCandidateQuestion_Success() {
        CandidateQuestionResult candidateQuestionResult = new CandidateQuestionResult();


        assertDoesNotThrow(() -> candidateQuestionRepository.saveCandidateQuestion(candidateQuestionResult));
        verify(candidateQuestionResultTable, times(1)).putItem(candidateQuestionResult);
    }

    @Test
    void saveCandidateQuestion_ExceptionThrown() {
        CandidateQuestionResult candidateQuestionResult = new CandidateQuestionResult();
        doThrow(RuntimeException.class).when(candidateQuestionResultTable).putItem(candidateQuestionResult);

        assertThrows(ProcessFailedException.class, () -> candidateQuestionRepository.saveCandidateQuestion(candidateQuestionResult));
        verify(candidateQuestionResultTable, times(1)).putItem(candidateQuestionResult);
    }

    @Test
    void updateCandidateQuestion_Success() {
        CandidateQuestionResult candidateQuestionResult = new CandidateQuestionResult();
        UpdateItemEnhancedRequest<CandidateQuestionResult> updateItemEnhancedRequest =  UpdateItemEnhancedRequest.builder(CandidateQuestionResult.class)
                .item(candidateQuestionResult)
                .ignoreNulls(true)
                .build();
        assertDoesNotThrow(() -> candidateQuestionRepository.updateCandidateQuestion(candidateQuestionResult));
        verify(candidateQuestionResultTable, times(1)).updateItem(updateItemEnhancedRequest);
    }

    @Test
    void updateCandidateQuestion_ExceptionThrown() {
        CandidateQuestionResult candidateQuestionResult = new CandidateQuestionResult();
        UpdateItemEnhancedRequest<CandidateQuestionResult> updateItemEnhancedRequest =  UpdateItemEnhancedRequest.builder(CandidateQuestionResult.class)
                .item(candidateQuestionResult)
                .ignoreNulls(true)
                .build();
        doThrow(RuntimeException.class).when(candidateQuestionResultTable).updateItem(updateItemEnhancedRequest);

        assertThrows(ProcessFailedException.class, () -> candidateQuestionRepository.updateCandidateQuestion(candidateQuestionResult));
        verify(candidateQuestionResultTable, times(1)).updateItem(updateItemEnhancedRequest);
    }

    @Test
    void getCandidateQuestion_Success() {
        Key key = Key.builder().partitionValue("testId").build();
        CandidateQuestionResult expectedResult = new CandidateQuestionResult();
        when(candidateQuestionResultTable.getItem(key)).thenReturn(expectedResult);

        CandidateQuestionResult result = candidateQuestionRepository.getCandidateQuestion(key);
        assertEquals(expectedResult, result);
        verify(candidateQuestionResultTable, times(1)).getItem(key);
    }

    @Test
    void getCandidateQuestion_ExceptionThrown() {
        Key key = Key.builder().partitionValue("testId").build();
        when(candidateQuestionResultTable.getItem(key)).thenThrow(RuntimeException.class);

        assertThrows(ProcessFailedException.class, () -> candidateQuestionRepository.getCandidateQuestion(key));
        verify(candidateQuestionResultTable, times(1)).getItem(key);
    }

}
