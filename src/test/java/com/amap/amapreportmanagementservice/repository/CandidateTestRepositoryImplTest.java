package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.CandidateTest;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CandidateTestRepositoryImplTest extends RepositoryBaseTestClass{

    @Mock
    private DynamoDbTable<CandidateTest> candidateTestTable;

    @InjectMocks
    private CandidateTestRepositoryImpl candidateTestRepository;

    @Test
    void saveCandidateTest_Success() {
        CandidateTest candidateTest = new CandidateTest();
        doNothing().when(candidateTestTable).putItem(candidateTest);

        assertDoesNotThrow(() -> candidateTestRepository.saveCandidateTest(candidateTest));
        verify(candidateTestTable, times(1)).putItem(candidateTest);
    }

    @Test
    void saveCandidateTest_ExceptionThrown() {
        CandidateTest candidateTest = new CandidateTest();
        doThrow(RuntimeException.class).when(candidateTestTable).putItem(candidateTest);

        assertThrows(ProcessFailedException.class, () -> candidateTestRepository.saveCandidateTest(candidateTest));
        verify(candidateTestTable, times(1)).putItem(candidateTest);
    }

    @Test
    void updateCandidateTest_Success() {
        CandidateTest candidateTest = new CandidateTest();
        UpdateItemEnhancedRequest<CandidateTest> updateItemEnhancedRequest =  UpdateItemEnhancedRequest.builder(CandidateTest.class)
                .item(candidateTest)
                .ignoreNulls(true)
                .build();
        assertDoesNotThrow(() -> candidateTestRepository.updateCandidateTest(candidateTest));
        verify(candidateTestTable, times(1)).updateItem(updateItemEnhancedRequest);
    }

    @Test
    void updateCandidateTest_ExceptionThrown() {
        CandidateTest candidateTest = new CandidateTest();
        UpdateItemEnhancedRequest<CandidateTest> updateItemEnhancedRequest =  UpdateItemEnhancedRequest.builder(CandidateTest.class)
                .item(candidateTest)
                .ignoreNulls(true)
                .build();
        doThrow(RuntimeException.class).when(candidateTestTable).updateItem(updateItemEnhancedRequest);

        assertThrows(ProcessFailedException.class, () -> candidateTestRepository.updateCandidateTest(candidateTest));
        verify(candidateTestTable, times(1)).updateItem(updateItemEnhancedRequest);
    }

    @Test
    void getSpecificCandidateTest_Success() {
        Key key = Key.builder().partitionValue("testId").build();
        CandidateTest expectedTest = new CandidateTest();
        when(candidateTestTable.getItem(key)).thenReturn(expectedTest);

        CandidateTest result = candidateTestRepository.getSpecificCandidateTest(key);
        assertEquals(expectedTest, result);
        verify(candidateTestTable, times(1)).getItem(key);
    }

    @Test
    void getSpecificCandidateTest_ExceptionThrown() {
        Key key = Key.builder().partitionValue("testId").build();
        when(candidateTestTable.getItem(key)).thenThrow(RuntimeException.class);

        assertThrows(ProcessFailedException.class, () -> candidateTestRepository.getSpecificCandidateTest(key));
        verify(candidateTestTable, times(1)).getItem(key);
    }

}
