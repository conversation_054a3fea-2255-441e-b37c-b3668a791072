package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.WebhookReportJob;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class WebhookReportJobRepositoryImplTest extends RepositoryBaseTestClass{

    @Mock
    private DynamoDbTable<WebhookReportJob> mockWebhookReportJobTable;

    @InjectMocks
    private WebhookReportJobRepositoryImpl webhookReportJobRepository;
    @Test
    void saveWebhookReportJob_Success() {
        WebhookReportJob job = new WebhookReportJob();
        when(mockWebhookReportJobTable.getItem(job)).thenReturn(job);
        assertDoesNotThrow(() -> webhookReportJobRepository.saveWebhookReportJob(job));

        verify(mockWebhookReportJobTable, times(1)).putItem(job);
    }

    @Test
    void saveWebhookReportJob_Failure() {
        WebhookReportJob job = new WebhookReportJob();
        doThrow(RuntimeException.class).when(mockWebhookReportJobTable).putItem(job);

        assertThrows(ProcessFailedException.class, () -> webhookReportJobRepository.saveWebhookReportJob(job));

        verify(mockWebhookReportJobTable, times(1)).putItem(job);
    }

    @Test
    void updateWebhookReportJob_Success() {
        WebhookReportJob job = new WebhookReportJob();
        UpdateItemEnhancedRequest<WebhookReportJob> updateItemEnhancedRequest = UpdateItemEnhancedRequest.builder(WebhookReportJob.class)
                .item(job)
                .ignoreNulls(true)
                .build();
        assertDoesNotThrow(() -> webhookReportJobRepository.updateWebhookReportJob(job));

        verify(mockWebhookReportJobTable, times(1)).updateItem(updateItemEnhancedRequest);
    }

    @Test
    void updateWebhookReportJob_Failure() {
        WebhookReportJob job = new WebhookReportJob();
        UpdateItemEnhancedRequest<WebhookReportJob> updateItemEnhancedRequest = UpdateItemEnhancedRequest.builder(WebhookReportJob.class)
                .item(job)
                .ignoreNulls(true)
                .build();
        doThrow(RuntimeException.class).when(mockWebhookReportJobTable).updateItem(updateItemEnhancedRequest);

        assertThrows(ProcessFailedException.class, () -> webhookReportJobRepository.updateWebhookReportJob(job));

        verify(mockWebhookReportJobTable, times(1)).updateItem(updateItemEnhancedRequest);
    }

    @Test
    void deleteWebhookReportJob_Success() {
        Key key = Key.builder().partitionValue("PK").sortValue("SK").build();

        assertDoesNotThrow(() -> webhookReportJobRepository.deleteWebhookReportJob(key));

        verify(mockWebhookReportJobTable, times(1)).deleteItem(key);
    }

    @Test
    void deleteWebhookReportJob_Failure() {
        Key key = Key.builder().partitionValue("PK").sortValue("SK").build();
        doThrow(RuntimeException.class).when(mockWebhookReportJobTable).deleteItem(key);

        assertThrows(ProcessFailedException.class, () -> webhookReportJobRepository.deleteWebhookReportJob(key));

        verify(mockWebhookReportJobTable, times(1)).deleteItem(key);
    }

    @Test
    void getWebhookReportJob_Success() {
        Key key = Key.builder().partitionValue("PK").sortValue("SK").build();
        WebhookReportJob job = new WebhookReportJob();
        when(mockWebhookReportJobTable.getItem(key)).thenReturn(job);

        WebhookReportJob result = webhookReportJobRepository.getWebhookReportJob(key);

        assertNotNull(result);
        assertEquals(job, result);
        verify(mockWebhookReportJobTable, times(1)).getItem(key);
    }

    @Test
    void getWebhookReportJob_Failure() {
        Key key = Key.builder().partitionValue("PK").sortValue("SK").build();
        doThrow(RuntimeException.class).when(mockWebhookReportJobTable).getItem(key);

        assertThrows(ProcessFailedException.class, () -> webhookReportJobRepository.getWebhookReportJob(key));

        verify(mockWebhookReportJobTable, times(1)).getItem(key);
    }


}
