package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.CandidateFeedback;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

class CandidateFeedbackRepositoryImplTest extends RepositoryBaseTestClass {
    @Mock
    private DynamoDbTable<CandidateFeedback> candidateFeedbackTable;

    @InjectMocks
    private CandidateFeedbackRepositoryImpl candidateFeedbackRepository;

    @Test
    void saveCandidateFeedback_shouldSaveSuccessfully() {
        // Arrange
        CandidateFeedback feedback = new CandidateFeedback();

        // Act
        candidateFeedbackRepository.saveCandidateFeedback(feedback);

        // Assert
        verify(candidateFeedbackTable, times(1)).putItem(feedback);
    }

    @Test
    void saveCandidateFeedback_shouldThrowProcessFailedExceptionOnFailure() {
        // Arrange
        CandidateFeedback feedback = new CandidateFeedback();
        doThrow(RuntimeException.class).when(candidateFeedbackTable).putItem(feedback);

        // Act & Assert
        assertThrows(ProcessFailedException.class, () ->
                candidateFeedbackRepository.saveCandidateFeedback(feedback));
    }


    @Test
    void getCandidateFeedback_shouldReturnFeedback() {
        // Arrange
        Key key = Key.builder().partitionValue("pk").sortValue("sk").build();
        CandidateFeedback expectedFeedback = new CandidateFeedback();
        when(candidateFeedbackTable.getItem(key)).thenReturn(expectedFeedback);

        // Act
        CandidateFeedback result = candidateFeedbackRepository.getCandidateFeedback(key);

        // Assert
        assertEquals(expectedFeedback, result);
        verify(candidateFeedbackTable, times(1)).getItem(key);
    }

    @Test
    void getCandidateFeedback_shouldThrowExceptionWhenGetFails() {
        // Arrange
        Key key = Key.builder().partitionValue("pk").sortValue("sk").build();
        when(candidateFeedbackTable.getItem(key)).thenThrow(RuntimeException.class);

        // Act & Assert
        assertThrows(ProcessFailedException.class, () ->
                candidateFeedbackRepository.getCandidateFeedback(key));
    }

}
