package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.CandidateAssessment;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CandidateAssessmentRepositoryImplTest extends RepositoryBaseTestClass {

    @Mock
    private DynamoDbTable<CandidateAssessment> candidateAssessmentTable;
    @InjectMocks
    private CandidateAssessmentRepositoryImpl candidateAssessmentRepository;

    @Test
    void saveCandidateAssessment_shouldSaveSuccessfully() {
        // Arrange
        CandidateAssessment assessment = new CandidateAssessment();

        // Act
        assertDoesNotThrow(()->candidateAssessmentRepository.saveCandidateAssessment(assessment));
        ;

        // Assert
        verify(candidateAssessmentTable, times(1)).putItem(assessment);
    }

    @Test
    void saveCandidateAssessment_shouldThrowProcessFailedExceptionOnFailure() {
        // Arrange
        CandidateAssessment assessment = new CandidateAssessment();
        doThrow(RuntimeException.class).when(candidateAssessmentTable).putItem(assessment);

        assertThrows(ProcessFailedException.class, ()->candidateAssessmentRepository.saveCandidateAssessment(assessment));
    }

    @Test
    void updateCandidateAssessment_shouldUpdateSuccessfully() {
        // Arrange
        CandidateAssessment assessment = new CandidateAssessment();
        UpdateItemEnhancedRequest<CandidateAssessment> request = UpdateItemEnhancedRequest.builder(CandidateAssessment.class)
                .item(assessment)
                .ignoreNulls(true)
                .build();

        // Act
        assertDoesNotThrow(()->candidateAssessmentRepository.updateCandidateAssessment(assessment));
        // Assert
        verify(candidateAssessmentTable, times(1)).updateItem(request);
    }

    @Test
    void updateCandidateAssessment_shouldThrowProcessFailedExceptionOnFailure() {
        // Arrange
        CandidateAssessment assessment = new CandidateAssessment();
        UpdateItemEnhancedRequest<CandidateAssessment> request = UpdateItemEnhancedRequest.builder(CandidateAssessment.class)
                .item(assessment)
                .ignoreNulls(true)
                .build();
        doThrow(RuntimeException.class).when(candidateAssessmentTable).updateItem(request);

        // Act & Assert
        assertThrows(ProcessFailedException.class, ()->candidateAssessmentRepository.updateCandidateAssessment(assessment));

    }

    @Test
    void getSpecificCandidateAssessment_shouldReturnAssessment() {
        // Arrange
        Key key = Key.builder().partitionValue("pk").sortValue("sk").build();
        CandidateAssessment expectedAssessment = new CandidateAssessment();
        when(candidateAssessmentTable.getItem(key)).thenReturn(expectedAssessment);

        // Act
        CandidateAssessment result = candidateAssessmentRepository.getSpecificCandidateAssessment(key);

        // Assert
        assertEquals(expectedAssessment, result);
        verify(candidateAssessmentTable, times(1)).getItem(key);
    }

    @Test
    void getSpecificCandidateAssessment_shouldThrowProcessFailedExceptionOnFailure() {
        // Arrange
        Key key = Key.builder().partitionValue("pk").sortValue("sk").build();
        when(candidateAssessmentTable.getItem(key)).thenThrow(RuntimeException.class);

        // Act & Assert
        assertThrows(ProcessFailedException.class, () ->
                candidateAssessmentRepository.getSpecificCandidateAssessment(key));
    }
}
