package com.amap.amapreportmanagementservice.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class CandidateRiskCalculatorTest {

    @Test
    void testCalculateRiskLevelScore_NoViolations_ShouldReturnDefaultScore() {
        int violations = 0;
        int expectedScore = 100;
        int actualScore = CandidateRiskCalculator.calculateRiskLevelScore(violations);
        assertEquals(expectedScore, actualScore);
    }

    @Test
    void testCalculateRiskLevelScore_FewViolations_ShouldReturnAdjustedScore() {
        int violations = 3;
        int expectedScore = 70;
        int actualScore = CandidateRiskCalculator.calculateRiskLevelScore(violations);
        assertEquals(expectedScore, actualScore);
    }

    @Test
    void testCalculateRiskLevelScore_ManyViolations_ShouldReturnZero() {
        int violations = 15;
        int expectedScore = 0;
        int actualScore = CandidateRiskCalculator.calculateRiskLevelScore(violations);
        assertEquals(expectedScore, actualScore);
    }

    @Test
    void testDetermineRiskLevel_LowRisk_ShouldReturnLowRisk() {
        int score = 85;
        String expectedLevel = "Low Risk";
        String actualLevel = CandidateRiskCalculator.determineRiskLevel(score);
        assertEquals(expectedLevel, actualLevel);
    }

    @Test
    void testDetermineRiskLevel_MediumRisk_ShouldReturnMediumRisk() {
        int score = 60;
        String expectedLevel = "Medium Risk";
        String actualLevel = CandidateRiskCalculator.determineRiskLevel(score);
        assertEquals(expectedLevel, actualLevel);
    }

    @Test
    void testDetermineRiskLevel_HighRisk_ShouldReturnHighRisk() {
        int score = 30;
        String expectedLevel = "High Risk";
        String actualLevel = CandidateRiskCalculator.determineRiskLevel(score);
        assertEquals(expectedLevel, actualLevel);
    }

    @Test
    void testDetermineRiskLevel_BoundaryLowRisk_ShouldReturnLowRisk() {
        int score = 80;
        String expectedLevel = "Low Risk";
        String actualLevel = CandidateRiskCalculator.determineRiskLevel(score);
        assertEquals(expectedLevel, actualLevel);
    }

    @Test
    void testDetermineRiskLevel_BoundaryMediumRisk_ShouldReturnMediumRisk() {
        int score = 50;
        String expectedLevel = "Medium Risk";
        String actualLevel = CandidateRiskCalculator.determineRiskLevel(score);
        assertEquals(expectedLevel, actualLevel);
    }

    @Test
    void testDetermineRiskLevel_ZeroScore_ShouldReturnHighRisk() {
        int score = 0;
        String expectedLevel = "High Risk";
        String actualLevel = CandidateRiskCalculator.determineRiskLevel(score);
        assertEquals(expectedLevel, actualLevel);
    }
}