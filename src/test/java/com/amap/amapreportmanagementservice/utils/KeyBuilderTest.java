package com.amap.amapreportmanagementservice.utils;

import com.amap.amapreportmanagementservice.constants.DbKeysPrefixes;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class KeyBuilderTest {

    private static final String ORG_ID = "org123";
    private static final String ASSESSMENT_ID = "assess456";
    private static final String CANDIDATE_EMAIL = "<EMAIL>";
    private static final String CANDIDATE_ID = "cand789";
    private static final String TEST_ID = "test101";
    private static final String QUESTION_ID = "question202";

    @Test
    void testAssessmentPK() {
        String expected = DbKeysPrefixes.ORGANIZATION_PREFIX + ORG_ID + DbKeysPrefixes.ASSESSMENT_PREFIX + ASSESSMENT_ID;
        assertEquals(expected, KeyBuilder.assessmentPK(ORG_ID, ASSESSMENT_ID));
    }

    @Test
    void testAssessmentSK() {
        String expected = DbKeysPrefixes.ORGANIZATION_PREFIX + ORG_ID + DbKeysPrefixes.ASSESSMENT_PREFIX + ASSESSMENT_ID;
        assertEquals(expected, KeyBuilder.assessmentSK(ORG_ID, ASSESSMENT_ID));
    }

    @Test
    void testCandidateAssessmentPK() {
        String expected = DbKeysPrefixes.ORGANIZATION_PREFIX + ORG_ID + DbKeysPrefixes.ASSESSMENT_PREFIX + ASSESSMENT_ID;
        assertEquals(expected, KeyBuilder.candidateAssessmentPK(ORG_ID, ASSESSMENT_ID));
    }

    @Test
    void testCandidateAssessmentSK() {
        String expected = DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX + CANDIDATE_EMAIL + DbKeysPrefixes.CANDIDATE_ID_PREFIX + CANDIDATE_ID;
        assertEquals(expected, KeyBuilder.candidateAssessmentSK(CANDIDATE_EMAIL, CANDIDATE_ID));
    }

    @Test
    void testAssessmentTestPK() {
        String expected = DbKeysPrefixes.ORGANIZATION_PREFIX + ORG_ID + DbKeysPrefixes.ASSESSMENT_PREFIX + ASSESSMENT_ID;
        assertEquals(expected, KeyBuilder.assessmentTestPK(ORG_ID, ASSESSMENT_ID));
    }

    @Test
    void testAssessmentTestSK() {
        String expected = DbKeysPrefixes.ASSESSMENT_PREFIX + ASSESSMENT_ID + DbKeysPrefixes.TEST_PREFIX + TEST_ID;
        assertEquals(expected, KeyBuilder.assessmentTestSK(ASSESSMENT_ID, TEST_ID));
    }

    @Test
    void testCandidateTestPK() {
        String expected = DbKeysPrefixes.ORGANIZATION_PREFIX + ORG_ID + DbKeysPrefixes.ASSESSMENT_PREFIX + ASSESSMENT_ID;
        assertEquals(expected, KeyBuilder.candidateTestPK(ORG_ID, ASSESSMENT_ID));
    }

    @Test
    void testCandidateTestSK() {
        String expected = DbKeysPrefixes.TEST_PREFIX + TEST_ID + DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX + CANDIDATE_EMAIL + DbKeysPrefixes.CANDIDATE_ID_PREFIX + CANDIDATE_ID;
        assertEquals(expected, KeyBuilder.candidateTestSK(TEST_ID, CANDIDATE_EMAIL, CANDIDATE_ID));
    }

    @Test
    void testTestQuestionPK() {
        String expected = DbKeysPrefixes.ORGANIZATION_PREFIX + ORG_ID + DbKeysPrefixes.ASSESSMENT_PREFIX + ASSESSMENT_ID + DbKeysPrefixes.TEST_PREFIX + TEST_ID;
        assertEquals(expected, KeyBuilder.testQuestionPK(ORG_ID, ASSESSMENT_ID, TEST_ID));
    }

    @Test
    void testTestQuestionSK() {
        String expected = DbKeysPrefixes.QUESTION_PREFIX + QUESTION_ID;
        assertEquals(expected, KeyBuilder.testQuestionSK(QUESTION_ID));
    }

    @Test
    void testCandidateQuestionPK() {
        String expected = DbKeysPrefixes.ORGANIZATION_PREFIX + ORG_ID + DbKeysPrefixes.ASSESSMENT_PREFIX + ASSESSMENT_ID + DbKeysPrefixes.TEST_PREFIX + TEST_ID;
        assertEquals(expected, KeyBuilder.candidateQuestionPK(ORG_ID, ASSESSMENT_ID, TEST_ID));
    }

    @Test
    void testCandidateQuestionSK() {
        String expected = DbKeysPrefixes.QUESTION_PREFIX + QUESTION_ID + DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX + CANDIDATE_EMAIL + DbKeysPrefixes.CANDIDATE_ID_PREFIX + CANDIDATE_ID;
        assertEquals(expected, KeyBuilder.candidateQuestionSK(QUESTION_ID, CANDIDATE_EMAIL, CANDIDATE_ID));
    }

    @Test
    void testCanEmailSk() {
        String expected = DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX + CANDIDATE_EMAIL;
        assertEquals(expected, KeyBuilder.canEmailSk(CANDIDATE_EMAIL));
    }

    @Test
    void testCandidateFeedbackPK() {
        String expected = DbKeysPrefixes.ORGANIZATION_PREFIX + ORG_ID + DbKeysPrefixes.ASSESSMENT_PREFIX + ASSESSMENT_ID;
        assertEquals(expected, KeyBuilder.candidateFeedbackPK(ORG_ID, ASSESSMENT_ID));
    }

    @Test
    void testCandidateFeedbackSk() {
        String expected = DbKeysPrefixes.CANDIDATE_ID_PREFIX + CANDIDATE_ID;
        assertEquals(expected, KeyBuilder.candidateFeedbackSk(CANDIDATE_ID));
    }

    @Test
    void testQuestionFlaggingPK() {
        String expected = DbKeysPrefixes.ORGANIZATION_PREFIX + ORG_ID + DbKeysPrefixes.ASSESSMENT_PREFIX + ASSESSMENT_ID;
        assertEquals(expected, KeyBuilder.questionFlaggingPK(ORG_ID, ASSESSMENT_ID));
    }

    @Test
    void testQuestionFlaggingSK() {
        String expected = DbKeysPrefixes.QUESTION_PREFIX + QUESTION_ID;
        assertEquals(expected, KeyBuilder.questionFlaggingSK(QUESTION_ID));
    }

    @Test
    void testWebhookReportPK(){
        String expected = DbKeysPrefixes.ORGANIZATION_PREFIX + ORG_ID;
        assertEquals(expected, KeyBuilder.webhookReportPK(ORG_ID));
    }

    @Test
    void testWebhookReportSK(){
        String expected = DbKeysPrefixes.WEBHOOK_PREFIX+CANDIDATE_ID;
        assertEquals(expected, KeyBuilder.webhookReportSK(CANDIDATE_ID));
    }
}