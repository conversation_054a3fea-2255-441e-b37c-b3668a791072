package com.amap.amapreportmanagementservice.utils;

import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

class HelperTest {

    @Test
    void testAverageCalculator_ShouldReturnCorrectAverage() {
        double numerator = 10.0;
        double denominator = 2.0;
        double expectedAverage = 5.0;
        double actualAverage = Helper.averageCalculator(numerator, denominator);
        assertEquals(expectedAverage, actualAverage);

        numerator = 15.0;
        denominator = 3.0;
        expectedAverage = 5.0;
        actualAverage = Helper.averageCalculator(numerator, denominator);
        assertEquals(expectedAverage, actualAverage);

        numerator = 7.0;
        denominator = 2.0;
        expectedAverage = 3.5;
        actualAverage = Helper.averageCalculator(numerator, denominator);
        assertEquals(expectedAverage, actualAverage);
    }

    @Test
    void testAveragePercentageCalculator_ShouldReturnCorrectPercentage() {
        double numerator = 5.0;
        double denominator = 10.0;
        double expectedPercentage = 50.0;
        double actualPercentage = Helper.averagePercentageCalculator(numerator, denominator);
        assertEquals(expectedPercentage, actualPercentage);

        numerator = 3.0;
        denominator = 4.0;
        expectedPercentage = 75.0;
        actualPercentage = Helper.averagePercentageCalculator(numerator, denominator);
        assertEquals(expectedPercentage, actualPercentage);

        numerator = 1.0;
        denominator = 2.0;
        expectedPercentage = 50.0;
        actualPercentage = Helper.averagePercentageCalculator(numerator, denominator);
        assertEquals(expectedPercentage, actualPercentage);
    }

    @Test
    void testDurationCalculator_ShouldReturnCorrectDuration() {
        ZonedDateTime startTime = ZonedDateTime.of(2023, 10, 27, 10, 0, 0, 0, ZoneOffset.UTC);
        ZonedDateTime endTime = ZonedDateTime.of(2023, 10, 27, 11, 30, 0, 0, ZoneOffset.UTC);
        Duration expectedDuration = Duration.ofMinutes(90);
        Duration actualDuration = Helper.durationCalculator(startTime, endTime);
        assertEquals(expectedDuration, actualDuration);

        startTime = ZonedDateTime.of(2023, 10, 27, 10, 0, 0, 0, ZoneOffset.UTC);
        endTime = ZonedDateTime.of(2023, 10, 27, 10, 15, 0, 0, ZoneOffset.UTC);
        expectedDuration = Duration.ofMinutes(15);
        actualDuration = Helper.durationCalculator(startTime, endTime);
        assertEquals(expectedDuration, actualDuration);

        startTime = ZonedDateTime.of(2023, 10, 27, 10, 0, 0, 0, ZoneOffset.UTC);
        endTime = ZonedDateTime.of(2023, 10, 28, 10, 0, 0, 0, ZoneOffset.UTC);
        expectedDuration = Duration.ofHours(24);
        actualDuration = Helper.durationCalculator(startTime, endTime);
        assertEquals(expectedDuration, actualDuration);
    }

    @Test
    void testDurationCalculator_startTimeAfterEndTime_ShouldReturnNegativeDuration() {
        ZonedDateTime startTime = ZonedDateTime.of(2023, 10, 27, 11, 0, 0, 0, ZoneOffset.UTC);
        ZonedDateTime endTime = ZonedDateTime.of(2023, 10, 27, 10, 0, 0, 0, ZoneOffset.UTC);
        Duration actualDuration = Helper.durationCalculator(startTime, endTime);
        assertEquals(Duration.ofHours(-1), actualDuration);
    }

    @Test
    void testAverageCalculator_divideByZero_ShouldReturnInfinity() {
        double numerator = 10.0;
        double denominator = 0.0;
        double actualAverage = Helper.averageCalculator(numerator, denominator);
        assertEquals(Double.POSITIVE_INFINITY, actualAverage);
    }

    @Test
    void testAveragePercentageCalculator_divideByZero_ShouldReturnInfinity() {
        double numerator = 10.0;
        double denominator = 0.0;
        double actualPercentage = Helper.averagePercentageCalculator(numerator, denominator);
        assertEquals(Double.POSITIVE_INFINITY, actualPercentage);
    }
}