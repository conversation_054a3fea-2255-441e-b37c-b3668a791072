import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';

export class CandidateAssessment extends BaseEntity {
  @ApiProperty({ description: 'Candidate unique identifier' })
  candidateId: string;

  @ApiProperty({ description: 'Assessment title' })
  title: string;

  @ApiProperty({ description: 'Candidate email address' })
  candidateEmail: string;

  @ApiProperty({ description: 'Assessment unique identifier' })
  assessmentId: string;

  @ApiProperty({ description: 'Time taken to complete assessment in minutes' })
  timeTaken: number;

  @ApiProperty({ description: 'Assessment completion status' })
  status: string;

  @ApiProperty({ description: 'Total score possible' })
  totalScore: number;

  @ApiProperty({ description: 'Candidate marks obtained' })
  candidateMarks: number;

  @ApiProperty({ description: 'Assessment start time' })
  assessmentStartTime: string;

  @ApiProperty({ description: 'Assessment end time' })
  assessmentEndTime: string;

  @ApiProperty({ description: 'Score percentage' })
  scorePercentage: number;

  @ApiProperty({ description: 'Proctor name' })
  proctor: string;

  @ApiProperty({ description: 'Proctor level' })
  proctorLevel: string;

  @ApiProperty({ description: 'Integrity score' })
  integrityScore: number;

  @ApiProperty({ description: 'Assessment duration' })
  duration: number;

  @ApiProperty({ description: 'Assessment window violation count' })
  assessmentWindowViolationCount: number;

  @ApiProperty({ description: 'Assessment window violation duration' })
  assessmentWindowViolationDuration: number;

  @ApiProperty({ description: 'Assessment taker shot count' })
  assessmentTakerShotCount: number;

  @ApiProperty({ description: 'Assessment taker violation shot count' })
  assessmentTakerViolationShotCount: number;

  @ApiProperty({ description: 'Window shot count' })
  windowShotCount: number;

  @ApiProperty({ description: 'Window violation shot count' })
  windowViolationShotCount: number;

  @ApiProperty({ description: 'Assessment duration in minutes' })
  assessmentDuration: number;

  constructor(data?: Partial<CandidateAssessment>) {
    super();
    if (data) {
      Object.assign(this, data);
    }
  }
}
