import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';

export class Assessment extends BaseEntity {
  @ApiProperty({ description: 'Assessment unique identifier' })
  assessmentId: string;

  @ApiProperty({ description: 'Assessment duration in minutes' })
  assessmentDuration: number;

  @ApiProperty({ description: 'Assessment title' })
  title: string;

  @ApiProperty({ description: 'Average time taken by candidates' })
  averageTimeTaken: number;

  @ApiProperty({ description: 'Whether this is a system assessment' })
  isSystem: boolean;

  @ApiProperty({ description: 'Number of test takers' })
  numberOfTakers: number;

  @ApiProperty({ description: 'Number of unique test takers' })
  numberOfUniqueTakers: number;

  @ApiProperty({ description: 'Total score possible' })
  totalScore: number;

  @ApiProperty({ description: 'List of test IDs associated with this assessment' })
  testsIds: string[];

  @ApiProperty({ description: 'Number of candidates in progress' })
  progressCount: number;

  @ApiProperty({ description: 'Number of completed assessments' })
  completedCount: number;

  @ApiProperty({ description: 'Average percentage score' })
  averagePercentage: number;

  @ApiProperty({ description: 'Passing mark threshold' })
  passMark: number;

  @ApiProperty({ description: 'Assessment instructions' })
  instruction: string;

  @ApiProperty({ description: 'Assessment expiration date' })
  expireDate: string;

  @ApiProperty({ description: 'Assessment commencement date' })
  commenceDate: string;

  @ApiProperty({ description: 'Number of feedbacks received' })
  numberOfFeedbacks: number;

  @ApiProperty({ description: 'Number of completed status assessments' })
  numberOfCompletedStatus: number;

  @ApiProperty({ description: 'Number of incomplete status assessments' })
  numberOfIncompleteStatus: number;

  constructor(data?: Partial<Assessment>) {
    super();
    if (data) {
      Object.assign(this, data);
    }
  }
}
