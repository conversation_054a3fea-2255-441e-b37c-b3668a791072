import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';

export interface CodeTemplate {
  id: string;
  codeType: string;
  body: string;
  questionId: string;
  languageId: string;
  language: Language;
}

export interface Language {
  id: string;
  name: string;
  version: string;
}

export interface CodeResult {
  error: string;
  memory: number;
  inQueue: boolean;
  statusId: number;
  isCorrect: boolean;
  actualOutput: string;
  testCaseId: string;
  executionTime: string;
  statusDescription: string;
}

export interface CodeExecutionSummary {
  totalTestCases: number;
  passedTestCases: number;
  failedTestCases: number;
  executionTime: string;
  memoryUsed: number;
}

export interface CodeConstraint {
  timeLimit: number;
  memoryLimit: number;
  languageRestrictions: string[];
}

export interface ReferenceSolution {
  id: string;
  language: string;
  code: string;
  explanation: string;
}

export class CandidateQuestionResult extends BaseEntity {
  @ApiProperty({ description: 'Candidate unique identifier' })
  candidateId: string;

  @ApiProperty({ description: 'Question unique identifier' })
  questionId: string;

  @ApiProperty({ description: 'Candidate email address' })
  candidateEmail: string;

  @ApiProperty({ description: 'Question text content' })
  questionText: string;

  @ApiProperty({ description: 'Question domain/subject area' })
  domain: string;

  @ApiProperty({ description: 'Question category' })
  category: string;

  @ApiProperty({ description: 'Question type' })
  questionType: string;

  @ApiProperty({ description: 'Total score possible for this question' })
  totalScore: number;

  @ApiProperty({ description: 'Candidate marks obtained' })
  candidateMarks: number;

  @ApiProperty({ description: 'Question difficulty level' })
  difficultyLevel: string;

  @ApiProperty({ description: 'Time limit for this question in minutes' })
  timeLimit: number;

  @ApiProperty({ description: 'Whether this question was flagged' })
  isFlagged: string;

  @ApiProperty({ description: 'Test taker answers' })
  testTakerAnswers: string[];

  @ApiProperty({ description: 'Available option answers' })
  optionAnswers: string[];

  @ApiProperty({ description: 'Correct answers' })
  correctAnswers: string[];

  @ApiProperty({ description: 'Whether this is a comprehension question' })
  isComprehension: boolean;

  @ApiProperty({ description: 'Whether this question was answered' })
  isAnswered: boolean;

  @ApiProperty({ description: 'Whether the answer was correct' })
  isAnswerCorrect: string;

  @ApiProperty({ description: 'Essay rubrics for essay questions' })
  essayRubrics: string;

  @ApiProperty({ description: 'Code review comments' })
  codeReview: string;

  @ApiProperty({ description: 'Code templates for programming questions' })
  codeTemplates: CodeTemplate[];

  @ApiProperty({ description: 'Code execution results' })
  codeResults: CodeResult[];

  @ApiProperty({ description: 'Code execution summary' })
  codeExecutionSummary: CodeExecutionSummary;

  @ApiProperty({ description: 'Code constraints' })
  codeConstraint: CodeConstraint;

  @ApiProperty({ description: 'Reference solutions' })
  referenceSolution: ReferenceSolution[];

  constructor(data?: Partial<CandidateQuestionResult>) {
    super();
    if (data) {
      Object.assign(this, data);
    }
  }
}
