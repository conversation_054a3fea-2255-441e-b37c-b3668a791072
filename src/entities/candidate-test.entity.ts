import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';

export class CandidateTest extends BaseEntity {
  @ApiProperty({ description: 'Candidate unique identifier' })
  candidateId: string;

  @ApiProperty({ description: 'Candidate email address' })
  candidateEmail: string;

  @ApiProperty({ description: 'Test unique identifier' })
  testId: string;

  @ApiProperty({ description: 'Total score possible for this test' })
  totalScore: number;

  @ApiProperty({ description: 'Test domain/subject area' })
  domain: string;

  @ApiProperty({ description: 'Candidate marks obtained' })
  candidateMarks: number;

  @ApiProperty({ description: 'Score percentage' })
  scorePercentage: number;

  @ApiProperty({ description: 'Test passage or description' })
  passage: string;

  @ApiProperty({ description: 'Time taken to complete test' })
  timeTaken: number;

  @ApiProperty({ description: 'Number of questions failed' })
  numberOfQuestionsFailed: number;

  @ApiProperty({ description: 'Number of questions passed' })
  numberOfQuestionsPassed: number;

  @ApiProperty({ description: 'Number of questions answered' })
  numberOfQuestionsAnswered: number;

  @ApiProperty({ description: 'Total number of questions' })
  numberOfQuestions: number;

  @ApiProperty({ description: 'Test window violation duration' })
  testWindowViolationDuration: number;

  @ApiProperty({ description: 'Test window violation count' })
  testWindowViolationCount: number;

  @ApiProperty({ description: 'Test taker shot count' })
  testTakerShotCount: number;

  @ApiProperty({ description: 'Test taker violation shot count' })
  testTakerViolationShotCount: number;

  @ApiProperty({ description: 'Test window shot count' })
  testWindowShotCount: number;

  constructor(data?: Partial<CandidateTest>) {
    super();
    if (data) {
      Object.assign(this, data);
    }
  }
}
