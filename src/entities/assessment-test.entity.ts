import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';

export class AssessmentTest extends BaseEntity {
  @ApiProperty({ description: 'Assessment unique identifier' })
  assessmentId: string;

  @ApiProperty({ description: 'Test title' })
  title: string;

  @ApiProperty({ description: 'Test unique identifier' })
  testId: string;

  @ApiProperty({ description: 'Test domain/subject area' })
  domain: string;

  @ApiProperty({ description: 'Total score possible for this test' })
  totalScore: number;

  @ApiProperty({ description: 'Average score achieved by candidates' })
  averageScore: number;

  @ApiProperty({ description: 'Test duration in minutes' })
  duration: number;

  @ApiProperty({ description: 'Test passage or description' })
  passage: string;

  @ApiProperty({ description: 'Total candidate marks for this test' })
  totalCandidateMarks: number;

  @ApiProperty({ description: 'Total candidate percentage for this test' })
  totalCandidatePercentage: number;

  @ApiProperty({ description: 'Average percentage score' })
  averagePercentageScore: number;

  @ApiProperty({ description: 'Number of times this test was answered' })
  numberOfTimesAnswered: number;

  @ApiProperty({ description: 'Number of flags raised for this test' })
  numberOfFlags: number;

  @ApiProperty({ description: 'List of question IDs in this test' })
  questionIds: string[];

  constructor(data?: Partial<AssessmentTest>) {
    super();
    if (data) {
      Object.assign(this, data);
    }
  }
}
