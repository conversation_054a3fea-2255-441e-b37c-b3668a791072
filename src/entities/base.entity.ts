import { ApiProperty } from '@nestjs/swagger';

export abstract class BaseEntity {
  @ApiProperty({ description: 'Partition key for DynamoDB' })
  pk: string;

  @ApiProperty({ description: 'Sort key for DynamoDB' })
  sk: string;

  @ApiProperty({ description: 'Organization identifier' })
  organizationId: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: string;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: string;

  constructor() {
    const now = new Date().toISOString();
    this.createdAt = now;
    this.updatedAt = now;
  }

  updateTimestamp(): void {
    this.updatedAt = new Date().toISOString();
  }
}
