import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';

export class QuestionFlagging extends BaseEntity {
  @ApiProperty({ description: 'Question unique identifier' })
  questionId: string;

  @ApiProperty({ description: 'Test unique identifier' })
  testId: string;

  @ApiProperty({ description: 'Assessment unique identifier' })
  assessmentId: string;

  @ApiProperty({ description: 'Test taker unique identifier' })
  testTakerId: string;

  @ApiProperty({ description: 'Question text content' })
  questionText: string;

  @ApiProperty({ description: 'Test taker email address' })
  testTakerEmail: string;

  @ApiProperty({ description: 'Reasons for flagging this question' })
  reasonOfFlagging: string[];

  constructor(data?: Partial<QuestionFlagging>) {
    super();
    if (data) {
      Object.assign(this, data);
    }
  }
}
