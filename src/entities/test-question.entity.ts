import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';

export class TestQuestion extends BaseEntity {
  @ApiProperty({ description: 'Assessment unique identifier' })
  assessmentId: string;

  @ApiProperty({ description: 'Test unique identifier' })
  testId: string;

  @ApiProperty({ description: 'Question unique identifier' })
  questionId: string;

  @ApiProperty({ description: 'Question text content' })
  questionText: string;

  @ApiProperty({ description: 'Question domain/subject area' })
  domain: string;

  @ApiProperty({ description: 'Question category' })
  category: string;

  @ApiProperty({ description: 'Number of times this question was missed' })
  numberOfTimesMissed: number;

  @ApiProperty({ description: 'Number of times this question was answered' })
  numberOfTimesAnswered: number;

  @ApiProperty({ description: 'Question title' })
  title: string;

  @ApiProperty({ description: 'Question duration in minutes' })
  duration: number;

  @ApiProperty({ description: 'Question difficulty level' })
  difficultyLevel: string;

  @ApiProperty({ description: 'Domain identifier' })
  domainId: string;

  @ApiProperty({ description: 'Category identifier' })
  categoryId: string;

  @ApiProperty({ description: 'Number of flags raised for this question' })
  numberOfFlags: number;

  @ApiProperty({ description: 'List of related question IDs' })
  questionIds: string[];

  @ApiProperty({ description: 'Total score for this question' })
  totalScore: number;

  @ApiProperty({ description: 'Code template for programming questions' })
  codeTemplate: string;

  @ApiProperty({ description: 'Code constraints for programming questions' })
  codeConstraint: string;

  constructor(data?: Partial<TestQuestion>) {
    super();
    if (data) {
      Object.assign(this, data);
    }
  }
}
