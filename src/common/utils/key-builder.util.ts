import { DB_KEYS_PREFIXES } from '../constants/db-keys-prefixes';

export class KeyBuilderUtil {
  static assessmentPK(organizationId: string): string {
    return `${DB_KEYS_PREFIXES.ORGANIZATION_PREFIX}${organizationId}`;
  }

  static assessmentSK(assessmentId: string): string {
    return `${DB_KEYS_PREFIXES.ASSESSMENT_PREFIX}${assessmentId}`;
  }

  static candidateAssessmentPK(organizationId: string, assessmentId: string): string {
    return `${DB_KEYS_PREFIXES.ORGANIZATION_PREFIX}${organizationId}#${DB_KEYS_PREFIXES.ASSESSMENT_PREFIX}${assessmentId}`;
  }

  static candidateAssessmentSK(candidateId: string): string {
    return `${DB_KEYS_PREFIXES.CANDIDATE_ID_PREFIX}${candidateId}`;
  }

  static candidateAssessmentEmailSK(candidateEmail: string): string {
    return `${DB_KEYS_PREFIXES.CANDIDATE_EMAIL_PREFIX}${candidateEmail}`;
  }

  static assessmentTestPK(organizationId: string, assessmentId: string): string {
    return `${DB_KEYS_PREFIXES.ORGANIZATION_PREFIX}${organizationId}#${DB_KEYS_PREFIXES.ASSESSMENT_PREFIX}${assessmentId}`;
  }

  static assessmentTestSK(testId: string): string {
    return `${DB_KEYS_PREFIXES.TEST_PREFIX}${testId}`;
  }

  static testQuestionPK(organizationId: string, assessmentId: string, testId: string): string {
    return `${DB_KEYS_PREFIXES.ORGANIZATION_PREFIX}${organizationId}#${DB_KEYS_PREFIXES.ASSESSMENT_PREFIX}${assessmentId}#${DB_KEYS_PREFIXES.TEST_PREFIX}${testId}`;
  }

  static testQuestionSK(questionId: string): string {
    return `${DB_KEYS_PREFIXES.QUESTION_PREFIX}${questionId}`;
  }

  static candidateQuestionPK(organizationId: string, assessmentId: string, candidateId: string): string {
    return `${DB_KEYS_PREFIXES.ORGANIZATION_PREFIX}${organizationId}#${DB_KEYS_PREFIXES.ASSESSMENT_PREFIX}${assessmentId}#${DB_KEYS_PREFIXES.CANDIDATE_ID_PREFIX}${candidateId}`;
  }

  static candidateQuestionSK(questionId: string): string {
    return `${DB_KEYS_PREFIXES.QUESTION_PREFIX}${questionId}`;
  }

  static candidateTestPK(organizationId: string, assessmentId: string, candidateId: string): string {
    return `${DB_KEYS_PREFIXES.ORGANIZATION_PREFIX}${organizationId}#${DB_KEYS_PREFIXES.ASSESSMENT_PREFIX}${assessmentId}#${DB_KEYS_PREFIXES.CANDIDATE_ID_PREFIX}${candidateId}`;
  }

  static candidateTestSK(testId: string): string {
    return `${DB_KEYS_PREFIXES.TEST_PREFIX}${testId}`;
  }

  static questionFlaggingPK(organizationId: string, assessmentId: string): string {
    return `${DB_KEYS_PREFIXES.ORGANIZATION_PREFIX}${organizationId}#${DB_KEYS_PREFIXES.ASSESSMENT_PREFIX}${assessmentId}`;
  }

  static questionFlaggingSK(questionId: string): string {
    return `${DB_KEYS_PREFIXES.QUESTION_PREFIX}${questionId}#FLAGGED`;
  }

  static webhookPK(organizationId: string): string {
    return `${DB_KEYS_PREFIXES.ORGANIZATION_PREFIX}${organizationId}`;
  }

  static webhookSK(webhookId: string): string {
    return `${DB_KEYS_PREFIXES.WEBHOOK_PREFIX}${webhookId}`;
  }
}
