import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Kafka } from 'kafkajs';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'KAFKA_CLIENT',
      useFactory: (configService: ConfigService) => {
        return new Kafka({
          clientId: configService.get('kafka.clientId'),
          brokers: configService.get('kafka.brokers'),
        });
      },
      inject: [ConfigService],
    },
    {
      provide: 'KAFKA_PRODUCER',
      useFactory: async (kafka: Kafka) => {
        const producer = kafka.producer();
        await producer.connect();
        return producer;
      },
      inject: ['KAFKA_CLIENT'],
    },
    {
      provide: 'KAFKA_CONSUMER',
      useFactory: async (kafka: Kafka, configService: ConfigService) => {
        const consumer = kafka.consumer({ 
          groupId: configService.get('kafka.groupId') 
        });
        await consumer.connect();
        return consumer;
      },
      inject: ['KAFKA_CLIENT', ConfigService],
    },
  ],
  exports: ['KAFKA_CLIENT', 'KAFKA_PRODUCER', 'KAFKA_CONSUMER'],
})
export class KafkaModule {}
