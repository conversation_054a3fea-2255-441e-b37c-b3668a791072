import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'DYNAMODB_CLIENT',
      useFactory: (configService: ConfigService) => {
        const client = new DynamoDBClient({
          region: configService.get('database.dynamodb.region'),
          endpoint: configService.get('database.dynamodb.endpoint'),
          credentials: configService.get('database.dynamodb.accessKeyId') ? {
            accessKeyId: configService.get('database.dynamodb.accessKeyId'),
            secretAccessKey: configService.get('database.dynamodb.secretAccessKey'),
          } : undefined,
        });

        return DynamoDBDocumentClient.from(client);
      },
      inject: [ConfigService],
    },
    {
      provide: 'DYNAMODB_TABLE_NAME',
      useFactory: (configService: ConfigService) => {
        return configService.get('database.dynamodb.tableName');
      },
      inject: [ConfigService],
    },
  ],
  exports: ['DYNAMODB_CLIENT', 'DYNAMODB_TABLE_NAME'],
})
export class DatabaseModule {}
