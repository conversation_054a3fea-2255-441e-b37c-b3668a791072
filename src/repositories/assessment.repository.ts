import { Injectable } from '@nestjs/common';
import { Assessment } from '../entities';
import { BaseRepository } from './base.repository';
import { AssessmentRepositoryInterface } from './interfaces/assessment-repository.interface';
import { KeyBuilderUtil } from '../common/utils/key-builder.util';
import { EntityNotFoundException } from '../common/exceptions/custom-exceptions';

@Injectable()
export class AssessmentRepository extends BaseRepository<Assessment> implements AssessmentRepositoryInterface {
  
  async findByOrganizationId(organizationId: string): Promise<Assessment[]> {
    const pk = KeyBuilderUtil.assessmentPK(organizationId);
    return this.findByPartitionKey(pk);
  }

  async checkIfExists(organizationId: string, assessmentId: string): Promise<boolean> {
    const pk = KeyBuilderUtil.assessmentPK(organizationId);
    const sk = KeyBuilderUtil.assessmentSK(assessmentId);
    return this.exists(pk, sk);
  }

  async findByAssessmentId(organizationId: string, assessmentId: string): Promise<Assessment | null> {
    const pk = KeyBuilderUtil.assessmentPK(organizationId);
    const sk = KeyBuilderUtil.assessmentSK(assessmentId);
    
    const assessment = await this.findByKey(pk, sk);
    if (!assessment) {
      throw new EntityNotFoundException(`Assessment with ID ${assessmentId} not found`);
    }
    
    return assessment;
  }

  async saveAssessment(assessment: Assessment): Promise<void> {
    const pk = KeyBuilderUtil.assessmentPK(assessment.organizationId);
    const sk = KeyBuilderUtil.assessmentSK(assessment.assessmentId);
    
    assessment.pk = pk;
    assessment.sk = sk;
    assessment.updateTimestamp();
    
    await this.save(assessment);
  }

  async updateAssessment(assessment: Assessment): Promise<void> {
    const pk = KeyBuilderUtil.assessmentPK(assessment.organizationId);
    const sk = KeyBuilderUtil.assessmentSK(assessment.assessmentId);
    
    assessment.pk = pk;
    assessment.sk = sk;
    assessment.updateTimestamp();
    
    await this.update(assessment);
  }
}
