import { Assessment } from '../../entities';
import { BaseRepositoryInterface } from './base-repository.interface';

export interface AssessmentRepositoryInterface extends BaseRepositoryInterface<Assessment> {
  findByOrganizationId(organizationId: string): Promise<Assessment[]>;
  checkIfExists(organizationId: string, assessmentId: string): Promise<boolean>;
  findByAssessmentId(organizationId: string, assessmentId: string): Promise<Assessment | null>;
}
