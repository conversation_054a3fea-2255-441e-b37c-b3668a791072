import { CandidateAssessment } from '../../entities';
import { BaseRepositoryInterface } from './base-repository.interface';

export interface CandidateAssessmentRepositoryInterface extends BaseRepositoryInterface<CandidateAssessment> {
  findByAssessment(organizationId: string, assessmentId: string): Promise<CandidateAssessment[]>;
  findByCandidate(organizationId: string, assessmentId: string, candidateId: string): Promise<CandidateAssessment | null>;
  findByCandidateEmail(organizationId: string, assessmentId: string, candidateEmail: string): Promise<CandidateAssessment | null>;
}
