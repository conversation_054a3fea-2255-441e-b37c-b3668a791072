import { Inject, Injectable, Logger } from '@nestjs/common';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { 
  PutCommand, 
  GetCommand, 
  UpdateCommand, 
  DeleteCommand, 
  QueryCommand,
  QueryCommandInput 
} from '@aws-sdk/lib-dynamodb';
import { BaseRepositoryInterface } from './interfaces/base-repository.interface';
import { ProcessFailedException } from '../common/exceptions/custom-exceptions';

@Injectable()
export abstract class BaseRepository<T> implements BaseRepositoryInterface<T> {
  protected readonly logger = new Logger(this.constructor.name);

  constructor(
    @Inject('DYNAMODB_CLIENT') protected readonly dynamoClient: DynamoDBDocumentClient,
    @Inject('DYNAMODB_TABLE_NAME') protected readonly tableName: string,
  ) {}

  async save(item: T): Promise<void> {
    try {
      const command = new PutCommand({
        TableName: this.tableName,
        Item: item,
      });
      
      await this.dynamoClient.send(command);
      this.logger.log(`Item saved successfully`);
    } catch (error) {
      this.logger.error(`Failed to save item: ${error.message}`, error.stack);
      throw new ProcessFailedException(`Failed to save item: ${error.message}`);
    }
  }

  async update(item: T): Promise<void> {
    try {
      const command = new PutCommand({
        TableName: this.tableName,
        Item: {
          ...item,
          updatedAt: new Date().toISOString(),
        },
      });
      
      await this.dynamoClient.send(command);
      this.logger.log(`Item updated successfully`);
    } catch (error) {
      this.logger.error(`Failed to update item: ${error.message}`, error.stack);
      throw new ProcessFailedException(`Failed to update item: ${error.message}`);
    }
  }

  async findByKey(pk: string, sk: string): Promise<T | null> {
    try {
      const command = new GetCommand({
        TableName: this.tableName,
        Key: { pk, sk },
      });
      
      const result = await this.dynamoClient.send(command);
      return result.Item as T || null;
    } catch (error) {
      this.logger.error(`Failed to find item by key: ${error.message}`, error.stack);
      throw new ProcessFailedException(`Failed to find item: ${error.message}`);
    }
  }

  async findByPartitionKey(pk: string): Promise<T[]> {
    try {
      const params: QueryCommandInput = {
        TableName: this.tableName,
        KeyConditionExpression: 'pk = :pk',
        ExpressionAttributeValues: {
          ':pk': pk,
        },
      };

      const command = new QueryCommand(params);
      const result = await this.dynamoClient.send(command);
      
      return result.Items as T[] || [];
    } catch (error) {
      this.logger.error(`Failed to find items by partition key: ${error.message}`, error.stack);
      throw new ProcessFailedException(`Failed to find items: ${error.message}`);
    }
  }

  async delete(pk: string, sk: string): Promise<void> {
    try {
      const command = new DeleteCommand({
        TableName: this.tableName,
        Key: { pk, sk },
      });
      
      await this.dynamoClient.send(command);
      this.logger.log(`Item deleted successfully`);
    } catch (error) {
      this.logger.error(`Failed to delete item: ${error.message}`, error.stack);
      throw new ProcessFailedException(`Failed to delete item: ${error.message}`);
    }
  }

  async exists(pk: string, sk: string): Promise<boolean> {
    try {
      const item = await this.findByKey(pk, sk);
      return item !== null;
    } catch (error) {
      this.logger.error(`Failed to check if item exists: ${error.message}`, error.stack);
      return false;
    }
  }

  protected async queryWithCondition(params: QueryCommandInput): Promise<T[]> {
    try {
      const command = new QueryCommand(params);
      const result = await this.dynamoClient.send(command);
      return result.Items as T[] || [];
    } catch (error) {
      this.logger.error(`Failed to query with condition: ${error.message}`, error.stack);
      throw new ProcessFailedException(`Failed to query items: ${error.message}`);
    }
  }
}
