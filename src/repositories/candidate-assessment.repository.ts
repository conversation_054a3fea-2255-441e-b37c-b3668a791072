import { Injectable } from '@nestjs/common';
import { CandidateAssessment } from '../entities';
import { BaseRepository } from './base.repository';
import { CandidateAssessmentRepositoryInterface } from './interfaces/candidate-assessment-repository.interface';
import { KeyBuilderUtil } from '../common/utils/key-builder.util';
import { QueryCommandInput } from '@aws-sdk/lib-dynamodb';

@Injectable()
export class CandidateAssessmentRepository 
  extends BaseRepository<CandidateAssessment> 
  implements CandidateAssessmentRepositoryInterface {

  async findByAssessment(organizationId: string, assessmentId: string): Promise<CandidateAssessment[]> {
    const pk = KeyBuilderUtil.candidateAssessmentPK(organizationId, assessmentId);
    return this.findByPartitionKey(pk);
  }

  async findByCandidate(
    organizationId: string, 
    assessmentId: string, 
    candidateId: string
  ): Promise<CandidateAssessment | null> {
    const pk = KeyBuilderUtil.candidateAssessmentPK(organizationId, assessmentId);
    const sk = KeyBuilderUtil.candidateAssessmentSK(candidateId);
    
    return this.findByKey(pk, sk);
  }

  async findByCandidateEmail(
    organizationId: string, 
    assessmentId: string, 
    candidateEmail: string
  ): Promise<CandidateAssessment | null> {
    const pk = KeyBuilderUtil.candidateAssessmentPK(organizationId, assessmentId);
    
    const params: QueryCommandInput = {
      TableName: this.tableName,
      KeyConditionExpression: 'pk = :pk',
      FilterExpression: 'candidateEmail = :email',
      ExpressionAttributeValues: {
        ':pk': pk,
        ':email': candidateEmail,
      },
    };

    const results = await this.queryWithCondition(params);
    return results.length > 0 ? results[0] : null;
  }

  async saveCandidateAssessment(candidateAssessment: CandidateAssessment): Promise<void> {
    const pk = KeyBuilderUtil.candidateAssessmentPK(
      candidateAssessment.organizationId, 
      candidateAssessment.assessmentId
    );
    const sk = KeyBuilderUtil.candidateAssessmentSK(candidateAssessment.candidateId);
    
    candidateAssessment.pk = pk;
    candidateAssessment.sk = sk;
    candidateAssessment.updateTimestamp();
    
    await this.save(candidateAssessment);
  }

  async updateCandidateAssessment(candidateAssessment: CandidateAssessment): Promise<void> {
    const pk = KeyBuilderUtil.candidateAssessmentPK(
      candidateAssessment.organizationId, 
      candidateAssessment.assessmentId
    );
    const sk = KeyBuilderUtil.candidateAssessmentSK(candidateAssessment.candidateId);
    
    candidateAssessment.pk = pk;
    candidateAssessment.sk = sk;
    candidateAssessment.updateTimestamp();
    
    await this.update(candidateAssessment);
  }
}
