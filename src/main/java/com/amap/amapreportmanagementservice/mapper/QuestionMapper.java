package com.amap.amapreportmanagementservice.mapper;



import com.amap.amapreportmanagementservice.dto.progress.*;
import com.amap.amapreportmanagementservice.dto.results.CodeResultDTO;
import com.amap.amapreportmanagementservice.dto.results.CodeTemplateDTO;
import com.amap.amapreportmanagementservice.dto.results.QuestionResultInputDTO;
import com.amap.amapreportmanagementservice.dto.results.ReferenceSolutionDTO;
import com.amap.amapreportmanagementservice.entity.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Utility class to map Question DTOs to domain entities
 */
public class QuestionMapper {

    public static void mapToCandidateQuestionResult(QuestionResultInputDTO questionDTO, CandidateQuestionResult candidateQuestionResult) {
        if (questionDTO == null || candidateQuestionResult == null) {
            return;
        }

        // Set isAnswered based on whether testTakerAnswers exists and is not empty
        List<String> testTakerAnswers = questionDTO.getTestTakerAnswers();
        boolean isAnswered = testTakerAnswers != null && !testTakerAnswers.isEmpty();
        candidateQuestionResult.setAnswered(isAnswered);

        String answerStatus = determineAnswerStatus(questionDTO, isAnswered);
        candidateQuestionResult.setIsAnswerCorrect(answerStatus);

        // Set isComprehension from the string value, defaulting to false if null
        if (questionDTO.getIsComprehension() != null) {
            candidateQuestionResult.setComprehension(
                    "true".equalsIgnoreCase(questionDTO.getIsComprehension())
            );
        }

        mapEssayAnswer(questionDTO, candidateQuestionResult);
        mapMultipleChoiceAnswer(questionDTO, candidateQuestionResult);
        mapMultipleSelectAnswer(questionDTO, candidateQuestionResult);
        mapTrueOrFalseAnswer(questionDTO, candidateQuestionResult);
        mapFillInAnswer(questionDTO, candidateQuestionResult);
        mapMatchMatrixAnswer(questionDTO, candidateQuestionResult);
        mapToCodeTemplate(questionDTO, candidateQuestionResult);
        mapCodeExecutionSummary(questionDTO, candidateQuestionResult);
        mapCodeResult(questionDTO, candidateQuestionResult);
        mapCodeConstraint(questionDTO, candidateQuestionResult);
        mapToReferenceSolution(questionDTO, candidateQuestionResult);
    }


    private static String determineAnswerStatus(QuestionResultInputDTO questionDTO, boolean isAnswered) {
        // If isAnswerCorrect is already set and not empty, use that value
        if (questionDTO.getIsAnswerCorrect() != null &&
                !questionDTO.getIsAnswerCorrect().trim().isEmpty()) {
            return questionDTO.getIsAnswerCorrect();
        }

        // Check if test taker provided any answer at all
        List<String> testTakerAnswers = questionDTO.getTestTakerAnswers();
        if (testTakerAnswers == null || testTakerAnswers.isEmpty()) {
            return "SKIPPED";
        }

        // Check if all answers in the list are null or empty strings
        boolean hasAnyAnswer = testTakerAnswers.stream()
                .anyMatch(answer -> answer != null && !answer.trim().isEmpty());

        if (!hasAnyAnswer) {
            return "SKIPPED";
        }


        // For other question types, compare with correct answers
        List<String> correctAnswers = getCorrectAnswersFromDTO(questionDTO);


        boolean answersMatch = compareAnswers(testTakerAnswers, correctAnswers);
        return answersMatch ? "CORRECT" : "WRONG";
    }

    /**
     * Extracts correct answers from the DTO based on question type
     * @param questionDTO the question DTO
     * @return list of correct answers or null if not available
     */
    private static List<String> getCorrectAnswersFromDTO(QuestionResultInputDTO questionDTO) {
        String questionType = questionDTO.getQuestionType();

        if ("Multiple_choice".equals(questionType) && questionDTO.getMultipleChoiceAnswer() != null) {
            return questionDTO.getMultipleChoiceAnswer().getAnswer();
        }

        if ("Multi_select".equals(questionType) && questionDTO.getMultipleSelectAnswer() != null) {
            return questionDTO.getMultipleSelectAnswer().getAnswer();
        }

        if ("True_or_false".equals(questionType) && questionDTO.getTrueOrFalseAnswer() != null) {
            return questionDTO.getTrueOrFalseAnswer().getAnswer();
        }

        if ("Fill_in".equals(questionType) && questionDTO.getFillInAnswer() != null) {
            return questionDTO.getFillInAnswer().getAnswer();
        }

        if ("Matrix".equals(questionType) && questionDTO.getMatchMatrixAnswer() != null) {
            return questionDTO.getMatchMatrixAnswer().getQuestions();
        }

        return null;
    }


    private static boolean compareAnswers(List<String> testTakerAnswers, List<String> correctAnswers) {
        if (testTakerAnswers == null || correctAnswers == null) {
            return false;
        }

        // Normalize answers for comparison (case-insensitive, trimmed)
        List<String> normalizedTestTakerAnswers = testTakerAnswers.stream()
                .map(answer -> answer != null ? answer.trim().toLowerCase() : "")
                .toList();

        List<String> normalizedCorrectAnswers = correctAnswers.stream()
                .map(answer -> answer != null ? answer.trim().toLowerCase() : "")
                .toList();

        // For multi-select questions, check if all answers match (order doesn't matter)
        return normalizedCorrectAnswers.stream().allMatch(normalizedTestTakerAnswers::contains) &&
                normalizedTestTakerAnswers.stream().allMatch(normalizedCorrectAnswers::contains);
    }

    /**
     * Maps essay answer from DTO to entity
     */
    private static void mapEssayAnswer(QuestionResultInputDTO questionDTO, CandidateQuestionResult candidateQuestionResult) {
        if(Objects.equals(questionDTO.getQuestionType(), "Essay")) {
            if (questionDTO.getEssayAnswer() != null) {
                candidateQuestionResult.setEssayRubrics(questionDTO.getEssayAnswer().getRubrics());
            }
        }

    }

    /**
     * Maps multiple choice answer from DTO to entity
     */
    private static void mapMultipleChoiceAnswer(QuestionResultInputDTO questionDTO, CandidateQuestionResult candidateQuestionResult) {
        if(Objects.equals(questionDTO.getQuestionType(), "Multiple_choice")){
            if (questionDTO.getMultipleChoiceAnswer() != null) {
                candidateQuestionResult.setCorrectAnswers(questionDTO.getMultipleChoiceAnswer().getAnswer());
                candidateQuestionResult.setOptionAnswers(questionDTO.getMultipleChoiceAnswer().getOptions());
            }
        }

    }

    /**
     * Maps multiple select answer from DTO to entity
     */
    private static void mapMultipleSelectAnswer(QuestionResultInputDTO questionDTO, CandidateQuestionResult candidateQuestionResult) {
        if(Objects.equals(questionDTO.getQuestionType(),"Multi_select")){
            if (questionDTO.getMultipleSelectAnswer() != null && questionDTO.getMultipleSelectAnswer().getAnswer() != null) {
                candidateQuestionResult.setCorrectAnswers(questionDTO.getMultipleSelectAnswer().getAnswer());
                candidateQuestionResult.setOptionAnswers(questionDTO.getMultipleSelectAnswer().getOptions());
            }
        }

    }

    /**
     * Maps true/false answer from DTO to entity
     */
    private static void mapTrueOrFalseAnswer(QuestionResultInputDTO questionDTO, CandidateQuestionResult candidateQuestionResult) {
        if (questionDTO.getTrueOrFalseAnswer() != null) {
            candidateQuestionResult.setCorrectAnswers(questionDTO.getTrueOrFalseAnswer().getAnswer());
            candidateQuestionResult.setOptionAnswers(questionDTO.getTrueOrFalseAnswer().getOptions());
        }
    }

    /**
     * Maps fill-in answer from DTO to entity
     */
    private static void mapFillInAnswer(QuestionResultInputDTO questionDTO, CandidateQuestionResult candidateQuestionResult) {
        if(Objects.equals(questionDTO.getQuestionType(), "Fill_in")){
            if (questionDTO.getFillInAnswer() != null && questionDTO.getFillInAnswer().getAnswer() != null) {
                candidateQuestionResult.setCorrectAnswers(questionDTO.getFillInAnswer().getAnswer());
                candidateQuestionResult.setOptionAnswers(questionDTO.getFillInAnswer().getOptions());
            }
        }

    }

    /**
     * Maps match matrix answer from DTO to entity
     */
    private static void mapMatchMatrixAnswer(QuestionResultInputDTO questionDTO, CandidateQuestionResult candidateQuestionResult) {
        if(Objects.equals(questionDTO.getQuestionType(), "Matrix")){
            if (questionDTO.getMatchMatrixAnswer() != null  ) {
                candidateQuestionResult.setCorrectAnswers(questionDTO.getMatchMatrixAnswer().getQuestions());
                candidateQuestionResult.setOptionAnswers(questionDTO.getMatchMatrixAnswer().getOptions());
            }
        }

    }




    private static void mapToCodeTemplate(QuestionResultInputDTO questionDTO, CandidateQuestionResult candidateQuestionResult) {
        if(Objects.equals(questionDTO.getQuestionType(), "Code")){
            if(questionDTO.getCodeTemplates() != null && !questionDTO.getCodeTemplates().isEmpty()){
            List<CodeTemplate> codeTemplates = questionDTO.getCodeTemplates().stream()
                    .filter(Objects::nonNull)
                    .map(QuestionMapper::mapCodeTemplate)
                    .collect(Collectors.toList());
            candidateQuestionResult.setCodeTemplates(codeTemplates);
            }
        }

    }

    private static CodeTemplate mapCodeTemplate(CodeTemplateDTO codeTemplateDTO) {
        if(codeTemplateDTO == null){
            return null;
        }
        CodeTemplate codeTemplate = new CodeTemplate();
        codeTemplate.setId(codeTemplateDTO.getId());
        codeTemplate.setQuestionId(codeTemplateDTO.getQuestionId());
        codeTemplate.setCodeType(codeTemplateDTO.getCodeType());
        codeTemplate.setBody(codeTemplateDTO.getBody());

        LanguageDTO languageDTO = codeTemplateDTO.getLanguage();
        if (languageDTO != null) {
            Language language = Language.builder()
                    .id(languageDTO.getId())
                    .name(languageDTO.getName())
                    .judgeLanguageId(languageDTO.getJudgeLanguageId())
                    .build();
            codeTemplate.setLanguage(language);
            codeTemplate.setLanguageId(languageDTO.getId());
        } else {
            if (codeTemplateDTO.getLanguageId() != null) {
                codeTemplate.setLanguageId(codeTemplateDTO.getLanguageId());
            }
        }

        return codeTemplate;
    }

    private static void mapCodeResult(QuestionResultInputDTO questionDTO, CandidateQuestionResult candidateQuestionResult) {
        if (Objects.equals(questionDTO.getQuestionType(), "Code")) {
            if (questionDTO.getCodeResults() != null && !questionDTO.getCodeResults().isEmpty()) {
                List<CodeResult> codeResults = questionDTO.getCodeResults().stream()
                        .map(QuestionMapper::mapToCodeResult)
                        .collect(Collectors.toList());
                candidateQuestionResult.setCodeResults(codeResults);
            }
        }
    }

    private static CodeResult mapToCodeResult(CodeResultDTO codeResultDTO) {
        if (codeResultDTO == null) {
            return null;
        }

        CodeResult codeResult = new CodeResult();

        codeResult.setTest_case_id(codeResultDTO.getTest_case_id());
        codeResult.setError(codeResultDTO.getError());
        codeResult.setCorrect(codeResultDTO.isCorrect());
        codeResult.setMemory(codeResultDTO.getMemory());
        codeResult.setInQueue(codeResultDTO.isInQueue());
        codeResult.setActualOutput(codeResultDTO.getActualOutput());
        codeResult.setExecutionTime(codeResultDTO.getExecutionTime());
        codeResult.setStatusDescription(codeResultDTO.getStatusDescription());
        codeResult.setStatusId(codeResultDTO.getStatusId());
        return codeResult;
    }

    private static void mapCodeExecutionSummary(QuestionResultInputDTO questionDTO, CandidateQuestionResult candidateQuestionResult) {
        if (Objects.equals(questionDTO.getQuestionType(), "Code")) {
            if (questionDTO.getCodeExecutionSummary() != null) {
                CodeExecutionSummaryDTO summary = questionDTO.getCodeExecutionSummary();

                Performance performance = null;
                if (summary.getPerformance() != null) {
                    PerformanceDTO performanceDTO = summary.getPerformance();
                    performance = Performance.builder()
                            .maxMemory(performanceDTO.getMaxMemory())
                            .totalMemory(performanceDTO.getTotalMemory())
                            .averageMemory(performanceDTO.getAverageMemory())
                            .maxExecutionTime(performanceDTO.getMaxExecutionTime())
                            .totalExecutionTime(performanceDTO.getTotalExecutionTime())
                            .averageExecutionTime(performanceDTO.getAverageExecutionTime())
                            .build();
                }

                // Create a new CodeExecutionSummary for the candidate result
                CodeExecutionSummary candidateSummary = CodeExecutionSummary.builder()
                        .allPassed(summary.isAllPassed())
                        .totalTests(summary.getTotalTests())
                        .failedTests(summary.getFailedTests())
                        .passedTests(summary.getPassedTests())
                        .performance(performance)
                        .inQueueTests(summary.getInQueueTests())
                        .stillProcessing(summary.isStillProcessing())
                        .build();

                candidateQuestionResult.setCodeExecutionSummary(candidateSummary);
            }
        }

    }

    private static void mapCodeConstraint(QuestionResultInputDTO questionDTO, CandidateQuestionResult candidateQuestionResult) {
        if (Objects.equals(questionDTO.getQuestionType(), "Code")) {
            if (questionDTO.getCodeConstraint() != null) {
                CodeConstraintsDTO constraint = questionDTO.getCodeConstraint();

                CodeConstraint codeConstraint = CodeConstraint.builder()
                        .questionId(constraint.getQuestionId())
                        .memoryLimit(constraint.getMemoryLimit())
                        .id(constraint.getId())
                        .spaceComplexity(constraint.getSpaceComplexity())
                        .timeComplexity(constraint.getTimeComplexity())
                        .timeLimit(constraint.getTimeLimit())
                        .build();

                candidateQuestionResult.setCodeConstraint(codeConstraint);
            }
        }

    }

    private static void mapToReferenceSolution(QuestionResultInputDTO questionDTO, CandidateQuestionResult candidateQuestionResult) {
        if(Objects.equals(questionDTO.getQuestionType(), "Code")) {
            if(questionDTO.getReferenceSolution() != null && !questionDTO.getReferenceSolution().isEmpty()) {
                List<ReferenceSolution> referenceSolutions = questionDTO.getReferenceSolution()
                        .stream().filter(Objects::nonNull).map(QuestionMapper::mapReferenceSolution)
                        .collect(Collectors.toList());
                candidateQuestionResult.setReferenceSolution(referenceSolutions);
            }
        }
    }

    private static ReferenceSolution mapReferenceSolution(ReferenceSolutionDTO referenceSolutionDTO) {
        if (referenceSolutionDTO == null) {
            return null;
        }
        ReferenceSolution referenceSolution = new ReferenceSolution();
        referenceSolution.setId(referenceSolutionDTO.getId());
        referenceSolution.setCodeType(referenceSolutionDTO.getCodeType());
        referenceSolution.setBody(referenceSolutionDTO.getBody());
        referenceSolution.setQuestionId(referenceSolutionDTO.getQuestionId());
        LanguageDTO languageDTO = referenceSolutionDTO.getLanguage();
        if (languageDTO != null) {
            Language language = Language.builder()
                    .id(languageDTO.getId())
                    .name(languageDTO.getName())
                    .judgeLanguageId(languageDTO.getJudgeLanguageId())
                    .build();
            referenceSolution.setLanguage(language);
            referenceSolution.setLanguageId(languageDTO.getId());
        } else {
            if (referenceSolutionDTO.getLanguageId() != null) {
                referenceSolution.setLanguageId(referenceSolutionDTO.getLanguageId());
            }
        }
        return referenceSolution;
    }
}
