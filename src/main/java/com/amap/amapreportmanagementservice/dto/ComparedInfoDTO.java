package com.amap.amapreportmanagementservice.dto;

import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.ComparativeAnalysisDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ComparedInfoDTO {
    private String candidateEmail;
    private String candidateId;
    private List<ComparativeAnalysisDTO> comparativeAnalysis;

}
