package com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221;

import com.amap.amapreportmanagementservice.dto.AssessmentScoreMetricsDTO;
import com.amap.amapreportmanagementservice.dto.AverageTimesDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GeneralDetailsDTO {
   private AverageTimesDTO durations;
   private CompletionRateDTO completionRate;
   private AssessmentScoreMetricsDTO averageScoreMetrics;
   private List<Double> percentageScoreDistribution;
   
}
