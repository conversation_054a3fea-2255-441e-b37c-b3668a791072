package com.amap.amapreportmanagementservice.dto.results;

import lombok.*;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FlaggedInputDTO {
    @NonNull
    private String id;
    @NonNull
    private String assessmentId;
    @NonNull
    private String organizationId;
    @NonNull
    private String testId;
    @NonNull
    private String questionId;
    private String questionText;
    private String testTakerId;
    private String testTakerEmail;
    private List<String> reasonForFlagging;

}


