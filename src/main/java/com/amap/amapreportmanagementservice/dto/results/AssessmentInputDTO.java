package com.amap.amapreportmanagementservice.dto.results;

import lombok.*;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssessmentInputDTO {
    @NonNull
    private String organizationId;
    @NonNull
    private String testTakerId;
    private String email;
    private String assessmentId;
    private String startTime;
    private String proctorLevel;
    private String proctor;
    private String assessmentEndTime;
    private int assessmentDuration;
    private int testTakerDurationSeconds;
    private int assessmentWindowViolationCount ;
    private int assessmentWindowViolationDuration ;
    private int assessmentTakerShotCount ;
    private int assessmentTakerViolationShotCount ;
    private int windowShotCount ;
    private int windowViolationShotCount ;
    private int testCount;
    private List<String> testList;
    private List<String> submittedTests;
    private String totalFiveToStart;
    private List<TestResultInputDTO> testResults;
    private List<String> windowViolation;
    private List<String> intervalScreenshots;
    private IdentityInputDTO identity;
    private String status;
    private String reportCallbackURL;

}
