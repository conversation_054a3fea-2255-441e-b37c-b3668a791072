package com.amap.amapreportmanagementservice.dto.results;

import lombok.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CodeResultDTO {
    private String error;
    private int memory;
    private boolean inQueue;
    private int statusId;
    private boolean isCorrect;
    private String actualOutput;
    private String test_case_id;
    private String executionTime;
    private String statusDescription;
}
