package com.amap.amapreportmanagementservice.dto.results;

import com.amap.amapreportmanagementservice.dto.progress.LanguageDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CodeTemplateDTO {
    private String id;
    private String codeType;
    private String body;
    private String questionId;
    private String languageId;
    @JsonProperty("Language")
    private LanguageDTO language;
}