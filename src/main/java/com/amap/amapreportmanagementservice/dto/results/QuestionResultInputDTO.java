package com.amap.amapreportmanagementservice.dto.results;

import com.amap.amapreportmanagementservice.dto.progress.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QuestionResultInputDTO {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    @NonNull
    private String questionId;
    private String questionText;
    private String questionType;
    private String isAnswerCorrect;
    private String isComprehension;
    private String difficultyLevel;
    @JsonProperty("testTakerAnswers")
    private Object rawTestTakerAnswersData;
    private MultipleAnswerDTO multipleSelectAnswer;
    private MultipleChoiceAnswerDTO multipleChoiceAnswer;
    private TrueOrFalseAnswerDTO trueOrFalseAnswer;
    private FillInAnswerDTO fillInAnswer;
    private MatchMatrixAnswerDTO matchMatrixAnswer;
    private EssayAnswerDTO essayAnswer;
    private TestTakerCodeAnswerDTO testTakerCode;
    private List<CodeTemplateDTO> codeTemplates;
    private List<CodeResultDTO> codeResults;
    private int idleTime;
    private double scored;
    private int score;
    private String codeReview;
    public CodeExecutionSummaryDTO codeExecutionSummary;
    @JsonProperty("CodeConstraint")
    public CodeConstraintsDTO codeConstraint;
    private List<ReferenceSolutionDTO> referenceSolution;

    @JsonIgnore
    public List<String> getTestTakerAnswers() {
        List<String> result = new ArrayList<>();

        if (rawTestTakerAnswersData == null) {
            return result;
        }

        try {
            // If it's already a list
            if (rawTestTakerAnswersData instanceof List<?> list) {

                for (Object item : list) {
                    if (item instanceof String) {
                        result.add((String) item);
                    } else {
                        result.add(objectMapper.writeValueAsString(item));
                    }
                }
            } else {
                result.add(objectMapper.writeValueAsString(rawTestTakerAnswersData));
            }
        } catch (Exception e) {
            result.add(String.valueOf(rawTestTakerAnswersData));
        }

        return result;
    }
}
