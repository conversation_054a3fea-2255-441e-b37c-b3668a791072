package com.amap.amapreportmanagementservice.dto.results;

import lombok.*;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TestResultInputDTO {
    @NonNull
    private String testId;
    private List<QuestionResultInputDTO> questionResults;
    private int numberOfQuestions;
    private int numberOfQuestionsFailed;
    private int numberOfQuestionsAnswered;
    private int numberOfQuestionsPassed;
    private int totalScore;
    private double totalPassedScore;
    private float testPercentage;
    private int testWindowViolationDuration;
    private int testWindowViolationCount ;
    private int testTakerShotCount ;
    private int testTakerViolationShotCount ;
    private int testWindowShotCount ;
    private int testWindowViolationShotCount ;
    private String StartTime;
    private String finnishTime;
    private String   status;
    private String passStatus;
}
