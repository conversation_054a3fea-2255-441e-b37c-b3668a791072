package com.amap.amapreportmanagementservice.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TestDetailDTO {
    private String testTitle;
    private Integer numberOfQuestions;
    private Double averageScore;
    private Integer numberOfFlaggedQuestions;
    private List<FlaggedQuestionDTO> flaggedQuestions;
    private Boolean easiest;
    private Boolean mostChallenging;

    public TestDetailDTO(String testA) {
    }
}