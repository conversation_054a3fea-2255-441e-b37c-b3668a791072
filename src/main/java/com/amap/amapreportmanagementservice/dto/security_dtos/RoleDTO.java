package com.amap.amapreportmanagementservice.dto.security_dtos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RoleDTO {
    private int id;
    private String roleName;
    private String description;
    private List<String> permissions;
    private String organizationId;
    private boolean isSystem;
    private LocalDateTime updatedAt;
    private LocalDateTime createdAt;
    private int numberOfUsers;
}
