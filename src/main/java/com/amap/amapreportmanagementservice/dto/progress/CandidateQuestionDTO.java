package com.amap.amapreportmanagementservice.dto.progress;

import com.amap.amapreportmanagementservice.dto.results.CodeResultDTO;
import lombok.*;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CandidateQuestionDTO {
    @NonNull
    private String id;
    private String questionText;
    private String questionType;
    private String domain;
    private String category;
    private double score;
    private int timeLimit;
    private String difficultyLevel;
    private String isComprehension;
    private String isAnswerCorrect;
    private String domainId;
    private String categoryId;
    private MultipleAnswerDTO multipleSelectAnswer;
    private MultipleChoiceAnswerDTO multipleChoiceAnswer;
    private TrueOrFalseAnswerDTO trueOrFalseAnswer;
    private FillInAnswerDTO fillInAnswer;
    private MatchMatrixAnswerDTO matchMatrixAnswer;
    private CodeResultDTO codeResult;
    private List<String> testTakerAnswers;
}
