package com.amap.amapreportmanagementservice.dto.progress;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TestCaseResultDTO {
    private String testCaseId;
    private String input;
    private String expectedOutput;
    private String actualOutput;
    private boolean isCorrect;
    private String status;
    private Double executionTime;
    private Integer memoryUsed;
    private String error;
}