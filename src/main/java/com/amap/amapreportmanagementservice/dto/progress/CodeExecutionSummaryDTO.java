package com.amap.amapreportmanagementservice.dto.progress;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CodeExecutionSummaryDTO {
    private boolean allPassed;
    private int totalTests;
    private int failedTests;
    private int passedTests;
    private int inQueueTests;
    private PerformanceDTO performance;
    private boolean stillProcessing;
}