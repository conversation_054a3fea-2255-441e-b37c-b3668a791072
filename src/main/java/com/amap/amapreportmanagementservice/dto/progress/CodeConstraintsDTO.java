package com.amap.amapreportmanagementservice.dto.progress;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DynamoDbBean

public class CodeConstraintsDTO {
    private String id;
    private int timeLimit;
    private int memoryLimit;
    private String timeComplexity;
    private String spaceComplexity;
    private String questionId;
}
