package com.amap.amapreportmanagementservice.dto.progress;

import lombok.*;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CandidateTestDTO {
    @NonNull
    private String id;
    private String title;
    private String domain;
    private boolean isActivated;
    private String difficultyLevel;
    private String passage;
    private String domainId;
    private String description;
    private String instructions;
    private int duration;
    private List<CandidateQuestionDTO> questions;

}
