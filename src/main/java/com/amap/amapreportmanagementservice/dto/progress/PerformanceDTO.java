package com.amap.amapreportmanagementservice.dto.progress;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PerformanceDTO {
    private int maxMemory;
    private int totalMemory;
    private int averageMemory;
    private double maxExecutionTime;
    private double totalExecutionTime;
    private double averageExecutionTime;
}
