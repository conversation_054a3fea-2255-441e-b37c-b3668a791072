package com.amap.amapreportmanagementservice.dto.progress;

import lombok.*;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CandidateAssessmentDTO {
    @NonNull
    private String id;
    private String title;
    private String instructions;
    private String system;
    private String proctor;
    private String proctorLevel;
    private int assessmentDuration;
    private String assessmentStartTime;
    private List<CandidateTestDTO> tests;

}
