package com.amap.amapreportmanagementservice.dto.progress;

import lombok.*;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssessmentProgressDTO {
    @NonNull
    private String id;
    @NonNull
    private String assessmentId;
    @NonNull
    private String organizationId;
    private String email;
    private String status;
    private String proctor;
    private int screenshotsInterval ;
    private int  camerashotsInterval ;
    private CandidateAssessmentDTO assessment;
    private String expireDate;
    private String commenceDate;
    private double passMark;
    private String reportCallbackURL;

}
