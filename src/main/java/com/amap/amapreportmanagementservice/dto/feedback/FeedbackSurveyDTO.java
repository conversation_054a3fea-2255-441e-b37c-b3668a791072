package com.amap.amapreportmanagementservice.dto.feedback;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FeedbackSurveyDTO {
    private String takerEmail;
    @NonNull
    private String testTakerId;
    @NonNull
    private String organizationId;
    @NonNull
    private String assessmentId;
    private List<FeedbackSurveyQuestionDTO> surveyQuestions;
}


