package com.amap.amapreportmanagementservice.dto;

import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.AssessmentsDetails;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateMetricsDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.ComparativeAnalysisCandidateInfo;
import com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221.GeneralDetailsDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AllDetailsDTO {
    private GeneralDetailsDTO generalDetails;
    private AssessmentsDetails assessmentsDetails;
    private CandidateMetricsDTO candidateMetrics;
    private List<FeedbackResponseDTO> feedbacks;
    private List<ComparativeAnalysisCandidateInfo> totalComparativeAnalysis;
}