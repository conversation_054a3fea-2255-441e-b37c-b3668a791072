package com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentResponseDTO {
    private String assessmentId;
    private int assessmentDuration;
    private String title;
    private double averageTimeTaken;
    private boolean system;
    private int numberOfTakers;
    private int numberOfUniqueTakers;
    private int totalScore;
    private List<String> testsIds;
    private int progressCount;
    private int completedCount;
    private double averagePercentage;
    private String instruction;
}
