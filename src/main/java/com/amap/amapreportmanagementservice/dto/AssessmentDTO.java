package com.amap.amapreportmanagementservice.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssessmentDTO {
    private String assessmentId;
    private String title;
    private String instructions;
    private double averageScore;
    private String createdAt = String.valueOf(LocalDateTime.of(2019, 12, 30, 15, 45, 40));
    private String updatedAt = String.valueOf(LocalDateTime.of(2019, 12, 30, 15, 45, 40));
    private int numberOfCandidates;
    private int numberOfSections;
    private int numberOfFeedbacks;
    private int numberOfCompleted;
    private int numberOfIncomplete;
}
