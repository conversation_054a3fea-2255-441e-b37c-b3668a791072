package com.amap.amapreportmanagementservice.dto;


import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateTrajectoryDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CandidateTrajectoryInfoDTO {
    private String candidateId;
    private String candidateEmail;
    private List<CandidateTrajectoryDTO> candidateTrajectory;
}

