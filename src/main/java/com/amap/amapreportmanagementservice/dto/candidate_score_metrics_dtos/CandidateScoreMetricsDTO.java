package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CandidateScoreMetricsDTO {
    private String candidateId;
    private String candidateEmail;
    private double candidateMarks;
    private int integrityScore;
    private double scorePercentage;
    private String riskLevel;
    private String proctoringLevel;
    private int assessmentWindowViolationCount ;
    private int assessmentWindowViolationDuration ;
    private int assessmentTakerShotCount ;
    private int assessmentTakerViolationShotCount ;
    private int windowShotCount ;
    private int windowViolationShotCount ;
    private int screenshotsInterval ;
    private int  camerashotsInterval ;
}
