package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ComparativeTestAnalysisDTO {
    private List<ComparativeAnalysisDTO> comparativeAnalysis;
    private List<CandidateTrajectoryDTO> candidateTrajectory;
    private CandidateFeedbackDTO candidateFeedback;
}
