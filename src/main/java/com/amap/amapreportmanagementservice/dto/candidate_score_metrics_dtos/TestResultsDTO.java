package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import com.amap.amapreportmanagementservice.entity.CandidateQuestionResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TestResultsDTO {
    private String name;
    private int questionsAnswered;
    private int totalNumQuestions;
    private long testTime;
    private double candidateScore;
    private double overallScore;
    private double percentage;
    private int numberOfQuestionsFailed ;
    private int numberOfQuestionsPassed ;
    private int numberOfQuestionsAnswered ;
    private int numberOfQuestions ;
    private int testWindowViolationDuration;
    private int testWindowViolationCount ;
    private int testTakerShotCount ;
    private int testTakerViolationShotCount ;
    private int testWindowShotCount ;
    private int testWindowViolationShotCount ;
    private String passage;
    private  String status;
    private  String passStatus;
    private String domain;
    private List<CandidateQuestionResult> questionResults;
}
