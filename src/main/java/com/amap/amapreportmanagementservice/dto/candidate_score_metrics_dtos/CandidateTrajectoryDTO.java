package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CandidateTrajectoryDTO {
    private String assessmentTitle;
    private double assessmentScore;
    private String assessmentStartTime;

    public CandidateTrajectoryDTO(String a) {
    }
}
