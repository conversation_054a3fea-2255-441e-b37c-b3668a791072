package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import com.amap.amapreportmanagementservice.dto.progress.CodeExecutionSummaryDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QuestionResultDTO {
    private String candidateId;
    private String questionId;
    private String candidateEmail;
    private String questionText;
    private String questionType;
    private int totalScore;
    private int candidateMarks;
    private String difficultyLevel;
    private int timeLimit;
    private String isFlagged;
    private List<String> TestTakerAnswers;
    private CodeExecutionSummaryDTO codeExecutionSummary;
    private boolean isComprehension;
    private String isAnswerCorrect;
}
