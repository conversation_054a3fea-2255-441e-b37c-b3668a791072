package com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos;

import com.amap.amapreportmanagementservice.dto.FeedbackResponseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssessmentResultsDTO {
    private String assessmentTitle;
    private String status;
    private String proctor;
    private String proctorLevel;
    private String commenceDate;
    private String expireDate;
    private String assessmentEndTime;
    private String assessmentStartTime;
    private long assessmentTime;
    private long assessmentTimeTaken;
    private double assessmentCandidateScore;
    private double assessmentOverallScore;
    private double assessmentCandidatePercentage;
    private int assessmentWindowViolationCount ;
    private int assessmentWindowViolationDuration ;
    private int assessmentTakerShotCount ;
    private int assessmentTakerViolationShotCount ;
    private int windowShotCount ;
    private int windowViolationShotCount ;
    private int screenshotsInterval ;
    private int integrityScore ;
    private int  camerashotsInterval ;
    private List<TestResultsDTO> testResults;
    private FeedbackResponseDTO feedback;
}
