/**
 * Utility class for building keys used in database operations. These keys are typically
 * composed of various parts, including prefixes and identifiers, to uniquely identify
 * entities in the database.
 */

package com.amap.amapreportmanagementservice.utils;

import com.amap.amapreportmanagementservice.constants.DbKeysPrefixes;

public class KeyBuilder {
    /**
     * Private constructor to prevent instantiation of this utility class.
     */
    private KeyBuilder() {
    }

     /**
     * Constructs the default key using organization and assessment identifiers.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     * @return The constructed key.
     */
    private static String defaultKey(String organizationId, String assessmentId) {
        return DbKeysPrefixes.ORGANIZATION_PREFIX + organizationId + DbKeysPrefixes.ASSESSMENT_PREFIX + assessmentId;
    }

    // Various methods for building specific keys...
    public static String assessmentPK(String organizationId, String assessmentId) {
        return defaultKey(organizationId, assessmentId);
    }

    public static String assessmentSK(String organizationId, String assessmentId) {
        return defaultKey(organizationId, assessmentId);
    }

    public static String candidateAssessmentPK(String organizationId, String assessmentId) {
        return defaultKey(organizationId, assessmentId);
    }

    public static String candidateAssessmentSK(String candidateEmail, String candidateId) {
        return DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX + candidateEmail + DbKeysPrefixes.CANDIDATE_ID_PREFIX + candidateId;
    }

    public static String assessmentTestPK(String organizationId, String assessmentId) {
        return defaultKey(organizationId, assessmentId);
    }

    public static String assessmentTestSK(String assessmentId, String testId) {
        return DbKeysPrefixes.ASSESSMENT_PREFIX + assessmentId + DbKeysPrefixes.TEST_PREFIX + testId;
    }

    public static String candidateTestPK(String organizationId, String assessmentId) {
        return defaultKey(organizationId, assessmentId);
    }

    public static String candidateTestSK(String testId, String candidateEmail, String candidateId) {
        return DbKeysPrefixes.TEST_PREFIX + testId + DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX + candidateEmail + DbKeysPrefixes.CANDIDATE_ID_PREFIX + candidateId;
    }

    public static String testQuestionPK(String organizationId, String assessmentId, String testId) {
        return defaultKey(organizationId, assessmentId) + DbKeysPrefixes.TEST_PREFIX + testId;
    }

    public static String testQuestionSK(String questionId) {
        return DbKeysPrefixes.QUESTION_PREFIX + questionId;
    }

    public static String candidateQuestionPK(String organizationId, String assessmentId, String testId) {
        return testQuestionPK(organizationId, assessmentId, testId);
    }

    public static String candidateQuestionSK(String questionId, String candidateEmail, String candidateId) {
        return DbKeysPrefixes.QUESTION_PREFIX + questionId + DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX + candidateEmail +
                DbKeysPrefixes.CANDIDATE_ID_PREFIX + candidateId;
    }

    public static String canEmailSk(String candidateEmail) {
        return DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX + candidateEmail;
    }

    public static String candidateFeedbackPK(String organizationId, String assessmentId) {
        return defaultKey(organizationId, assessmentId);

    }

    public static String candidateFeedbackSk(String candidateId) {
        return DbKeysPrefixes.CANDIDATE_ID_PREFIX + candidateId;

    }
    public static String questionFlaggingPK(String organizationId, String assessmentId) {
        return defaultKey(organizationId, assessmentId);

    }
    public static String questionFlaggingSK(String questionId) {
        return DbKeysPrefixes.QUESTION_PREFIX + questionId;
    }

    public static String webhookReportPK(String organizationId){return DbKeysPrefixes.ORGANIZATION_PREFIX+organizationId; }
    public static String webhookReportSK(String candidateId){return DbKeysPrefixes.WEBHOOK_PREFIX+candidateId; }
}
