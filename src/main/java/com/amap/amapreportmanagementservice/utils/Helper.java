/**
 * Utility class providing various helper methods for common calculations and operations.
 */
package com.amap.amapreportmanagementservice.utils;

import java.time.Duration;
import java.time.ZonedDateTime;

public class Helper {
    /**
     * Private constructor to prevent instantiation of this utility class.
     */
    private Helper() {
    }

    /**
     * Calculates the average of two double values.
     *
     * @param numerator   The numerator of the average calculation.
     * @param denominator The denominator of the average calculation.
     * @return The calculated average.
     */
    public static Double averageCalculator(double numerator, double denominator) {
        return numerator / denominator;
    }

    /**
     * Calculates the average as a percentage of two double values.
     *
     * @param numerator   The numerator of the percentage calculation.
     * @param denominator The denominator of the percentage calculation.
     * @return The calculated percentage as a double value.
     */
    public static Double averagePercentageCalculator(double numerator, double denominator) {
        return (numerator / denominator) * 100;
    }


    /**
     * Calculates the duration between two ZonedDateTime instances.
     *
     * @param startTime The start time for the duration calculation.
     * @param endTime   The end time for the duration calculation.
     * @return The calculated duration as a java.time.Duration object.
     */
    public static Duration durationCalculator(ZonedDateTime startTime, ZonedDateTime endTime) {
        return Duration.between(startTime, endTime);
    }


}
