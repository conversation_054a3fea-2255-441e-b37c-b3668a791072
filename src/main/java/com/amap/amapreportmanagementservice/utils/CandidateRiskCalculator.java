/**
 * Utility class for calculating and determining risk levels for candidates.
 * This class provides methods for calculating risk level scores based on the
 * number of window violations and determining the corresponding risk level.
 */

package com.amap.amapreportmanagementservice.utils;

public class CandidateRiskCalculator {

     /**
     * Private constructor to prevent instantiation of this utility class.
     */
    private CandidateRiskCalculator(){}

    /**
     * Calculates the risk level score based on the number of window violations.
     *
     * @param numberOfWindowViolations The number of window violations attributed to the candidate.
     * @return The calculated risk level score.
     */
    public static int calculateRiskLevelScore(int numberOfWindowViolations) {
        int defaultRiskScore = 100;
        int pointsPerViolation = 10;

        int adjustedRiskScore = defaultRiskScore - (numberOfWindowViolations * pointsPerViolation);
        return Math.max(adjustedRiskScore, 0); // Ensure risk score doesn't go negative
    }

    /**
     * Determines the risk level based on the calculated risk level score.
     *
     * @param riskLevelScore The calculated risk level score.
     * @return The determined risk level, which can be "Low Risk," "Medium Risk," or "High Risk."
     */
    public static String determineRiskLevel(int riskLevelScore) {
        if (riskLevelScore >= 80) {
            return "Low Risk";
        } else if (riskLevelScore >= 50) {
            return "Medium Risk";
        } else {
            return "High Risk";
        }
    }

}
