/**
 * This package contains utility classes for common calculations and operations, as well as
 * key-building methods for constructing database keys used in various database operations.
 *
 * <p>Utility Classes:</p>
 * <ul>
 *     <li>{@link com.amap.amapreportmanagementservice.utils.Helper} - Provides various helper methods
 *     for calculations, including average calculation and duration calculation.</li>
 *     <li>{@link com.amap.amapreportmanagementservice.utils.KeyBuilder} - Offers methods to construct
 *     keys used in database operations, including keys for assessments, candidates, tests, roles, and more.</li>
 *     <li>{@link com.amap.amapreportmanagementservice.utils.CandidateRiskCalculator} - Calculates risk levels
 *  *     for candidates based on the number of window violations.</li>
 * </ul>
 *
 * These utility classes are designed to simplify common tasks and provide key-building functionality
 * for interacting with the database.
 */
package com.amap.amapreportmanagementservice.utils;