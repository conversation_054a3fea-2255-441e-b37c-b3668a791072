package com.amap.amapreportmanagementservice.entity;

import com.amap.amapreportmanagementservice.dto.progress.LanguageDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
@Builder
public class CodeTemplate {
    private String id;
    private String codeType;
    private String body;
    private String questionId;
    private String languageId;
    @JsonProperty("Language")
    private Language language;
}
