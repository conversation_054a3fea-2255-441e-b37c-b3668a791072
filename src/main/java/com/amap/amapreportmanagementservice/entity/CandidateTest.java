package com.amap.amapreportmanagementservice.entity;

import lombok.*;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
@Builder
public class CandidateTest extends ReportBaseEntity {
    @NonNull
    private String candidateId;
    private String candidateEmail;
    @NonNull
    private String testId;
    private int totalScore;
    private String domain;
    private double candidateMarks;
    private double scorePercentage;
    private String passage;
    private long timeTaken;
    private int numberOfQuestionsFailed;
    private int numberOfQuestionsPassed;
    private int numberOfQuestionsAnswered;
    private int numberOfQuestions;
    private int testWindowViolationDuration;
    private int testWindowViolationCount;
    private int testTakerShotCount;
    private int testTakerViolationShotCount;
    private int testWindowShotCount;
    private int testWindowViolationShotCount;
    private String status;
    private String passStatus;
    private List<String> questionIDs;


}
