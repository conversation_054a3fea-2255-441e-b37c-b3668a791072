package com.amap.amapreportmanagementservice.entity;

import lombok.*;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

@DynamoDbBean
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WebhookReportJob extends ReportBaseEntity{

    @NonNull
    private String candidateId;
    @NonNull
    private String assessmentId;
    @NonNull
    private String email;
    @NonNull
    private String reportCallbackURL;
    int trails;
}
