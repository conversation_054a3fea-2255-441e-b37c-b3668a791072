package com.amap.amapreportmanagementservice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DynamoDbBean
public class ReferenceSolution {
    public String id;
    public String codeType;
    public String body;
    public String questionId;
    public String languageId;
    @JsonProperty("Language")
    public Language language;
}
