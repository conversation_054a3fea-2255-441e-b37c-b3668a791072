package com.amap.amapreportmanagementservice.entity;

import lombok.*;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
@Builder
public class CandidateFeedback extends ReportBaseEntity{
    private String candidateEmail;
    @NonNull
    private String candidateId;
    @NonNull
    private String assessmentId;
    private List<Feedback> feedbacks;
    private String date;
}
