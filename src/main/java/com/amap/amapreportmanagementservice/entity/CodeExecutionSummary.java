package com.amap.amapreportmanagementservice.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
@Builder
public class CodeExecutionSummary {
    private boolean allPassed;
    private int totalTests;
    private int failedTests;
    private int passedTests;
    private Performance performance;
    private int inQueueTests;
    private boolean stillProcessing;
}
