package com.amap.amapreportmanagementservice.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
@Builder
public class CodeConstraint {
    private String id;
    private int timeLimit;
    private int memoryLimit;
    private String timeComplexity;
    private String spaceComplexity;
    private String questionId;
}
