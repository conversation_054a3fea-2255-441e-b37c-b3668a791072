package com.amap.amapreportmanagementservice.entity;


import lombok.*;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
public class AssessmentTest extends ReportBaseEntity{
    @NonNull
    private String assessmentId;
    private String title;
    @NonNull
    private String testId;
    private String domain;
    private int totalScore;
    private double averageScore;
    private long duration;
    private String passage;
    private double totalCandidateMarks;
    private double totalCandidatePercentage;
    private double averagePercentageScore;
    private int numberOfTimesAnswered;
    private int numberOfFlags;
    private List<String> questionIds;
}
