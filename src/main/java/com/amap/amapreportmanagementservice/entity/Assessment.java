package com.amap.amapreportmanagementservice.entity;

import lombok.*;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
@Builder
public class Assessment extends ReportBaseEntity{
    @NonNull
    private String assessmentId;
    private int assessmentDuration;
    private String title;
    private double averageTimeTaken;
    private boolean isSystem;
    private int numberOfTakers;
    private int numberOfUniqueTakers;
    private int totalScore;
    private List<String> testsIds;
    private int progressCount;
    private int completedCount;
    private double averagePercentage;
    private double passMark;
    private String instruction;
    private String expireDate;
    private String commenceDate;
    private Integer numberOfFeedbacks;
    private int numberOfCompletedStatus;
    private int numberOfIncompleteStatus;



}
