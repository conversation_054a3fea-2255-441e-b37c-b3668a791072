package com.amap.amapreportmanagementservice.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
@Builder
public class CodeResult {
    private String error;
    private int memory;
    private boolean inQueue;
    private int statusId;
    private boolean isCorrect;
    private String actualOutput;
    private String test_case_id;
    private String executionTime;
    private String statusDescription;
}
