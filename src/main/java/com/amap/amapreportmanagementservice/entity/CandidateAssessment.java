package com.amap.amapreportmanagementservice.entity;

import lombok.*;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;


@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
public class CandidateAssessment extends ReportBaseEntity {
    @NonNull
    private String candidateId;
    private String title;
    private String candidateEmail;
    @NonNull
    private String assessmentId;
    private int timeTaken;
    private String status;
    private float totalScore;
    private double candidateMarks;
    private String assessmentStartTime;
    private String assessmentEndTime;
    private double scorePercentage;
    private  String proctor;
    private String proctorLevel;
    private int integrityScore;
    private int duration;
     private int assessmentWindowViolationCount ;
     private int assessmentWindowViolationDuration ;
     private int assessmentTakerShotCount ;
     private int assessmentTakerViolationShotCount ;
     private int windowShotCount ;
     private int windowViolationShotCount ;
     private int assessmentDuration ;
     private int screenshotsInterval ;
     private int  camerashotsInterval ;
     private String reportCallbackURL;

}
