package com.amap.amapreportmanagementservice.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DynamoDbBean
public class Performance {
    private int maxMemory;
    private int totalMemory;
    private double averageMemory;
    private double maxExecutionTime;
    private double totalExecutionTime;
    private double averageExecutionTime;
}
