package com.amap.amapreportmanagementservice.entity;


import com.amap.amapreportmanagementservice.dto.progress.CodeConstraintsDTO;
import com.amap.amapreportmanagementservice.dto.progress.CodeExecutionSummaryDTO;
import com.amap.amapreportmanagementservice.dto.results.CodeResultDTO;
import com.amap.amapreportmanagementservice.dto.results.CodeTemplateDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
@Builder
//@JsonIgnoreProperties(ignoreUnknown = true)
public class CandidateQuestionResult extends ReportBaseEntity {
   @NonNull
   private String candidateId;
   private String questionId;
   private String candidateEmail;
   private String questionText;
   private String domain;
   private String category;
   private String questionType;
   private int totalScore;
   private double candidateMarks;
   private String difficultyLevel;
   private int timeLimit;
   private String isFlagged;
   private List<String> testTakerAnswers;
   private List<String> optionAnswers;
   private List<String> correctAnswers ;
   private boolean isComprehension;
   private boolean isAnswered;
   private String isAnswerCorrect;
   private String essayRubrics;
   private String codeReview;


   private List<CodeTemplate> codeTemplates;

   private List<CodeResult> codeResults;

   private CodeExecutionSummary codeExecutionSummary;

   private CodeConstraint codeConstraint;

   private List<ReferenceSolution> referenceSolution;

}