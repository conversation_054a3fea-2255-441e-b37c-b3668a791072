package com.amap.amapreportmanagementservice.entity;

import jakarta.annotation.Nullable;
import lombok.*;
import org.apache.kafka.common.protocol.types.Field;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
@RequiredArgsConstructor
@Builder
public class TestQuestion extends ReportBaseEntity {
    @NonNull
    private String assessmentId;
    @NonNull
    private String testId;
    @NonNull
    private String questionId;
    private String questionText;
    private String domain;
    private String category;
    private int numberOfTimesMissed;
    private int numberOfTimesAnswered;
    private String title;
    private int duration;
    private String difficultyLevel;
    private String domainId;
    private String categoryId;
    private int numberOfFlags;
    private List<String> questionIds;
    private double totalScore;
    private String codeTemplate;
    private String codeConstraint;
}
