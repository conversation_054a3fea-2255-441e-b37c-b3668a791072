package com.amap.amapreportmanagementservice.entity;

import lombok.*;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
@Builder
public class CandidateAnswer extends ReportBaseEntity{

    private String questionId;
    private List<String>  testTakerAnswer;
}
