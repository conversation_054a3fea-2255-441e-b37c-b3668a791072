package com.amap.amapreportmanagementservice.entity;


import lombok.*;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
@Builder
public class QuestionFlagging extends  ReportBaseEntity{

    @NonNull
    private String questionId;
    @NonNull
    private String testId;
    @NonNull
    private String assessmentId;
    @NonNull
    private String testTakerId;

    private String questionText;
    private String testTakerEmail;
    private List<String> reasonOfFlagging;
}
