package com.amap.amapreportmanagementservice;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableCaching
@EnableScheduling
public class AmapReportManagementServiceApplication {
	public static void main(String[] args) {
		SpringApplication.run(AmapReportManagementServiceApplication.class, args);
	}

}
