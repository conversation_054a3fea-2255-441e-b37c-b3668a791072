package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.AssessmentScoreMetricsDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateScoreMetricsDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateTrajectoryDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.ComparativeAnalysisDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;

import java.util.List;

public interface CandidateAssessmentService {
    void saveCandidateAssessment(AssessmentProgressDTO assessmentDTO);
    void updateCandidateAssessment(AssessmentInputDTO assessmentInputDTO);
    AssessmentScoreMetricsDTO averageScoreMetrics(String organizationId, String assessmentId);
    List<Double> getPercentageScoreDistribution(String organizationId, String assessmentId);
    List<CandidateScoreMetricsDTO> getCandidateScoreMetrics(String organizationId, String assessmentId);

    List<ComparativeAnalysisDTO> getComparedInfo(String organizationId, String assessmentId, String candidateEmail, String candidateId);

    List<CandidateTrajectoryDTO> getCandidateTrajectory(String organizationId, String assessmentId, String candidateEmail, String candidateId);

}
