package com.amap.amapreportmanagementservice.service;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.proc.BadJOSEException;
import org.springframework.stereotype.Service;

import java.text.ParseException;

@Service
public interface JwtService {
    String extractUserName(String token) throws BadJ<PERSON><PERSON><PERSON><PERSON>, ParseException, J<PERSON><PERSON>xception;

    boolean isTokenValid(String token) throws <PERSON><PERSON><PERSON><PERSON><PERSON>, ParseException;

    String extractString(String token, String claim) throws <PERSON><PERSON><PERSON><PERSON>x<PERSON>, ParseException, JOSEException;
    Object extract(String token, String claim) throws <PERSON><PERSON><PERSON>EEx<PERSON>, ParseException, J<PERSON><PERSON>xception;
    String generateToken() throws J<PERSON>EException;
}