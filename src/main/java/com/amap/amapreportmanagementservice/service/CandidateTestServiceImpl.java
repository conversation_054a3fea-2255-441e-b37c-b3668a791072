/**
 * Service implementation for managing candidate test results.
 */
package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateQuestionDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateTestDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.entity.CandidateTest;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.CandidateTestRepository;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.ArrayList;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "services_logger")
public class CandidateTestServiceImpl implements CandidateTestService {
    private final CandidateTestRepository candidateTestRepository;

    /**
     * Saves candidate test results based on assessment progress data.
     *
     * @param assessmentProgressDTO The assessment progress data.
     */
    @Override
    public void saveCandidateTest(AssessmentProgressDTO assessmentProgressDTO) {
        try {
            String pk = KeyBuilder.candidateTestPK(assessmentProgressDTO.getOrganizationId(), assessmentProgressDTO.getAssessmentId());

            for (CandidateTestDTO candidateTestDTO : assessmentProgressDTO.getAssessment().getTests()) {
                String testId = candidateTestDTO.getId();
                String sk = KeyBuilder.candidateTestSK(testId, assessmentProgressDTO.getEmail(), assessmentProgressDTO.getId());

                ArrayList<String> questionIds = new ArrayList<>();
                for(CandidateQuestionDTO question : candidateTestDTO.getQuestions()){
                    questionIds.add(question.getId());
                }

                CandidateTest candidateTest = new CandidateTest();
                candidateTest.setPk(pk);
                candidateTest.setSk(sk);
                candidateTest.setCandidateId(assessmentProgressDTO.getId());
                candidateTest.setPassage(candidateTestDTO.getPassage());
                candidateTest.setCandidateEmail(assessmentProgressDTO.getEmail());
                candidateTest.setTestId(testId);
                candidateTest.setQuestionIDs(questionIds);
                candidateTest.setDomain((candidateTestDTO.getDomain()));
                candidateTestRepository.saveCandidateTest(candidateTest);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }


    /**
     * Updates candidate test results based on assessment input data.
     *
     * @param assessmentInputDTO The assessment input data.
     */
    @Override
    public void updateCandidateTest(AssessmentInputDTO assessmentInputDTO) {
        try {
            String pk = KeyBuilder.candidateTestPK(assessmentInputDTO.getOrganizationId(), assessmentInputDTO.getAssessmentId());

            assessmentInputDTO.getTestResults().forEach(candidateTestDTO -> {
                String testId = candidateTestDTO.getTestId();
                String sk = KeyBuilder.candidateTestSK(testId, assessmentInputDTO.getEmail(), assessmentInputDTO.getTestTakerId());
                Key candidateTestKey = Key.builder().partitionValue(pk).sortValue(sk).build();
                CandidateTest candidateTest = candidateTestRepository.getSpecificCandidateTest(candidateTestKey);
                candidateTest.setCandidateMarks(candidateTestDTO.getTotalPassedScore());
                candidateTest.setTotalScore(candidateTestDTO.getTotalScore());
                candidateTest.setScorePercentage(candidateTestDTO.getTestPercentage());
                candidateTest.setNumberOfQuestions(candidateTestDTO.getNumberOfQuestions());
                candidateTest.setNumberOfQuestionsPassed(candidateTestDTO.getNumberOfQuestionsPassed());
                candidateTest.setNumberOfQuestionsFailed(candidateTestDTO.getNumberOfQuestionsFailed());
                candidateTest.setNumberOfQuestionsAnswered(candidateTestDTO.getNumberOfQuestionsAnswered());
                candidateTest.setTestWindowShotCount(candidateTestDTO.getTestWindowShotCount());
                candidateTest.setTestTakerShotCount(candidateTestDTO.getTestTakerShotCount());
                candidateTest.setTestWindowViolationCount(candidateTestDTO.getTestWindowViolationCount());
                candidateTest.setTestWindowViolationDuration(candidateTestDTO.getTestWindowViolationDuration());
                candidateTest.setTestTakerViolationShotCount(candidateTestDTO.getTestTakerViolationShotCount());
                candidateTest.setTestWindowViolationShotCount(candidateTestDTO.getTestWindowViolationShotCount());
                candidateTest.setStatus(candidateTestDTO.getStatus());
                candidateTest.setPassStatus(candidateTest.getPassStatus());
                candidateTestRepository.updateCandidateTest(candidateTest);
            });
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

}
