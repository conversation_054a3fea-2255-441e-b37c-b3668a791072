package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.entity.CandidateAssessment;
import com.amap.amapreportmanagementservice.entity.WebhookReportJob;

import java.util.List;

public interface WebhookReportJobService {
    void saveWebhookReportJob(CandidateAssessment candidateAssessment);

    void reduceWebhookReportJobTrial(WebhookReportJob webhookReportJob);

    void deleteWebhookReportJob(WebhookReportJob webhookReportJob);

    List<WebhookReportJob> getWebhookReportJobs();

}
