/**
 * Service implementation for handling candidate assessment-related operations.
 */
package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.AssessmentScoreMetricsDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateScoreMetricsDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateTrajectoryDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.ComparativeAnalysisDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.entity.Assessment;
import com.amap.amapreportmanagementservice.entity.AssessmentTest;
import com.amap.amapreportmanagementservice.entity.CandidateAssessment;
import com.amap.amapreportmanagementservice.entity.CandidateTest;
import com.amap.amapreportmanagementservice.exceptions.CandidateNotFoundException;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.*;
import com.amap.amapreportmanagementservice.utils.CandidateRiskCalculator;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.amap.amapreportmanagementservice.utils.Helper.*;
import static com.amap.amapreportmanagementservice.constants.AppConstant.INTEGRITYSCORE;
import static com.amap.amapreportmanagementservice.constants.DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "services_logger")
public class CandidateAssessmentImpl implements CandidateAssessmentService {
    private final AssessmentService assessmentService;
    private final CandidateTestService candidateTestService;
    private final CandidateAssessmentRepository candidateAssessmentRepository;
    private final TestQuestionService testQuestionService;
    private final AssessmentTestService assessmentTestService;
    private final CandidateQuestionService candidateQuestionService;
    private final WebhookReportJobService webhookReportJobService;
    private final AssessmentRepository assessmentRepository;
    private final AssessmentTestRepository assessmentTestRepository;
    private final CandidateTestRepository candidateTestRepository;
    private static final String CANDIDATE_ASSESSMENT_DOES_NOT_EXIST = "Candidate Assessment does not exist";

    /**
     * Updates the candidate assessment based on assessment input data.
     *
     * @param assessmentInputDTO The assessment input data.
     */
    @Override
    public void updateCandidateAssessment(AssessmentInputDTO assessmentInputDTO) {
        CandidateAssessment candidateAssessment = null;
        try {
            String pk = KeyBuilder.candidateAssessmentPK(assessmentInputDTO.getOrganizationId(), assessmentInputDTO.getAssessmentId());
            String sk = KeyBuilder.candidateAssessmentSK(assessmentInputDTO.getEmail(), assessmentInputDTO.getTestTakerId());

            Key candidateKey = Key.builder().partitionValue(pk).sortValue(sk).build();
            candidateAssessment = candidateAssessmentRepository.getSpecificCandidateAssessment(candidateKey);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        if (candidateAssessment == null) {
            throw new CandidateNotFoundException(CANDIDATE_ASSESSMENT_DOES_NOT_EXIST);
        }
        // Create the entity
        DecimalFormat decimalFormat = new DecimalFormat("0.00");

        double candidateMarks = 0;
        float assessmentTotalScore = 0;
        int riskLevel = 0;
//        int testNumber = assessmentInputDTO.getTestList().size();
        try {
            int violationCounts = candidateAssessment.getAssessmentWindowViolationCount()+candidateAssessment.getAssessmentTakerViolationShotCount();
            riskLevel = CandidateRiskCalculator.calculateRiskLevelScore(violationCounts);
            for (TestResultInputDTO candidateTestDTO : assessmentInputDTO.getTestResults()) {
                candidateMarks += candidateTestDTO.getTotalPassedScore();
                assessmentTotalScore += candidateTestDTO.getTotalScore();

            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        try {

            double candidateAssessmentPercentageScore = averagePercentageCalculator(candidateMarks, assessmentTotalScore);

            candidateAssessment.setIntegrityScore(riskLevel);
            candidateAssessment.setStatus(assessmentInputDTO.getStatus());
            candidateAssessment.setTotalScore(assessmentTotalScore);
            candidateAssessment.setAssessmentEndTime(assessmentInputDTO.getAssessmentEndTime());
            candidateAssessment.setCandidateMarks(candidateMarks);
            candidateAssessment.setScorePercentage(Double.parseDouble(decimalFormat.format(candidateAssessmentPercentageScore)));
            candidateAssessment.setAssessmentTakerShotCount(assessmentInputDTO.getAssessmentTakerShotCount());
            candidateAssessment.setAssessmentTakerViolationShotCount(assessmentInputDTO.getAssessmentTakerViolationShotCount());
            candidateAssessment.setAssessmentWindowViolationCount(assessmentInputDTO.getWindowViolationShotCount());
            candidateAssessment.setAssessmentWindowViolationDuration(assessmentInputDTO.getAssessmentWindowViolationDuration());
            candidateAssessment.setWindowShotCount(assessmentInputDTO.getWindowShotCount());
            candidateAssessment.setAssessmentDuration(assessmentInputDTO.getAssessmentDuration());
            candidateAssessment.setTimeTaken(assessmentInputDTO.getTestTakerDurationSeconds());
            candidateAssessment.setProctor(assessmentInputDTO.getProctor());
            candidateAssessment.setProctorLevel(assessmentInputDTO.getProctorLevel());
            candidateAssessment.setDuration(assessmentInputDTO.getTestTakerDurationSeconds());


            candidateAssessmentRepository.updateCandidateAssessment(candidateAssessment);
//        update the assessment - handle the logic here
            candidateTestService.updateCandidateTest(assessmentInputDTO);
            candidateQuestionService.updateCandidateQuestionResult(assessmentInputDTO);
            testQuestionService.updateTestQuestions(assessmentInputDTO);
            assessmentTestService.updateAssessmentTest(assessmentInputDTO);
            assessmentService.updateAssessment(assessmentInputDTO);

//            assessmentTestService.getAssessmentTestAverages(assessmentInputDTO.getOrganizationId(), assessmentInputDTO.getAssessmentId());
            if(!candidateAssessment.getReportCallbackURL().isEmpty()){
                webhookReportJobService.saveWebhookReportJob(candidateAssessment); //                use this when retry is required
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }

    }

    /**
     * Saves the candidate assessment based on assessment progress data.
     *
     * @param assessmentDTO The assessment progress data.
     */
    @Override
    public void saveCandidateAssessment(AssessmentProgressDTO assessmentDTO) {

        // Build the keys
        String pk = null;
        String sk = null;
        try {
            pk = KeyBuilder.candidateAssessmentPK(assessmentDTO.getOrganizationId(),
                    assessmentDTO.getAssessmentId());
            sk = KeyBuilder.candidateAssessmentSK(assessmentDTO.getEmail(), assessmentDTO.getId());
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        // Save to dynamo
        try {
        // Create the entity
            CandidateAssessment candidateAssessment = new CandidateAssessment();
            candidateAssessment.setPk(pk);
            candidateAssessment.setSk(sk);
            candidateAssessment.setCandidateEmail(assessmentDTO.getEmail());
            candidateAssessment.setAssessmentId(assessmentDTO.getAssessmentId());
            candidateAssessment.setOrganizationId(assessmentDTO.getOrganizationId());
            candidateAssessment.setCandidateId(assessmentDTO.getId());
            candidateAssessment.setAssessmentStartTime(assessmentDTO.getAssessment().getAssessmentStartTime());
            candidateAssessment.setIntegrityScore(INTEGRITYSCORE);
            candidateAssessment.setStatus(assessmentDTO.getStatus());
            candidateAssessment.setTitle(assessmentDTO.getAssessment().getTitle());
            candidateAssessment.setAssessmentDuration(assessmentDTO.getAssessment().getAssessmentDuration());
            candidateAssessment.setScreenshotsInterval(assessmentDTO.getScreenshotsInterval());
            candidateAssessment.setCamerashotsInterval(assessmentDTO.getCamerashotsInterval());
            candidateAssessment.setReportCallbackURL(assessmentDTO.getReportCallbackURL());

            candidateAssessmentRepository.saveCandidateAssessment(candidateAssessment);

            // compute data that needs to be computed
            // save or update the assessment - handle the logic here
            candidateTestService.saveCandidateTest(assessmentDTO);
            candidateQuestionService.saveCandidateQuestionResult(assessmentDTO);
            assessmentService.saveAssessment(assessmentDTO);
            assessmentTestService.saveAssessmentTest(assessmentDTO);
            testQuestionService.saveTestQuestions(assessmentDTO);



        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }




    /**
     * Retrieves and caches the average score metrics for a given organization and assessment.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     * @return The average score metrics.
     */
    @Override
    public AssessmentScoreMetricsDTO averageScoreMetrics(String organizationId, String assessmentId) {
        Assessment assessment = null;
        List<CandidateAssessment> candidateAssessments = null;
        try {
            String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
            Key assessmentKey = Key.builder().partitionValue(pk).sortValue(pk).build();
            Key key = Key.builder().partitionValue(pk).sortValue(CANDIDATE_EMAIL_PREFIX).build();
            assessment = assessmentRepository.getAssessment(assessmentKey);
            candidateAssessments = candidateAssessmentRepository.getCandidateAssessment(key);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        try {
            assert assessment != null;
            double assessmentTotalScore = assessment.getTotalScore();
            double totalCandidatePercentageScores = 0;

            assert candidateAssessments != null;
            List<CandidateAssessment> completedAssessments = candidateAssessments.stream()
                    .filter(candAssessment -> "Completed".equals(candAssessment.getStatus()))
                    .toList();
            List<Double> percentageScores = new ArrayList<>();

            if (completedAssessments.isEmpty()) {
                return new AssessmentScoreMetricsDTO(0.0, 0.0, 0.0);
            }
            for (CandidateAssessment candidateAssessment : completedAssessments) {
                if (assessmentTotalScore != 0) {
                    double percentageScore = (candidateAssessment.getCandidateMarks()) / assessmentTotalScore * 100;
                    percentageScores.add(percentageScore);
                    totalCandidatePercentageScores += percentageScore;

                } else {
                    percentageScores.add(0.0);
                }

            }
            DecimalFormat decimalFormat = new DecimalFormat("0.00");

            double highestScore = Double.parseDouble(decimalFormat.format(Collections.max(percentageScores)));
            double lowestScore = Double.parseDouble(decimalFormat.format(Collections.min(percentageScores)));


            double averageScore = Double.parseDouble(decimalFormat.format(totalCandidatePercentageScores / completedAssessments.size()));
            return new AssessmentScoreMetricsDTO(averageScore, highestScore, lowestScore);
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieves and caches the distribution of percentage scores for a given organization and assessment.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     * @return A list of percentage scores.
     */
    @Override
    public List<Double> getPercentageScoreDistribution(String organizationId, String assessmentId) {
        try {
            String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
            Key key = Key.builder().partitionValue(pk).sortValue(CANDIDATE_EMAIL_PREFIX).build();
            List<CandidateAssessment> candidateAssessments = candidateAssessmentRepository.getCandidateAssessment(key);
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            List<CandidateAssessment> completedAssessments = candidateAssessments.stream()
                    .filter(candAssessment -> "Completed".equals(candAssessment.getStatus()))
                    .toList();
            List<Double> percentageScores = completedAssessments.stream()
                    .map(candidateAssessment -> Double.parseDouble(decimalFormat.format(candidateAssessment.getScorePercentage()))).toList();

            return new ArrayList<>(percentageScores);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieves and caches the candidate score metrics for a given organization and assessment.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     * @return A list of candidate score metrics.
     */
    @Override
    public List<CandidateScoreMetricsDTO> getCandidateScoreMetrics(String organizationId, String assessmentId) {
        List<CandidateAssessment> candidateAssessments = null;
        try {
            String pk = KeyBuilder.candidateAssessmentPK(organizationId, assessmentId);
            Key key = Key.builder().partitionValue(pk).sortValue(CANDIDATE_EMAIL_PREFIX).build();
            candidateAssessments = candidateAssessmentRepository.getCandidateAssessment(key);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        List<CandidateScoreMetricsDTO> candidateScoreMetricsList = new ArrayList<>();
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        try {
            assert candidateAssessments != null;
            for (CandidateAssessment assessment : candidateAssessments) {
                String riskLevel = CandidateRiskCalculator.determineRiskLevel(assessment.getIntegrityScore());

                CandidateScoreMetricsDTO candidateScoreMetrics = CandidateScoreMetricsDTO.builder()
                        .candidateId(assessment.getCandidateId())
                        .candidateEmail(assessment.getCandidateEmail())
                        .candidateMarks(assessment.getCandidateMarks())
                        .riskLevel(riskLevel)
                        .integrityScore(assessment.getIntegrityScore())
                        .scorePercentage(Double.parseDouble(decimalFormat.format(assessment.getScorePercentage())))
                        .proctoringLevel(assessment.getProctorLevel())
                        .camerashotsInterval(assessment.getCamerashotsInterval())
                        .screenshotsInterval(assessment.getScreenshotsInterval())
                        .assessmentWindowViolationCount(assessment.getAssessmentWindowViolationCount())
                        .assessmentWindowViolationDuration(assessment.getAssessmentWindowViolationDuration())
                        .windowViolationShotCount(assessment.getAssessmentTakerViolationShotCount())
                        .build();

                candidateScoreMetricsList.add(candidateScoreMetrics);
            }
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
        return candidateScoreMetricsList;
    }

    /**
     * Retrieves and caches comparative analysis information for a candidate in a given organization and assessment.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     * @param candidateEmail The candidate's email address.
     * @param candidateId    The candidate identifier.
     * @return A list of comparative analysis data.
     */
    @Override
    public List<ComparativeAnalysisDTO> getComparedInfo(String organizationId, String assessmentId, String candidateEmail, String candidateId) {
    String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
    String sk = KeyBuilder.assessmentSK(organizationId, assessmentId);
    String candidatePK = KeyBuilder.candidateAssessmentPK(organizationId, assessmentId);
    String candidateSK = KeyBuilder.candidateAssessmentSK(candidateEmail, candidateId);
    DecimalFormat decimalFormat = new DecimalFormat("0.00");

    try {
        CandidateAssessment candidate = candidateAssessmentRepository.getSpecificCandidateAssessment(Key.builder()
                .partitionValue(candidatePK)
                .sortValue(candidateSK)
                .build());

        if (candidate == null) {
            throw new CandidateNotFoundException(CANDIDATE_ASSESSMENT_DOES_NOT_EXIST);
        }

        Key assessmentKey = Key.builder().partitionValue(pk).sortValue(sk).build();
        List<Assessment> assessments = assessmentRepository.getAssessments(assessmentKey);

        if (assessments == null || assessments.isEmpty()) {
            return Collections.emptyList();
        }

        return assessments.stream()
                .flatMap(test -> test.getTestsIds().stream())
                .flatMap(testId -> {
                    String assessmentTestSk = KeyBuilder.assessmentTestSK(assessmentId, testId);
                    Key key = Key.builder().partitionValue(pk).sortValue(assessmentTestSk).build();

                    List<AssessmentTest> assessmentTests = assessmentTestRepository.getAssessmentTestsWithoutCandidateTest(key);
                    String candidateTestSk = KeyBuilder.candidateTestSK(testId, candidateEmail, candidateId);

                    return assessmentTests.stream().map(assessmentTest -> {
                        Optional<Double> average = Optional.of(assessmentTest.getAverageScore());
                        String assessmentTestTitle = assessmentTest.getTitle();

                        CandidateTest candidateTest = candidateTestRepository.getSpecificCandidateTest(Key.builder()
                                .partitionValue(pk)
                                .sortValue(candidateTestSk)
                                .build());


//                        double candidateScore = (candidateTest.getCandidateMarks() / assessmentTest.getTotalScore()) * 100;
                        return new ComparativeAnalysisDTO(assessmentTestTitle,
                                Double.parseDouble(decimalFormat.format(average.orElse(0.0))),
                                Double.parseDouble(decimalFormat.format(candidateTest.getScorePercentage())));
                    });
                })
                .toList();

    } catch (Exception e) {
        log.error(e.getMessage());
        throw new ProcessFailedException(e.getMessage());
    }
}


    /**
     * Retrieves and caches the candidate trajectory for a given candidate in a specific organization and assessment.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     * @param candidateEmail The candidate's email address.
     * @param candidateId    The candidate identifier.
     * @return A list of candidate trajectory data.
     */
    @Override
    public List<CandidateTrajectoryDTO> getCandidateTrajectory(String organizationId, String assessmentId, String candidateEmail, String candidateId) {
    String candidatePK = KeyBuilder.candidateAssessmentPK(organizationId, assessmentId);
    String candidateSK = KeyBuilder.candidateAssessmentSK(candidateEmail, candidateId);

    Key candidateKey = Key.builder().partitionValue(candidatePK).sortValue(candidateSK).build();
    String canEmailSK = KeyBuilder.canEmailSk(candidateEmail);
    Key candidateTrajectoryKey = Key.builder().partitionValue(candidatePK).sortValue(canEmailSK).build();

    try {
        CandidateAssessment candidate = candidateAssessmentRepository.getSpecificCandidateAssessment(candidateKey);

        if (candidate == null) {
            throw new CandidateNotFoundException(CANDIDATE_ASSESSMENT_DOES_NOT_EXIST);
        }

        List<CandidateAssessment> candidateAssessments = candidateAssessmentRepository.getCandidateAssessment(candidateTrajectoryKey);

        if (candidateAssessments == null || candidateAssessments.isEmpty()) {
            return Collections.emptyList();
        }

        return candidateAssessments.stream()
                .map(candidateAssessment -> CandidateTrajectoryDTO.builder()
                        .assessmentTitle(candidateAssessment.getTitle())
                        .assessmentStartTime(candidateAssessment.getAssessmentStartTime())
                        .assessmentScore(candidateAssessment.getCandidateMarks())
                        .build())
                .toList();

    } catch (Exception e) {
        log.error(e.getMessage());
        throw new ProcessFailedException(e.getMessage());
    }
}


}
