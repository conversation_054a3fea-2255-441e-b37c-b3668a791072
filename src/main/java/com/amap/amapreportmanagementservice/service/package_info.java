/**
 * This package contains a set of services responsible for various tasks related to assessment management.
 * These services provide functionality for saving, updating, and retrieving information about assessments,
 * candidate tests, test questions, feedback, and more.
 *
 * <p>
 * The primary services in this package include:
 * <ul>
 *   <li>{@link com.amap.amapreportmanagementservice.service.AssessmentService}: Manages assessments.</li>
 *   <li>{@link com.amap.amapreportmanagementservice.service.AssessmentTestService}: Manages assessment tests.</li>
 *   <li>{@link com.amap.amapreportmanagementservice.service.CandidateTestService}: Manages candidate tests.</li>
 *   <li>{@link com.amap.amapreportmanagementservice.service.CandidateAssessmentService}: Manages candidate assessments.</li>
 *   <li>{@link com.amap.amapreportmanagementservice.service.CandidateFeedbackService}: Manages candidate feedback.</li>
 *   <li>{@link com.amap.amapreportmanagementservice.service.CandidateQuestionService}: Manages candidate questions.</li>
 *   <li>{@link com.amap.amapreportmanagementservice.service.JwtService}: Manages JSON Web Tokens (JWT) for authentication.</li>
 *   <li>{@link com.amap.amapreportmanagementservice.service.KafkaService}: Handles message consumption from Kafka topics.</li>
 *   <li>{@link com.amap.amapreportmanagementservice.service.ResponseService}: Provides information about assessment results and metrics.</li>
 *   <li>{@link com.amap.amapreportmanagementservice.service.TestQuestionService}: Manages test questions and their results.</li>
 * </ul>
 * </p>
 *
 * <p>
 * Additionally, several data transfer objects (DTOs) are used to represent data structures for communication
 * between services and external components. These include DTOs for progress tracking, assessment details,
 * test results, feedback, and more.
 * </p>
 *
 * @see com.amap.amapreportmanagementservice.service
 * @see com.amap.amapreportmanagementservice.dto
 */
package com.amap.amapreportmanagementservice.service;