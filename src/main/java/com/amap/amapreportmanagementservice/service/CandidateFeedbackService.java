package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.FeedbackResponseDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateFeedbackDTO;
import com.amap.amapreportmanagementservice.dto.feedback.FeedbackSurveyDTO;

import java.util.List;

public interface CandidateFeedbackService {

    void saveFeedback(FeedbackSurveyDTO feedbackSurveyDTO);

    CandidateFeedbackDTO getFeedback(String organizationId, String assessmentId, String candidateId);

    List<FeedbackResponseDTO> getAllFeedbacks(String organizationId, String assessmentId);
}
