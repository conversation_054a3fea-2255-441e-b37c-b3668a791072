package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.results.FlaggedInputDTO;
import com.amap.amapreportmanagementservice.entity.QuestionFlagging;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.QuestionFlaggingRepository;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.List;


@Service
@RequiredArgsConstructor
@Slf4j(topic = "services_logger")
public class QuestionFlaggingServiceImpl implements QuestionFlaggingService{

    private  final QuestionFlaggingRepository questionFlaggingRepository;
    @Override
    public void saveQuestionFlagging(FlaggedInputDTO flaggedInputDTO  ) {
        try {
            String pk = KeyBuilder.questionFlaggingPK(flaggedInputDTO.getOrganizationId(), flaggedInputDTO.getAssessmentId());

            String sk = KeyBuilder.questionFlaggingSK(flaggedInputDTO.getQuestionId());

            QuestionFlagging questionFlagging = getQuestionFlagging(flaggedInputDTO, pk, sk);
            questionFlaggingRepository.saveQuestionFlagging(questionFlagging);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }

    }

    @NotNull
    private static QuestionFlagging getQuestionFlagging(FlaggedInputDTO flaggedInputDTO, String pk, String sk) {
        QuestionFlagging questionFlagging = new QuestionFlagging();
        questionFlagging.setPk(pk);
        questionFlagging.setSk(sk);
        questionFlagging.setTestTakerId(flaggedInputDTO.getTestTakerId());
        questionFlagging.setTestTakerEmail(flaggedInputDTO.getTestTakerEmail());
        questionFlagging.setOrganizationId(flaggedInputDTO.getOrganizationId());
        questionFlagging.setAssessmentId(flaggedInputDTO.getAssessmentId());
        questionFlagging.setTestId(flaggedInputDTO.getTestId());
        questionFlagging.setQuestionId(flaggedInputDTO.getQuestionId());
        questionFlagging.setReasonOfFlagging(flaggedInputDTO.getReasonForFlagging());
        return questionFlagging;
    }

    @Override
    public List<QuestionFlagging> getQuestionFlaggingsByQuestion(String organizationId, String assessmentId, String questionId) {
        
        try {

            String pk = KeyBuilder.questionFlaggingPK(organizationId, assessmentId );
            String sk = KeyBuilder.questionFlaggingSK(questionId);
            Key questionFlaggingKey = Key.builder().partitionValue(pk).sortValue(sk).build();

            return questionFlaggingRepository.getQuestionFlaggings(questionFlaggingKey);

        } catch (Exception e){
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }


}






