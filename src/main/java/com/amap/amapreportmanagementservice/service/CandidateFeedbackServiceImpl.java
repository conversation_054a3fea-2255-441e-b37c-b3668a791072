/**
 * Service implementation for handling candidate feedback-related operations.
 */
package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.FeedbackResponseDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateFeedbackDTO;
import com.amap.amapreportmanagementservice.dto.feedback.FeedbackSurveyDTO;
import com.amap.amapreportmanagementservice.dto.feedback.FeedbackSurveyQuestionDTO;
import com.amap.amapreportmanagementservice.entity.CandidateFeedback;
import com.amap.amapreportmanagementservice.entity.Feedback;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.CandidateFeedbackRepository;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.amap.amapreportmanagementservice.constants.DbKeysPrefixes.CANDIDATE_ID_PREFIX;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "services_logger")
public class CandidateFeedbackServiceImpl implements CandidateFeedbackService{
    private final CandidateFeedbackRepository candidateFeedbackRepository;
    private final AssessmentService assessmentService;
    private final ObjectMapper objectMapper;

     /**
     * Saves candidate feedback based on a feedback survey.
     *
     * @param feedbackSurvey The feedback survey data.
     */
    @Override
    public void saveFeedback(FeedbackSurveyDTO feedbackSurvey) {
        try {
            CandidateFeedback candidateFeedback = new CandidateFeedback();
            ZonedDateTime zonedDateTime = ZonedDateTime.now();

            String candidateFeedbackPk = KeyBuilder.candidateFeedbackPK(feedbackSurvey.getOrganizationId(), feedbackSurvey.getAssessmentId());
            String candidateFeedbackSk = KeyBuilder.candidateFeedbackSk(feedbackSurvey.getTestTakerId());

            candidateFeedback.setPk(candidateFeedbackPk);
            candidateFeedback.setSk(candidateFeedbackSk);

            candidateFeedback.setOrganizationId(feedbackSurvey.getOrganizationId());
            candidateFeedback.setAssessmentId(feedbackSurvey.getAssessmentId());
            candidateFeedback.setCandidateId(feedbackSurvey.getTestTakerId());
            candidateFeedback.setCandidateEmail(feedbackSurvey.getTakerEmail());
            candidateFeedback.setDate(String.valueOf(zonedDateTime));


            List<Feedback> feedbacks = new ArrayList<>();
            for (FeedbackSurveyQuestionDTO feedbackSurveyQuestion : feedbackSurvey.getSurveyQuestions()) {
                feedbacks.add(objectMapper.convertValue(feedbackSurveyQuestion, Feedback.class));
            }

            candidateFeedback.setFeedbacks(feedbacks);
            candidateFeedbackRepository.saveCandidateFeedback(candidateFeedback);

            assessmentService.updateAssessmentFeedback(feedbackSurvey);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

     /**
     * Retrieves candidate feedback for a specific organization, assessment, and candidate.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     * @param candidateId    The candidate identifier.
     * @return The candidate feedback as a DTO.
     */
    @Override
    public CandidateFeedbackDTO getFeedback(String organizationId, String assessmentId, String candidateId) {
        try {
            String candidateFeedbackPk = KeyBuilder.candidateFeedbackPK(organizationId, assessmentId);
            String candidateFeedbackSk = KeyBuilder.candidateFeedbackSk(candidateId);

            Key key = Key.builder().partitionValue(candidateFeedbackPk).sortValue(candidateFeedbackSk).build();
            CandidateFeedback candidateFeedback = candidateFeedbackRepository.getCandidateFeedback(key);
            return objectMapper.convertValue(candidateFeedback, CandidateFeedbackDTO.class);
        } catch (IllegalArgumentException e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieves all feedback responses for a specific organization and assessment.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     * @return A list of feedback response DTOs.
     */
    @Override
    public List<FeedbackResponseDTO> getAllFeedbacks(String organizationId, String assessmentId) {
        String candidateFeedbackPk = KeyBuilder.candidateFeedbackPK(organizationId, assessmentId);

        Key key = Key.builder().partitionValue(candidateFeedbackPk).sortValue(CANDIDATE_ID_PREFIX).build();
        List<FeedbackResponseDTO> feedbackResponseDTOS = new ArrayList<>();
        try {
            List<CandidateFeedback> candidateFeedbacks = candidateFeedbackRepository.getAllFeedbacks(key);
            for(CandidateFeedback candidateFeedback : candidateFeedbacks){
                FeedbackResponseDTO feedbackResponseDTO = new FeedbackResponseDTO();
                feedbackResponseDTO.setCandidateEmail(candidateFeedback.getCandidateEmail());
                List<Feedback> feedbackList = candidateFeedback.getFeedbacks();

                Feedback overallRating = feedbackList.get(0);
                Feedback testRating = feedbackList.get(1);
                Feedback comment = feedbackList.get(2);

                feedbackResponseDTO.setOverallRating(Double.parseDouble(overallRating.getQuestionAnswer()));
                feedbackResponseDTO.setTestRating(Double.parseDouble(testRating.getQuestionAnswer()));
                feedbackResponseDTO.setComment(comment.getQuestionAnswer());
                feedbackResponseDTO.setDate(candidateFeedback.getDate());
                feedbackResponseDTOS.add(feedbackResponseDTO);
            }
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }

        return feedbackResponseDTOS;
    }

}
