/**
 * Service for managing test questions and their results.
 */
package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.MissedQuestionDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.QuestionResultInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.entity.Assessment;
import com.amap.amapreportmanagementservice.entity.AssessmentTest;
import com.amap.amapreportmanagementservice.entity.TestQuestion;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.AssessmentRepository;
import com.amap.amapreportmanagementservice.repository.AssessmentTestRepository;
import com.amap.amapreportmanagementservice.repository.TestQuestionRepository;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.List;

import static com.amap.amapreportmanagementservice.constants.DbKeysPrefixes.ASSESSMENT_PREFIX;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "services_logger")
public class TestQuestionServiceImpl implements TestQuestionService {
    private final TestQuestionRepository testQuestionRepository;
    private final AssessmentTestRepository assessmentTestRepository;
    private final AssessmentRepository assessmentRepository;

       /**
     * Save test questions based on the assessment progress.
     *
     * @param assessmentProgressDTO The assessment progress data.
     * @throws ProcessFailedException if an error occurs during the process.
     */
    @Override
    public void saveTestQuestions(AssessmentProgressDTO assessmentProgressDTO) {
        try {
            assessmentProgressDTO.getAssessment().getTests().forEach(candidateTestDTO -> {
                String testId = candidateTestDTO.getId();
                String pk = KeyBuilder.testQuestionPK(assessmentProgressDTO.getOrganizationId(), assessmentProgressDTO.getAssessmentId(), testId);

                candidateTestDTO.getQuestions().forEach(questionDTO -> {
                    String questionId = questionDTO.getId();
                    String sk = KeyBuilder.testQuestionSK(questionId);

                    if (testQuestionRepository.checkIfExist(assessmentProgressDTO.getOrganizationId(), assessmentProgressDTO.getAssessmentId(),
                            testId, questionId)) {
                        return;

                    }
                    TestQuestion testQuestion = new TestQuestion();
                    testQuestion.setPk(pk);
                    testQuestion.setSk(sk);
                    testQuestion.setOrganizationId(assessmentProgressDTO.getOrganizationId());
                    testQuestion.setAssessmentId(assessmentProgressDTO.getAssessmentId());
                    testQuestion.setTitle(candidateTestDTO.getTitle());
                    testQuestion.setDuration(questionDTO.getTimeLimit());
                    testQuestion.setDifficultyLevel(questionDTO.getDifficultyLevel());
                    testQuestion.setDomainId(questionDTO.getDomainId());
                    testQuestion.setCategoryId(questionDTO.getCategoryId());
                    testQuestion.setQuestionText(questionDTO.getQuestionText());
                    testQuestion.setQuestionId(questionDTO.getId());
                    testQuestion.setTestId(testId);
                    testQuestion.setDomain(questionDTO.getDomain());
                    testQuestion.setCategory(questionDTO.getCategory());
                    testQuestionRepository.saveTestQuestion(testQuestion);
                });
            });
        } catch (Exception exception) {
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

    /**
     * Update test questions based on the assessment input.
     *
     * @param assessmentInputDTO The assessment input data.
     * @throws ProcessFailedException if an error occurs during the process.
     */
    @Override
    public void updateTestQuestions(AssessmentInputDTO assessmentInputDTO) {
        try {

            for (TestResultInputDTO testResultInputDTO : assessmentInputDTO.getTestResults()) {
                String testId = testResultInputDTO.getTestId();
                String pk = KeyBuilder.testQuestionPK(assessmentInputDTO.getOrganizationId(), assessmentInputDTO.getAssessmentId(), testId);
                for (QuestionResultInputDTO questionResultInputDTO : testResultInputDTO.getQuestionResults()) {
                    String sk = KeyBuilder.testQuestionSK(questionResultInputDTO.getQuestionId());
                    TestQuestion testQuestion = testQuestionRepository.getTestQuestion(Key.builder().partitionValue(pk).sortValue(sk).build());
                    if(testQuestion != null) {
                        if (Boolean.parseBoolean(questionResultInputDTO.getIsAnswerCorrect())) {
                            testQuestion.setNumberOfTimesMissed(testQuestion.getNumberOfTimesMissed() + 1);
                        }
                        int questionScore = questionResultInputDTO.getScore();
                        testQuestion.setTotalScore(questionResultInputDTO.getScore());
                        testQuestion.setNumberOfTimesAnswered(questionScore);
                        testQuestionRepository.updateTestQuestion(testQuestion);
                    }
                }
            }
        } catch (Exception exception) {
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

    /**
     * Retrieve missed questions for a specific assessment.
     *
     * @param organizationId The organization ID.
     * @param assessmentId   The assessment ID.
     * @return List of missed questions for the assessment.
     * @throws ProcessFailedException if an error occurs during the process.
     */
    @Override
    public List<MissedQuestionDTO> getMissedQuestions(String organizationId, String assessmentId) {
        try {
            String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
            Key key = Key.builder().partitionValue(pk).sortValue(pk).build();
            Assessment assessment = assessmentRepository.getAssessment(key);

            return assessment.getTestsIds().stream()
                    .flatMap(testId -> {
                        Key assessmentTestKey = Key.builder().partitionValue(pk).sortValue(ASSESSMENT_PREFIX).build();
                        List<AssessmentTest> assessmentTests = assessmentTestRepository.getAssessmentTestsWithoutCandidateTest(assessmentTestKey);

                        return assessmentTests.stream()
                                .flatMap(test -> test.getQuestionIds().stream())
                                .flatMap(questionId -> {
                                    String testQuestionPk = KeyBuilder.testQuestionPK(organizationId, assessmentId, testId);
                                    String testQuestionSk = KeyBuilder.testQuestionSK(questionId);
                                    Key testQuestionKey = Key.builder().partitionValue(testQuestionPk).sortValue(testQuestionSk).build();
                                    List<TestQuestion> testQuestions = testQuestionRepository.getTestQuestions(testQuestionKey);

                                    return testQuestions.stream()
                                            .filter(testQuestion -> testQuestion.getNumberOfTimesMissed() > 0)
                                            .map(testQuestion -> {
                                                MissedQuestionDTO missedQuestionDTO = new MissedQuestionDTO();
                                                missedQuestionDTO.setQuestionText(testQuestion.getQuestionText());
                                                missedQuestionDTO.setTestId(testId);
                                                missedQuestionDTO.setPercentageOfCandidates(
                                                        ((double) testQuestion.getNumberOfTimesMissed() / (double) testQuestion.getNumberOfTimesAnswered()) * 100
                                                );
                                                return missedQuestionDTO;
                                            });
                                });
                    })
                    .toList();
        } catch (Exception exception) {
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

}
