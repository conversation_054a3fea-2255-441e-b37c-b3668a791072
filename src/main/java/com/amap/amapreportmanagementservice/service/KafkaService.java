/**
 * Service for consuming messages from Kafka topics and processing them.
 */
package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.feedback.FeedbackSurveyDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentTakerDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.FlaggedInputDTO;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "kafka_logger")
public class KafkaService {
    private final ObjectMapper objectMapper;
    private final CandidateAssessmentService candidateAssessmentService;
    private final CandidateQuestionService candidateQuestionService;
    private final QuestionFlaggingService questionFlaggingService;
    private final CandidateFeedbackService candidateFeedbackService;
    private final CacheManager cacheManager;

    /**
     * Kafka listener for consuming messages from the "submit-assessment-results" topic.
     *
     * @param message The Kafka message containing assessment results data.
     */
    @KafkaListener(topics = "submit-assessment-results", groupId = "reana-group")
    public void consumeAssessmentResults(ConsumerRecord<String, String> message) {
        log.info("message successfully consumed:" + message.value());
        try {
            HashMap<String, Object> assessmentObject = objectMapper.readValue(message.value(), new TypeReference<>() {
            });
            AssessmentInputDTO assessmentDTO = objectMapper.convertValue(assessmentObject, AssessmentInputDTO.class);
            log.info("assessment successfully converted:" + objectMapper.writeValueAsString(assessmentDTO));
            try {
                candidateAssessmentService.updateCandidateAssessment(assessmentDTO);
                log.info("assessment successfully updated:" + assessmentDTO);
                // Clear cache after successful update
                clearCache("getOrganizationAssignmentCache");
                clearCache("getGeneralDetailsCache");
                clearCache("getAssessmentDetailsCache");
                clearCache("getCandidateMetricsCache");
                clearCache("getComparativeAnalysisCache");
                clearCache("getAllComparativeAnalysisCache");
                clearCache("getAllDetails");
                clearCache("getAllComparativeAnalysis");
                clearCache("getAllTrajectories");
            } catch (Exception e) {
                log.error("Failed to update candidate assessment: {}", e.getMessage(), e);
                // Consider implementing retry logic here
            }
        } catch (Exception ex) {
            log.error("Error processing assessment result message: {}", ex.getMessage(), ex);
        }
    }

    /**
     * Kafka listener for consuming messages from the "assessment-progress" topic.
     *
     * @param message The Kafka message containing assessment progress data.
     */
    @KafkaListener(topics = "assessment-progress", groupId = "reana-group")
    public void consumeAssessmentProgress(ConsumerRecord<String, String> message) {
        log.info("message successfully consumed:" + message.value());
        try {
            HashMap<String, Object> assessmentObject = objectMapper.readValue(message.value(), new TypeReference<>() {
            });
            log.info("assessment successfully converted:" + objectMapper.writeValueAsString(assessmentObject));
            AssessmentTakerDTO assessmentDTO = objectMapper.convertValue(assessmentObject, AssessmentTakerDTO.class);
            candidateAssessmentService.saveCandidateAssessment(assessmentDTO.getAssessmentTaker());
            log.info("assessment successfully updated:" + assessmentDTO);
            // Reset the cache
        } catch (Exception ex) {
            log.error(ex.getMessage());
            throw new ProcessFailedException(ex.getMessage());
        }
    }


    public void clearCache(String cacheName) {
        Cache getOrganizationAssignmentCache = cacheManager.getCache(cacheName);
        if (getOrganizationAssignmentCache != null) {
            getOrganizationAssignmentCache.clear();
        }
    }

    /**
     * Kafka listener for consuming messages from the "report-question-flagged" topic.
     *
     * @param message The Kafka message containing flagged question data.
     */
    @KafkaListener(topics = "report-question-flagged", groupId = "reana-group")
    public void consumeQuestionFlagged(ConsumerRecord<String, String> message) {
        processFlaggedQuestion(message);
    }

    /**
     * Kafka listener for consuming messages from the "report-question-unflagged" topic.
     *
     * @param message The Kafka message containing unflagged question data.
     */
    @KafkaListener(topics = "report-question-unflagged", groupId = "reana-group")
    public void consumeQuestionUnflagged(ConsumerRecord<String, String> message) {
        processFlaggedQuestion(message);
    }

    public void processFlaggedQuestion(ConsumerRecord<String, String> message) {
        try {
            HashMap<String, Object> flagObject = objectMapper.readValue(message.value(), new TypeReference<>() {
            });
            FlaggedInputDTO flaggedInputDTO = objectMapper.convertValue(flagObject, FlaggedInputDTO.class);
            candidateQuestionService.updateFlaggedQuestion(flaggedInputDTO);
            if(flaggedInputDTO.getReasonForFlagging() != null) {
                questionFlaggingService.saveQuestionFlagging(flaggedInputDTO);
            }

        } catch (Exception ex) {
            log.error(ex.getMessage());
            throw new ProcessFailedException(ex.getMessage());
        }
    }

    /**
     * Kafka listener for consuming messages from the "survey-completed" topic.
     *
     * @param message The Kafka message containing survey feedback data.
     */
    @KafkaListener(topics = "survey-completed", groupId = "reana-group")
    public void consumeSurveyFeedback(ConsumerRecord<String, String> message) {
        try {
            HashMap<String, Object> feedback = objectMapper.readValue(message.value(), new TypeReference<>() {
            });
            FeedbackSurveyDTO feedbackSurvey = objectMapper.convertValue(feedback, FeedbackSurveyDTO.class);
            candidateFeedbackService.saveFeedback(feedbackSurvey);
//            clear feedbacks cache
            clearCache("getFeedbacks");
        } catch (Exception ex) {
            log.error(ex.getMessage());
            throw new ProcessFailedException(ex.getMessage());
        }
    }
}
