/**
 * Service implementation for handling assessment test-related operations.
 */

package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.TestInfoDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateQuestionDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateTestDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.entity.Assessment;
import com.amap.amapreportmanagementservice.entity.AssessmentTest;
import com.amap.amapreportmanagementservice.entity.CandidateTest;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.AssessmentRepository;
import com.amap.amapreportmanagementservice.repository.AssessmentTestRepository;
import com.amap.amapreportmanagementservice.repository.CandidateTestRepository;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.text.DecimalFormat;
import java.util.*;

import static com.amap.amapreportmanagementservice.constants.DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "services_logger")
public class AssessmentTestServiceImpl implements AssessmentTestService {
    private final AssessmentTestRepository assessmentTestRepository;
    private final CandidateTestRepository candidateTestRepository;
    private final AssessmentRepository assessmentRepository;

    /**
     * Saves assessment tests and their details.
     *
     * @param assessmentProgressDTO The assessment progress data.
     */
    @Override
    public void saveAssessmentTest(AssessmentProgressDTO assessmentProgressDTO) {
        try {
            String pk = KeyBuilder.assessmentTestPK(assessmentProgressDTO.getOrganizationId(), assessmentProgressDTO.getAssessmentId());

            for (CandidateTestDTO candidateTestDTO : assessmentProgressDTO.getAssessment().getTests()) {
                String testId = candidateTestDTO.getId();
                String sk = KeyBuilder.assessmentTestSK(assessmentProgressDTO.getAssessmentId(), testId);

                boolean ifExist = assessmentTestRepository.checkIfExists(assessmentProgressDTO.getOrganizationId(),
                        assessmentProgressDTO.getAssessmentId(), testId);
                if (ifExist) {
                    Key assessmetTestKey = Key.builder().partitionValue(pk).sortValue(sk).build();
                    AssessmentTest assessmentTest = assessmentTestRepository.getAssessmentTest(assessmetTestKey);
                    List<String> questionIds = candidateTestDTO.getQuestions().stream()
                            .map(CandidateQuestionDTO::getId)
                            .toList();
                    List<String> savedQuestionIds =  assessmentTest.getQuestionIds();
                    List<String> UniqueQuestionIds = mergeUnique(questionIds, savedQuestionIds);
                    assessmentTest.setQuestionIds(UniqueQuestionIds);
                    assessmentTestRepository.saveAssessmentTest(assessmentTest);
                    continue;
                }
                List<String> questionIds = candidateTestDTO.getQuestions().stream()
                        .map(CandidateQuestionDTO::getId)
                        .toList();

                AssessmentTest assessmentTest = new AssessmentTest();
                assessmentTest.setPk(pk);
                assessmentTest.setSk(sk);
                assessmentTest.setAssessmentId(assessmentProgressDTO.getAssessmentId());
                assessmentTest.setTestId(testId);
                assessmentTest.setDuration(candidateTestDTO.getDuration());
                assessmentTest.setTitle(candidateTestDTO.getTitle());
                assessmentTest.setPassage(candidateTestDTO.getPassage());
                assessmentTest.setOrganizationId(assessmentProgressDTO.getOrganizationId());
                assessmentTest.setQuestionIds(questionIds);
                assessmentTest.setDomain(candidateTestDTO.getDomain());
                assessmentTestRepository.saveAssessmentTest(assessmentTest);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

     /**
     * Updates assessment test statistics based on test results.
     *
     * @param assessmentInputDTO The assessment input data.
     */
    @Override
    public void updateAssessmentTest(AssessmentInputDTO assessmentInputDTO) {
        try {
            String pk = KeyBuilder.assessmentTestPK(assessmentInputDTO.getOrganizationId(), assessmentInputDTO.getAssessmentId());


            for (TestResultInputDTO testResultInputDTO : assessmentInputDTO.getTestResults()) {
                if(!"SUBMITTED".equals(testResultInputDTO.getStatus())) continue;
                String testId = testResultInputDTO.getTestId();
                String sk = KeyBuilder.assessmentTestSK(assessmentInputDTO.getAssessmentId(), testId);
                Key assessmetTestKey = Key.builder().partitionValue(pk).sortValue(sk).build();
                AssessmentTest assessmentTests = assessmentTestRepository.getAssessmentTest(assessmetTestKey);

                int numberOfTimesAnswered = assessmentTests.getNumberOfTimesAnswered() + 1;
                double totalCandidatesMarks = assessmentTests.getTotalCandidateMarks()+testResultInputDTO.getTotalPassedScore();
                double averageScore = totalCandidatesMarks/numberOfTimesAnswered;
//                double averagePercentage = (averageScore / assessmentTests.getTotalScore()) * 100;
//                double totalPercentageScore = assessmentTests.getAveragePercentageScore()+testResultInputDTO.getTotalPassedScore();
                assessmentTests.setTotalScore(testResultInputDTO.getTotalScore());
                assessmentTests.setTotalCandidateMarks(totalCandidatesMarks);
                assessmentTests.setTotalCandidatePercentage(testResultInputDTO.getTestPercentage());
                assessmentTests.setNumberOfTimesAnswered(numberOfTimesAnswered);
                assessmentTests.setAverageScore(averageScore);
                assessmentTestRepository.updateAssessmentTest(assessmentTests);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

     /**
     * Retrieves and caches information about assessment tests, including averages, counts, and categories.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     * @return A list of test information DTOs.
     */
    @Override
    public List<TestInfoDTO> getAssessmentTestInfo(String organizationId, String assessmentId) {
        String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
        String sk = KeyBuilder.assessmentSK(organizationId, assessmentId);
        Key assessmentkey = Key.builder().partitionValue(pk).sortValue(sk).build();
        List<Assessment> assessments = null;
        try {
            assessments = assessmentRepository.getAssessments(assessmentkey);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        DecimalFormat decimalFormat = new DecimalFormat("0.00");

        List<TestInfoDTO> testAverages = new ArrayList<>();
        try {
            assert assessments != null;
            testAverages = assessments.stream()
                    .flatMap(test -> test.getTestsIds().stream())
                    .distinct()
                    .map(testId -> {
                        String assessmentTestSk = KeyBuilder.assessmentTestSK(assessmentId,testId);
                        Key key = Key.builder().partitionValue(pk).sortValue(assessmentTestSk).build();
                        AssessmentTest assessmentTest = assessmentTestRepository.getAssessmentTest(key);
                        if (assessmentTest.getNumberOfTimesAnswered() == 0) {
                            return null; //Skip tests with no number of times answered
                        }
                        int numberOfCandidates = assessmentTest.getNumberOfTimesAnswered();
                        String testTitle = assessmentTest.getTitle();
                        int numberOfQuestions = assessmentTest.getQuestionIds().size();
                        int numberOfFlags = assessmentTest.getNumberOfFlags();

                        double totalCandidatesMarks = assessmentTest.getTotalCandidateMarks();
                        double average = assessmentTest.getAverageScore();
                        double averagePercentage = (average / assessmentTest.getTotalScore() ) * 100;
                        String category = "";


                        return new TestInfoDTO(testId, Double.parseDouble(decimalFormat.format(average)), Double.parseDouble(decimalFormat.format(averagePercentage)), numberOfCandidates,
                                testTitle, numberOfQuestions, numberOfFlags, totalCandidatesMarks ,category);
                    })
                    .filter(Objects::nonNull) //Remove null results
                    .sorted(Comparator.comparingDouble(TestInfoDTO::getTestAverage))
                    .toList();
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        assert testAverages != null;
        try {
            if (!testAverages.isEmpty()) {
                TestInfoDTO hardestTest = testAverages.getFirst();
                TestInfoDTO easiestTest = testAverages.getLast();
                hardestTest.setCategory("Most Challenging");
                easiestTest.setCategory("Easiest");
            }

            return testAverages;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Calculates and updates assessment test averages.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     */
    @Override
    public void getAssessmentTestAverages(String organizationId, String assessmentId){
        String assessmentPk = KeyBuilder.assessmentPK(organizationId, assessmentId);
        String assessmentSK = KeyBuilder.assessmentSK(organizationId, assessmentId);

        Assessment assessment = null;
        try {
            Key assessmentKey = Key.builder().partitionValue(assessmentPk).sortValue(assessmentSK).build();
            assessment = assessmentRepository.getAssessment(assessmentKey);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        try {
            assert assessment != null;
            List<String> assessmentTestIds = assessment.getTestsIds();
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            for(String assessmentTestId : assessmentTestIds){
                Key key = Key.builder().partitionValue(assessmentPk).sortValue("TEST#"+assessmentTestId+CANDIDATE_EMAIL_PREFIX).build();
                String assessmentTestSK = KeyBuilder.assessmentTestSK(assessmentId, assessmentTestId);
                Key assessmentTestKey = Key.builder().partitionValue(assessmentPk).sortValue(assessmentTestSK).build();
                AssessmentTest assessmentTest = assessmentTestRepository.getAssessmentTest(assessmentTestKey);
                List<CandidateTest> candidateTests = candidateTestRepository.getCandidateTests(key);
                List<CandidateTest> answeredCandidateTests = new ArrayList<>();
                double totalCandidateMarks =0;
                for(CandidateTest candidateTest: candidateTests){
                    if (candidateTest.getTotalScore() == 0) {
                        continue;
                    }
                    totalCandidateMarks += candidateTest.getCandidateMarks();
                    answeredCandidateTests.add(candidateTest);

                }
                double average = 0.0;
                double averagePercentageScore = 0.0;
                if(answeredCandidateTests.size() > 0){
                    average =  totalCandidateMarks/answeredCandidateTests.size();
                }
                if(assessmentTest.getTotalScore() > 0){
                    averagePercentageScore = (average/assessmentTest.getTotalScore()) * 100;
                }

                assessmentTest.setAverageScore(Double.parseDouble(decimalFormat.format(average)));
                assessmentTest.setAveragePercentageScore(Double.parseDouble(decimalFormat.format(averagePercentageScore)));
                assessmentTest.setNumberOfTimesAnswered(answeredCandidateTests.size());
                assessmentTestRepository.updateAssessmentTest(assessmentTest);

            }
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }
    public static List<String> mergeUnique(List<String> list1, List<String> list2) {
        // Use a HashSet to ensure unique elements
        Set<String> uniqueSet = new HashSet<>(list1);
        uniqueSet.addAll(list2);

        // Convert the set back to a list
        return new ArrayList<>(uniqueSet);
    }
}
