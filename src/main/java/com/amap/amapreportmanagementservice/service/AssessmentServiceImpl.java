/**
 * Service implementation for handling assessment-related operations.
 */

package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.AssessmentDTO;
import com.amap.amapreportmanagementservice.dto.AverageTimesDTO;
import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.AssessmentResponseDTO;
import com.amap.amapreportmanagementservice.dto.feedback.FeedbackSurveyDTO;
import com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221.CompletionRateDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateTestDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.entity.Assessment;
import com.amap.amapreportmanagementservice.entity.CandidateAssessment;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.AssessmentRepository;
import com.amap.amapreportmanagementservice.repository.CandidateAssessmentRepository;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;

import static com.amap.amapreportmanagementservice.constants.DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX;
import static com.amap.amapreportmanagementservice.utils.Helper.averageCalculator;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "services_logger")
public class AssessmentServiceImpl implements AssessmentService {
    private final AssessmentRepository assessmentRepository;
    private final CandidateAssessmentRepository candidateRepository;

    /**
     * Retrieves and caches average completion times for a specific assessment.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     * @return Average completion times for the assessment.
     */
    @Override
    public AverageTimesDTO getAverageTimes(String organizationId, String assessmentId) {
        String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
        Key key = Key.builder().partitionValue(pk).sortValue(CANDIDATE_EMAIL_PREFIX).build();

        List<CandidateAssessment> candidateAssessments = candidateRepository.getCandidateAssessment(key);
        int totalDuration = 0;

        try {
            List<Integer> listOfTimeTaken = new ArrayList<>();
            for (CandidateAssessment candidateAssessment : candidateAssessments) {

//                ZonedDateTime startTime = ZonedDateTime.parse(candidateAssessment.getAssessmentStartTime());
//                ZonedDateTime endTime = ZonedDateTime.parse(candidateAssessment.getAssessmentEndTime());

//                Duration assessmentDuration = Duration.between(startTime, endTime);
                totalDuration += candidateAssessment.getDuration();
                listOfTimeTaken.add(candidateAssessment.getDuration());

            }
            long shortestDuration = Collections.min(listOfTimeTaken);
            long longestDuration = Collections.max(listOfTimeTaken);

            long assessmentCount = candidateAssessments.size();
            long averageDuration = totalDuration / assessmentCount;
            return new AverageTimesDTO(assessmentCount, longestDuration, shortestDuration, averageDuration);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieves and caches completion rates for a specific assessment.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     * @return Completion rates for the assessment.
     */
    @Override
    public CompletionRateDTO getCompletionRate(String organizationId, String assessmentId) {

        String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
        Key key = Key.builder().partitionValue(pk).sortValue(CANDIDATE_EMAIL_PREFIX).build();
        List<CandidateAssessment> candidateAssessments = candidateRepository.getCandidateAssessment(key);
        try {
            int completedAssessments = 0;
            int startedAssessments = 0;
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            for (CandidateAssessment candidateAssessment : candidateAssessments) {
                startedAssessments++;
                if (Objects.equals(candidateAssessment.getStatus(), "Completed")) {
                    completedAssessments++;

                }
            }
            double completedPercentage = ((double) completedAssessments / candidateAssessments.size()) * 100;
            return new CompletionRateDTO(startedAssessments, completedAssessments, Double.parseDouble(decimalFormat.format(completedPercentage)));
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Saves assessment progress and updates assessment statistics.
     *
     * @param assessmentDTO The assessment progress data.
     */
    @Override
    public void saveAssessment(AssessmentProgressDTO assessmentDTO) {
        // Build the keys
        String pk = KeyBuilder.assessmentPK(assessmentDTO.getOrganizationId(), assessmentDTO.getAssessmentId());
        String sk = KeyBuilder.assessmentSK(assessmentDTO.getOrganizationId(), assessmentDTO.getAssessmentId());
        Key assessmentKey = Key.builder().partitionValue(pk).sortValue(sk).build();
        boolean ifExist = assessmentRepository.checkIfExist(assessmentDTO.getOrganizationId(), assessmentDTO.getAssessmentId());
        int progressCount = 0;
        try {
            if (ifExist) {
                Assessment savedAssessment = assessmentRepository.getAssessment(assessmentKey);
                int numberOfCandidate = savedAssessment.getProgressCount() + 1;
                savedAssessment.setProgressCount(numberOfCandidate);
                savedAssessment.setNumberOfTakers(numberOfCandidate);
                savedAssessment.setAssessmentDuration(assessmentDTO.getAssessment().getAssessmentDuration());
                savedAssessment.setUpdatedAt(String.valueOf(LocalDateTime.now()));
                if (savedAssessment.getCreatedAt() == null) {
                    savedAssessment.setCreatedAt(String.valueOf(LocalDateTime.of(2019, 12, 30, 15, 45, 40)));
                }
                assessmentRepository.updateAssessment(savedAssessment);

                return;
            }
            // Create the entity you need to save to dynamodb
            Assessment assessment = new Assessment();

            // Set the attributes on the entity
            assessment.setPk(pk);
            assessment.setSk(sk);
            assessment.setOrganizationId(assessmentDTO.getOrganizationId());
            assessment.setAssessmentId(assessmentDTO.getAssessment().getId());
            assessment.setTitle(assessmentDTO.getAssessment().getTitle());
            assessment.setSystem(Boolean.parseBoolean(assessmentDTO.getAssessment().getSystem()));
            assessment.setAssessmentDuration(assessmentDTO.getAssessment().getAssessmentDuration());
            assessment.setInstruction(assessmentDTO.getAssessment().getInstructions());
            assessment.setCommenceDate(assessmentDTO.getCommenceDate());
            assessment.setExpireDate(assessmentDTO.getExpireDate());
            assessment.setPassMark(assessmentDTO.getPassMark());
            List<String> testIds = assessmentDTO.getAssessment().getTests().stream().map(CandidateTestDTO::getId).toList();
            assessment.setTestsIds(testIds);
            assessment.setProgressCount(progressCount + 1);
            assessment.setUpdatedAt(String.valueOf(LocalDateTime.now()));
            assessment.setCreatedAt(String.valueOf(LocalDateTime.now()));
            assessment.setNumberOfFeedbacks(0);

            assessmentRepository.saveAssessment(assessment);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Updates assessment statistics based on test results.
     *
     * @param assessmentInputDTO The assessment input data.
     */
    @Override
    public void updateAssessment(AssessmentInputDTO assessmentInputDTO) {
        String pk = KeyBuilder.assessmentPK(assessmentInputDTO.getOrganizationId(), assessmentInputDTO.getAssessmentId());
        String sk = KeyBuilder.assessmentSK(assessmentInputDTO.getOrganizationId(), assessmentInputDTO.getAssessmentId());
        Key assessmentKey = Key.builder().partitionValue(pk).sortValue(sk).build();

        String candidateAssessmentPk = KeyBuilder.candidateAssessmentPK(assessmentInputDTO.getOrganizationId(), assessmentInputDTO.getAssessmentId());
        Key candidateKey = Key.builder().partitionValue(candidateAssessmentPk).sortValue(CANDIDATE_EMAIL_PREFIX).build();
        List<CandidateAssessment> candidateAssessments = candidateRepository.getCandidateAssessment(candidateKey);
        try {
            int candidateSize = candidateAssessments.size();
            long totalDurationSeconds = candidateAssessments.stream().mapToLong(CandidateAssessment::getDuration).sum();

            double averageDurationSeconds = averageCalculator(totalDurationSeconds, candidateSize);

            Assessment savedAssessment = assessmentRepository.getAssessment(assessmentKey);
            int completedCount = savedAssessment.getCompletedCount() + 1;
            int totalAssessmentScore = assessmentInputDTO.getTestResults().stream().mapToInt(TestResultInputDTO::getTotalScore).sum();
            double totalScorePercentage = candidateAssessments.stream().mapToDouble(CandidateAssessment::getScorePercentage).sum();


            double averagePercentage = averageCalculator(totalScorePercentage, candidateSize);
            String candidateStatus= assessmentInputDTO.getStatus();
            if(candidateStatus.equals("Completed")){
                savedAssessment.setNumberOfCompletedStatus(savedAssessment.getNumberOfCompletedStatus()+1);
            }else {
                savedAssessment.setNumberOfIncompleteStatus(savedAssessment.getNumberOfIncompleteStatus()+1);
            }
            savedAssessment.setTotalScore(totalAssessmentScore);
            savedAssessment.setCompletedCount(completedCount);
            savedAssessment.setAverageTimeTaken(averageDurationSeconds);
            savedAssessment.setAveragePercentage(averagePercentage);
            savedAssessment.setAssessmentId(assessmentInputDTO.getAssessmentId());

            assessmentRepository.updateAssessment(savedAssessment);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieves the assignment title for a specific assessment.
     *
     * @param organizationId The organization identifier.
     * @param assessmentId   The assessment identifier.
     * @return The title of the assessment.
     */
    @Override
    public AssessmentResponseDTO getAssignment(String organizationId, String assessmentId) {
        try {
            String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
            Key key = Key.builder().partitionValue(pk).sortValue(pk).build();

            AssessmentResponseDTO assessmentResponseDTO = new AssessmentResponseDTO();
            Assessment assessment = assessmentRepository.getAssessment(key);

            assessmentResponseDTO.setAssessmentDuration(assessment.getAssessmentDuration());
            assessmentResponseDTO.setAveragePercentage(assessment.getAveragePercentage());
            assessmentResponseDTO.setAssessmentId(assessment.getAssessmentId());
            assessmentResponseDTO.setInstruction(assessment.getInstruction());
            assessmentResponseDTO.setProgressCount(assessment.getProgressCount());
            assessmentResponseDTO.setAverageTimeTaken(assessment.getAverageTimeTaken());
            assessmentResponseDTO.setTitle(assessment.getTitle());
            assessmentResponseDTO.setCompletedCount(assessment.getCompletedCount());
            assessmentResponseDTO.setNumberOfTakers(assessment.getNumberOfTakers());
            assessmentResponseDTO.setTestsIds(assessment.getTestsIds());

            return assessmentResponseDTO;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieves a list of assessments for a specific organization.
     *
     * @param organizationId The organization identifier.
     * @return A list of assessment DTOs for the organization.
     */
    @Override

    public List<AssessmentDTO> getOrganizationAssessments(String organizationId) {
        try {
            List<Assessment> assessments = assessmentRepository.queryUsingGSI(organizationId);
            List<AssessmentDTO> assessmentList = new ArrayList<>();
            for (Assessment assessment : assessments) {
                AssessmentDTO assessmentDTO = new AssessmentDTO();
                if (assessment.getCreatedAt() != null) {
                    assessmentDTO.setUpdatedAt(assessment.getUpdatedAt());
                    assessmentDTO.setCreatedAt(assessment.getCreatedAt());
                }
                assessmentDTO.setTitle(assessment.getTitle());
                assessmentDTO.setAssessmentId(assessment.getAssessmentId());
                assessmentDTO.setInstructions(assessment.getInstruction());
                assessmentDTO.setAverageScore(assessment.getAveragePercentage());
                assessmentDTO.setNumberOfCandidates(assessment.getProgressCount());
                assessmentDTO.setNumberOfSections(assessment.getTestsIds().size());
                assessmentDTO.setNumberOfIncomplete(assessment.getNumberOfIncompleteStatus());
                assessmentDTO.setNumberOfCompleted(assessment.getNumberOfCompletedStatus());
                Integer numberOfFeedbacks= assessment.getNumberOfFeedbacks();
                if(numberOfFeedbacks==null){
                    numberOfFeedbacks=0;
                }
                assessmentDTO.setNumberOfFeedbacks(numberOfFeedbacks);
                assessmentList.add(assessmentDTO);

                //            TODO: implement pagination
            }
            assessmentList.sort(dateComparator);
            Collections.reverse(assessmentList);
            return assessmentList;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    @Override
    public void updateAssessmentFeedback(FeedbackSurveyDTO feedbackSurvey) {
        String assessmentPK = KeyBuilder.assessmentPK(feedbackSurvey.getOrganizationId(), feedbackSurvey.getAssessmentId());
        String assessmentSK= KeyBuilder.assessmentSK(feedbackSurvey.getOrganizationId(), feedbackSurvey.getAssessmentId());

        Key assessentKey= Key.builder().partitionValue(assessmentPK).sortValue(assessmentSK).build();
        Assessment assessment= assessmentRepository.getAssessment(assessentKey);
        Integer assessmentFeedbackCount= assessment.getNumberOfFeedbacks();
        if(assessmentFeedbackCount==null){
            assessmentFeedbackCount= 0;
        }
        assessment.setNumberOfFeedbacks(assessmentFeedbackCount+ 1);
        assessmentRepository.updateAssessment(assessment);
    }

    private final Comparator<AssessmentDTO> dateComparator = Comparator.comparing(assessmentDTO -> LocalDateTime.parse(assessmentDTO.getCreatedAt()));

}