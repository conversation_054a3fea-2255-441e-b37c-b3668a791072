/**
 * Service for providing response details and metrics for assessments.
 */
package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.*;
import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.AssessmentResponseDTO;
import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.AssessmentsDetails;
import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.MissedQuestionDTO;
import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.TestInfoDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.*;
import com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221.CompletionRateDTO;
import com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221.GeneralDetailsDTO;
import com.amap.amapreportmanagementservice.dto.progress.CodeConstraintsDTO;
import com.amap.amapreportmanagementservice.dto.progress.CodeExecutionSummaryDTO;
import com.amap.amapreportmanagementservice.dto.results.CodeResultDTO;
import com.amap.amapreportmanagementservice.dto.results.CodeTemplateDTO;
import com.amap.amapreportmanagementservice.entity.*;
import com.amap.amapreportmanagementservice.exceptions.CandidateNotFoundException;
import com.amap.amapreportmanagementservice.exceptions.EntityNotFoundException;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.*;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.Key;

//import java.time.Duration;
//import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
//import java.util.concurrent.atomic.AtomicLong;

import static com.amap.amapreportmanagementservice.constants.AppConstant.assessmentNotFound;
import static com.amap.amapreportmanagementservice.constants.DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "services_logger")
public class ResponseServiceImpl implements ResponseService {
    private final AssessmentService assessmentService;
    private final CandidateAssessmentService candidateAssessmentService;
    private final TestQuestionService testQuestionService;
    private final AssessmentTestService assessmentTestService;
    private final AssessmentRepository assessmentRepository;
    private final CandidateFeedbackService candidateFeedbackService;
    private final CandidateAssessmentRepository candidateAssessmentRepository;
    private final CandidateTestRepository candidateTestRepository;
    private final AssessmentTestRepository assessmentTestRepository;
    private final CandidateQuestionRepository candidateQuestionRepository;
    private final QuestionFlaggingRepository questionFlaggingRepository;
    private final CandidateFeedbackRepository candidateFeedbackRepository;
    public static final String CANDIDATENOTNULL = "candidateAssessments must not be null";
    private static final String CANDIDATE_ASSESSMENT_DOES_NOT_EXIST = "Candidate Assessment does not exist";

    /**
     * Retrieve general details and metrics for a specific assessment.
     *
     * @param organizationId The organization ID.
     * @param assessmentId   The assessment ID.
     * @return General details and metrics for the assessment.
     * @throws EntityNotFoundException if the assessment is not found.
     */
    @Override
//    @Cacheable(value = "getGeneralDetailsCache")
    public GeneralDetailsDTO getGeneralDetails(String organizationId, String assessmentId) {
        boolean exists;
        exists = assessmentRepository.checkIfExist(organizationId, assessmentId);
        if (!exists) {
            throw new EntityNotFoundException(assessmentNotFound(assessmentId));
        }

        try {
            AverageTimesDTO averageTimes = assessmentService.getAverageTimes(organizationId, assessmentId);
            CompletionRateDTO completionRate = assessmentService.getCompletionRate(organizationId, assessmentId);
            AssessmentScoreMetricsDTO averageScore = candidateAssessmentService.averageScoreMetrics(organizationId, assessmentId);
            List<Double> percentageScoreDistribution = candidateAssessmentService.getPercentageScoreDistribution(organizationId, assessmentId);
            return new GeneralDetailsDTO(averageTimes, completionRate, averageScore, percentageScoreDistribution);
        } catch (Exception e) {
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve details of an assessment, including missed questions and test info.
     *
     * @param organizationId The organization ID.
     * @param assessmentId   The assessment ID.
     * @return Details of the assessment.
     * @throws EntityNotFoundException if the assessment is not found.
     */
    @Override
    @Cacheable(value = "getAssessmentDetailsCache")
    public AssessmentsDetails getAssessmentDetails(String organizationId, String assessmentId) {
        boolean exists;
        exists = assessmentRepository.checkIfExist(organizationId, assessmentId);
        if (!exists) {
            throw new EntityNotFoundException(assessmentNotFound(assessmentId));
        }
        try {
            List<MissedQuestionDTO> missedQuestions = testQuestionService.getMissedQuestions(organizationId, assessmentId);
            List<TestInfoDTO> testAverages = assessmentTestService.getAssessmentTestInfo(organizationId, assessmentId);
            AssessmentResponseDTO assessment = assessmentService.getAssignment(organizationId, assessmentId);
            return new AssessmentsDetails(assessment, missedQuestions, testAverages);
        } catch (EntityNotFoundException e) {
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve candidate metrics for a specific assessment.
     *
     * @param organizationId The organization ID.
     * @param assessmentId   The assessment ID.
     * @return Candidate metrics for the assessment.
     * @throws EntityNotFoundException if the assessment is not found.
     */
    @Override
    @Cacheable(value = "getCandidateMetricsCache")
    public CandidateMetricsDTO getCandidateMetrics(String organizationId, String assessmentId) {
        boolean exists;
        exists = assessmentRepository.checkIfExist(organizationId, assessmentId);
        if (!exists) {
            throw new EntityNotFoundException(assessmentNotFound(assessmentId));
        }

        try {
            List<CandidateScoreMetricsDTO> candidateScoreMetrics = candidateAssessmentService.getCandidateScoreMetrics(organizationId, assessmentId);
            return new CandidateMetricsDTO(candidateScoreMetrics);
        } catch (Exception e) {
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve comparative analysis metrics for a specific candidate's performance in an assessment.
     *
     * @param organizationId The organization ID.
     * @param assessmentId   The assessment ID.
     * @param candidateEmail The email of the candidate.
     * @param candidateId    The ID of the candidate.
     * @return Comparative analysis metrics for the candidate's performance.
     * @throws EntityNotFoundException if the assessment is not found.
     */
    @Override
    @Cacheable(value = "getComparativeAnalysisCache")
    public ComparativeTestAnalysisDTO getComparableMetrics(String organizationId, String assessmentId, String candidateEmail, String candidateId) {
        boolean exists;
        exists = assessmentRepository.checkIfExist(organizationId, assessmentId);
        if (!exists) {
            throw new EntityNotFoundException(assessmentNotFound(assessmentId));
        }
        try {
            List<ComparativeAnalysisDTO> comparativeAnalysis = candidateAssessmentService.getComparedInfo(organizationId, assessmentId, candidateEmail, candidateId);
            List<CandidateTrajectoryDTO> candidateTrajectory = candidateAssessmentService.getCandidateTrajectory(organizationId, assessmentId, candidateEmail, candidateId);
            CandidateFeedbackDTO candidateFeedback = candidateFeedbackService.getFeedback(organizationId, assessmentId, candidateId);
            return new ComparativeTestAnalysisDTO(comparativeAnalysis, candidateTrajectory, candidateFeedback);
        } catch (Exception e) {
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve comparative analysis metrics for all candidates in an assessment.
     *
     * @param organizationId The organization ID.
     * @param assessmentId   The assessment ID.
     * @return a map of each candidate in an assessment and their comparative analysis.
     * @throws EntityNotFoundException if the assessment is not found.
     */
    @Override
    @Cacheable(value = "getAllComparativeAnalysisCache")
    public Map<String, List<ComparativeTestAnalysisDTO>> getAllCandidatesComparativeAnalysis(String organizationId, String assessmentId) {
        List<CandidateAssessment> candidateAssessments = getCandidateAssessments(organizationId, assessmentId);
        if (candidateAssessments == null) {
            throw new EntityNotFoundException(String.format("Candidate Assessments for organization with id: %s and assessment with id: %s does not exist", organizationId, assessmentId));
        }

        try {
            Map<String, List<ComparativeTestAnalysisDTO>> map = new ConcurrentHashMap<>();
            Set<String> seenCandidates = ConcurrentHashMap.newKeySet();
            candidateAssessments.parallelStream().forEach(candidate -> {
                String email = candidate.getCandidateEmail();
                List<ComparativeAnalysisDTO> comparativeAnalysis = candidateAssessmentService.getComparedInfo(organizationId, assessmentId, email, candidate.getCandidateId());
                List<CandidateTrajectoryDTO> candidateTrajectory = candidateAssessmentService.getCandidateTrajectory(organizationId, assessmentId, email, candidate.getCandidateId());
                CandidateFeedbackDTO candidateFeedback = candidateFeedbackService.getFeedback(organizationId, assessmentId, candidate.getCandidateId());

                ComparativeTestAnalysisDTO comparativeTestAnalysisDTO = new ComparativeTestAnalysisDTO(comparativeAnalysis, candidateTrajectory, candidateFeedback);

                map.compute(email, (key, existingList) -> {
                    if (existingList == null) {
                        seenCandidates.add(email);
                        return new ArrayList<>(List.of(comparativeTestAnalysisDTO));
                    } else {
                        existingList.add(comparativeTestAnalysisDTO);
                        return existingList;
                    }
                });
            });

            return map;
        } catch (Exception e) {
            throw new ProcessFailedException(e.getMessage(), e);
        }
    }

    /**
     * Get all candidate assessments.
     *
     * @param organizationId The organization ID.
     * @param assessmentId   The assessment ID.
     * @return a list of candidate assessments.
     * @throws ProcessFailedException if the assessment is not found.
     */
    private List<CandidateAssessment> getCandidateAssessments(String organizationId, String assessmentId) {
        List<CandidateAssessment> candidateAssessments;
        String pk = KeyBuilder.candidateAssessmentPK(organizationId, assessmentId);
        Key key = Key.builder().partitionValue(pk).sortValue(CANDIDATE_EMAIL_PREFIX).build();
        try {
            candidateAssessments = candidateAssessmentRepository.getCandidateAssessment(key);
        } catch (Exception e) {
            throw new ProcessFailedException(e.getMessage(), e);
        }
        return candidateAssessments;
    }

    /**
     * Retrieve feedback responses for a specific assessment.
     *
     * @param organizationId The organization ID.
     * @param assessmentId   The assessment ID.
     * @return List of feedback responses for the assessment.
     */
    @Override
    @Cacheable(value = "getFeedbacks")
    public List<FeedbackResponseDTO> getFeedbacks(String organizationId, String assessmentId) {
        return candidateFeedbackService.getAllFeedbacks(organizationId, assessmentId);
    }

    /**
     * Retrieve all details and metrics for an assessment, including comparative analysis for all candidates.
     *
     * @param organizationId The organization ID.
     * @param assessmentId   The assessment ID.
     * @return All details and metrics for the assessment.
     * @throws EntityNotFoundException if the assessment is not found.
     */
    @Override
    @Cacheable(value = "getAllDetails")
    public AllDetailsDTO getAllDetails(String organizationId, String assessmentId) {
        GeneralDetailsDTO generalDetailsDTO = getGeneralDetails(organizationId, assessmentId);

        AssessmentsDetails assessmentsDetails = getAssessmentDetails(organizationId, assessmentId);
        CandidateMetricsDTO candidateMetrics = getCandidateMetrics(organizationId, assessmentId);
        List<FeedbackResponseDTO> feedbackResponseDTOS = getFeedbacks(organizationId, assessmentId);

        String pk = KeyBuilder.assessmentTestPK(organizationId, assessmentId);


        List<CandidateAssessment> candidateAssessments = candidateAssessmentRepository.getCandidateAssessment(Key.builder().
                partitionValue(pk).
                sortValue(CANDIDATE_EMAIL_PREFIX).build());
        List<ComparativeAnalysisCandidateInfo> comparativeAnalysis = new ArrayList<>();
        try {
            Objects.requireNonNull(candidateAssessments, CANDIDATENOTNULL);
            for (CandidateAssessment candidateAssessment : candidateAssessments) {
                ComparativeTestAnalysisDTO comparativeAnalysisDTO = getComparableMetrics(organizationId, assessmentId, candidateAssessment.getCandidateEmail(), candidateAssessment.getCandidateId());
                ComparativeAnalysisCandidateInfo candidateInfo = new ComparativeAnalysisCandidateInfo();

                candidateInfo.setCandidateId(candidateAssessment.getCandidateId());
                candidateInfo.setEmail(candidateAssessment.getCandidateEmail());
                candidateInfo.setCandidateComparativeAnalysis(comparativeAnalysisDTO);

                comparativeAnalysis.add(candidateInfo);
            }

            return new AllDetailsDTO(generalDetailsDTO, assessmentsDetails, candidateMetrics, feedbackResponseDTOS, comparativeAnalysis);
        } catch (Exception e) {
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve comparative analysis metrics for all candidates in an assessment.
     *
     * @param organizationId The organization ID.
     * @param assessmentId   The assessment ID.
     * @return List of comparative analysis metrics for all candidates.
     */
    @Override
    @Cacheable(value = "getAllComparativeAnalysis")
    public List<ComparedInfoDTO> getComparativeAnalysis(String organizationId, String assessmentId) {
        List<CandidateAssessment> candidateAssessments = getCandidateAssessment(organizationId, assessmentId);
        try {
            Objects.requireNonNull(candidateAssessments, CANDIDATENOTNULL);
            return candidateAssessments.stream()
                    .map(candidateAssessment -> {
                        ComparativeTestAnalysisDTO comparativeAnalysisDTO = getComparableMetrics(
                                organizationId,
                                assessmentId,
                                candidateAssessment.getCandidateEmail(),
                                candidateAssessment.getCandidateId()
                        );

                        ComparedInfoDTO candidateInfo = new ComparedInfoDTO();
                        candidateInfo.setCandidateEmail(candidateAssessment.getCandidateEmail());
                        candidateInfo.setCandidateId(candidateAssessment.getCandidateId());
                        candidateInfo.setComparativeAnalysis(comparativeAnalysisDTO.getComparativeAnalysis());
                        return candidateInfo;
                    })
                    .toList();
        } catch (Exception e) {
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve candidate trajectory information for all candidates in an assessment.
     *
     * @param organizationId The organization ID.
     * @param assessmentId   The assessment ID.
     * @return List of candidate trajectory information for all candidates.
     */
    @Override
    @Cacheable(value = "getAllTrajectories")
    public List<CandidateTrajectoryInfoDTO> getCandidateTrajectory(String organizationId, String assessmentId) {
        List<CandidateAssessment> candidateAssessments = getCandidateAssessment(organizationId, assessmentId);
        List<CandidateTrajectoryInfoDTO> candidateTrajectories = new ArrayList<>();
        try {
            Objects.requireNonNull(candidateAssessments, CANDIDATENOTNULL);
            for (CandidateAssessment candidateAssessment : candidateAssessments) {
                ComparativeTestAnalysisDTO comparativeAnalysisDTO = getComparableMetrics(organizationId, assessmentId, candidateAssessment.getCandidateEmail(), candidateAssessment.getCandidateId());
                CandidateTrajectoryInfoDTO candidateInfo = new CandidateTrajectoryInfoDTO();

                candidateInfo.setCandidateEmail(candidateAssessment.getCandidateEmail());
                candidateInfo.setCandidateId(candidateAssessment.getCandidateId());
                candidateInfo.setCandidateTrajectory(comparativeAnalysisDTO.getCandidateTrajectory());
                candidateTrajectories.add(candidateInfo);
            }
        } catch (Exception e) {
            throw new ProcessFailedException(e.getMessage());
        }
        return candidateTrajectories;
    }

    private List<CandidateAssessment> getCandidateAssessment(String organizationId, String assessmentId) {
        String pk = KeyBuilder.assessmentTestPK(organizationId, assessmentId);
        Key key = Key.builder().
                partitionValue(pk).
                sortValue(CANDIDATE_EMAIL_PREFIX).build();

        return candidateAssessmentRepository.getCandidateAssessment(key);
    }

    /**
     * Retrieve a list of assessments for a specific organization.
     *
     * @param organizationId The organization ID.
     * @return List of assessments for the organization.
     */
    @Override
    @Cacheable(value = "getOrganizationAssignmentCache")
    public List<AssessmentDTO> getOrganizationAssessments(String organizationId) {
        return assessmentService.getOrganizationAssessments(organizationId);
    }

    @Override
    public AssessmentResultsDTO getCandidateResults(String organizationId, String assessmentId, String candidateId, String candidateEmail) {
        AssessmentResultsDTO assessmentResultsDTO = new AssessmentResultsDTO();

        String pk = KeyBuilder.candidateAssessmentPK(organizationId, assessmentId);
        String candidateAssessmentSK = KeyBuilder.candidateAssessmentSK(candidateEmail, candidateId);

        String assessmentPk = KeyBuilder.assessmentPK(organizationId, assessmentId);
        String assessmentSk = KeyBuilder.assessmentSK(organizationId, assessmentId);

        Key candidateKey = Key.builder().partitionValue(pk).sortValue(candidateAssessmentSK).build();
        CandidateAssessment candidateAssessment = candidateAssessmentRepository.getSpecificCandidateAssessment(candidateKey);
        if (candidateAssessment == null) {
            throw new CandidateNotFoundException(CANDIDATE_ASSESSMENT_DOES_NOT_EXIST);
        }
        
        // Update the isAnswerCorrect field for all question results
        candidateAssessment = candidateAssessmentRepository.updateAnswerCorrectStatus(candidateAssessment);
        
        try {

            Assessment assessment = assessmentRepository.getAssessment(Key.builder().partitionValue(assessmentPk).sortValue(assessmentSk).build());
            double candPercentage = (double) candidateAssessment.getCandidateMarks() / assessment.getTotalScore() * 100;

            assessmentResultsDTO.setAssessmentTitle(assessment.getTitle());
//            Instant start = Instant.parse(candidateAssessment.getAssessmentStartTime());
//            Instant stop = Instant.parse(candidateAssessment.getAssessmentEndTime());
            assessmentResultsDTO.setCamerashotsInterval(candidateAssessment.getCamerashotsInterval());
            assessmentResultsDTO.setScreenshotsInterval(candidateAssessment.getScreenshotsInterval());
            assessmentResultsDTO.setAssessmentTakerShotCount(candidateAssessment.getAssessmentTakerShotCount());
            assessmentResultsDTO.setAssessmentTakerViolationShotCount(candidateAssessment.getAssessmentTakerViolationShotCount());
            assessmentResultsDTO.setAssessmentWindowViolationCount(candidateAssessment.getAssessmentWindowViolationCount());
            assessmentResultsDTO.setWindowShotCount(candidateAssessment.getWindowShotCount());
            assessmentResultsDTO.setWindowViolationShotCount(candidateAssessment.getWindowViolationShotCount());
            assessmentResultsDTO.setAssessmentWindowViolationDuration(candidateAssessment.getAssessmentWindowViolationDuration());
//            assessmentResultsDTO.setAssessmentTimeTaken(Duration.between(start, stop).getSeconds());
            assessmentResultsDTO.setAssessmentTimeTaken(candidateAssessment.getDuration());
            assessmentResultsDTO.setAssessmentOverallScore(assessment.getTotalScore());
            assessmentResultsDTO.setAssessmentCandidatePercentage(Double.parseDouble(String.format("%.2f", candPercentage)));
            assessmentResultsDTO.setAssessmentCandidateScore(candidateAssessment.getCandidateMarks());
            assessmentResultsDTO.setStatus(candidateAssessment.getStatus());
            assessmentResultsDTO.setAssessmentEndTime(candidateAssessment.getAssessmentEndTime());
            assessmentResultsDTO.setExpireDate(assessment.getExpireDate());
            assessmentResultsDTO.setCommenceDate(assessment.getCommenceDate());
            assessmentResultsDTO.setAssessmentStartTime(candidateAssessment.getAssessmentStartTime());
            assessmentResultsDTO.setProctor(candidateAssessment.getProctor());
            assessmentResultsDTO.setProctorLevel(candidateAssessment.getProctorLevel());
            assessmentResultsDTO.setIntegrityScore(candidateAssessment.getIntegrityScore());
            List<TestResultsDTO> testResultsDTOS = assessment.getTestsIds().stream()
                    .map(testId -> {
                        String candidateTestSk = KeyBuilder.candidateTestSK(testId, candidateEmail, candidateId);
                        CandidateTest candidateTest = candidateTestRepository.getSpecificCandidateTest(Key.builder()
                                .partitionValue(pk)
                                .sortValue(candidateTestSk)
                                .build());
                        String assessmentTestSk = KeyBuilder.assessmentTestSK(assessmentId, testId);
                        Key key = Key.builder().partitionValue(pk).sortValue(assessmentTestSk).build();
                        AssessmentTest assessmentTest = assessmentTestRepository.getAssessmentTest(key);

                        String questionPK = KeyBuilder.candidateQuestionPK(organizationId, assessmentId, testId);
                        List<CandidateQuestionResult> candidateQuestionResults = new ArrayList<>();

                        if (candidateTest.getQuestionIDs() == null) {

                            assessmentTest.getQuestionIds().forEach(questionId -> {
                                String questionSK = KeyBuilder.candidateQuestionSK(questionId, candidateEmail, candidateId);
                                CandidateQuestionResult candidateQuestionResult = candidateQuestionRepository.getCandidateQuestion(
                                        Key.builder().partitionValue(questionPK).sortValue(questionSK).build()
                                );

                                candidateQuestionResults.add(candidateQuestionResult);


                            });
                        } else {

                            candidateTest.getQuestionIDs().forEach(questionId -> {
                                String questionSK = KeyBuilder.candidateQuestionSK(questionId, candidateEmail, candidateId);
                                CandidateQuestionResult candidateQuestionResult = candidateQuestionRepository.getCandidateQuestion(
                                        Key.builder().partitionValue(questionPK).sortValue(questionSK).build()
                                );

                                candidateQuestionResults.add(candidateQuestionResult);


                            });
                        }
                        TestResultsDTO testResultsDTO = TestResultsDTO.builder()
                                .candidateScore(Double.parseDouble(String.format("%.2f", candidateTest.getCandidateMarks())))
                                .name(assessmentTest.getTitle())
                                .overallScore(candidateTest.getTotalScore())
                                .percentage(Double.parseDouble(String.format("%.2f", candidateTest.getScorePercentage())))
                                .testTime(candidateTest.getTimeTaken())
                                .totalNumQuestions(assessmentTest.getQuestionIds().size())
                                .numberOfQuestionsAnswered(candidateTest.getNumberOfQuestionsAnswered())
                                .numberOfQuestionsPassed(candidateTest.getNumberOfQuestionsPassed())
                                .numberOfQuestionsFailed(candidateTest.getNumberOfQuestionsFailed())
                                .status(candidateTest.getStatus())
                                .testTakerShotCount(candidateTest.getTestTakerShotCount())
                                .testWindowViolationCount(candidateTest.getTestWindowViolationCount())
                                .testWindowShotCount(candidateTest.getTestWindowShotCount())
                                .testTakerShotCount(candidateTest.getTestTakerShotCount())
                                .testWindowViolationShotCount(candidateTest.getTestWindowViolationShotCount())
                                .testTakerViolationShotCount(candidateTest.getTestTakerViolationShotCount())
                                .questionResults(candidateQuestionResults)
                                .passStatus(candidateTest.getPassStatus())
                                .passage(candidateTest.getPassage())
                                .domain(candidateTest.getDomain())
                                .build();
                        return testResultsDTO;
                    }).toList();

            assessmentResultsDTO.setTestResults(testResultsDTOS);

            CandidateFeedbackDTO candidateFeedbackDTO= candidateFeedbackService.getFeedback(organizationId, assessmentId, candidateId);

            if(candidateFeedbackDTO!=null){

                List<Feedback> feedbackList = candidateFeedbackDTO.getFeedbacks();

                Feedback overallRating = feedbackList.get(0);
                Feedback testRating = feedbackList.get(1);
                Feedback comment = feedbackList.get(2);

                FeedbackResponseDTO feedbackResponseDTO= new FeedbackResponseDTO();
                feedbackResponseDTO.setCandidateEmail(candidateEmail);
                feedbackResponseDTO.setComment(comment.getQuestionAnswer());
                feedbackResponseDTO.setOverallRating(Double.parseDouble(overallRating.getQuestionAnswer()));
                feedbackResponseDTO.setTestRating(Double.parseDouble(testRating.getQuestionAnswer()));

                assessmentResultsDTO.setFeedback(feedbackResponseDTO);
            }

            return assessmentResultsDTO;
        } catch (Exception e) {
            throw new ProcessFailedException(e.getMessage());
        }

    }

    @Override
    public List<CandidateAssessment> getCandidatesResults(String organizationId, String assessmentId) {

        List<CandidateAssessment> candidateAssessments = getCandidateAssessment(organizationId, assessmentId);
        if (candidateAssessments.isEmpty()) {
            throw new EntityNotFoundException("Assessment Candidates Record not found");
        }

        return candidateAssessments;

    }

    @Override
    public List<QuestionFlagging> getFlaggedQuestionReasons(String organizationId, String assessmentId, String questionId) {
        try {
            String pk = KeyBuilder.questionFlaggingPK(organizationId, assessmentId);
            String sk = KeyBuilder.questionFlaggingSK(questionId);

            Key key = Key.builder().partitionValue(pk).sortValue(sk).build();

            return questionFlaggingRepository.getQuestionFlaggings(key);
        } catch (Exception e) {
            throw new ProcessFailedException(e.getMessage());
        }
    }


}
