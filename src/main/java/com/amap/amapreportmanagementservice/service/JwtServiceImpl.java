package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jose.crypto.MACVerifier;
import com.nimbusds.jose.jwk.source.ImmutableSecret;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.BadJOSEException;
import com.nimbusds.jose.proc.JWSKeySelector;
import com.nimbusds.jose.proc.JWSVerificationKeySelector;
import com.nimbusds.jose.proc.SimpleSecurityContext;
import com.nimbusds.jwt.JWTClaimNames;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import com.nimbusds.jwt.proc.ConfigurableJWTProcessor;
import com.nimbusds.jwt.proc.DefaultJWTClaimsVerifier;
import com.nimbusds.jwt.proc.DefaultJWTProcessor;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.text.ParseException;
import java.util.*;

@Service
public class JwtServiceImpl implements JwtService {
    private final String secretKey = System.getenv("JWT_SECRET");
    private static final String USER_ID = "userId";
    private final String expiration = System.getenv("EXPIRATION");

    @Override
    public String generateToken() throws JOSEException {
        String[] permissions = {"admin"};

        // Create JWT claims
        JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                .claim(USER_ID, "104d9d8e-8507-4813-8c43-65055f1fe7f8")
                .claim("email", "<EMAIL>")
                .claim("role", "Super Admin")
                .claim("organizationId", "1bf5694a-8452-4811-818f-dc6293f634ad")
                .claim("permissions", permissions)
                .issueTime(new Date())
                .expirationTime(new Date(System.currentTimeMillis() + Long.parseLong(expiration)))
                .build();

        // Create HMAC signer with the secret key
        JWSSigner signer = new MACSigner(secretKey);

        // Prepare the JWT with the claims and sign it
        SignedJWT signedJWT = new SignedJWT(new JWSHeader(JWSAlgorithm.HS256), claimsSet);
        signedJWT.sign(signer);

        // Serialize the JWT to a string
        return signedJWT.serialize();
    }

    private ConfigurableJWTProcessor<SimpleSecurityContext> jwtProcessor() {
        ConfigurableJWTProcessor<SimpleSecurityContext> jwtProcessor = new DefaultJWTProcessor<>();
        JWKSource<SimpleSecurityContext> jweKeySource = new ImmutableSecret<>(secretKey.getBytes());
        JWSKeySelector<SimpleSecurityContext> keySelector = new JWSVerificationKeySelector<>(
                JWSAlgorithm.HS256,
                jweKeySource);
        jwtProcessor.setJWSKeySelector(keySelector);
        // Set the required JWT claims for access tokens
        jwtProcessor.setJWTClaimsSetVerifier(new DefaultJWTClaimsVerifier<>(
                new JWTClaimsSet.Builder().build(),
                new HashSet<>(Arrays.asList(
                        JWTClaimNames.ISSUED_AT,
                        JWTClaimNames.EXPIRATION_TIME,
                        "data"))));
        return jwtProcessor;
    }


    @Override
    public String extractUserName(String token) throws BadJOSEException, ParseException, JOSEException {
        JWTClaimsSet claims = jwtProcessor().process(token, null);
        return claims.getStringClaim("data");
    }
    @Override
    public boolean isTokenValid(String token) throws JOSEException, ParseException {
        // Create HMAC verifier with the secret key
        JWSVerifier verifier = new MACVerifier(secretKey);

        // Parse the token
        SignedJWT signedJWT = SignedJWT.parse(token);

        // Verify the signature
        return signedJWT.verify(verifier);
    }

    @Override
    public Object extract(String token, String nameOfClaim) {
        try {
            JWTClaimsSet claims = extractAllClaims(token);
            return claims.getClaim(nameOfClaim);
        } catch (ParseException | BadJOSEException | JOSEException exception) {
            return null;
        }
    }

    @Override
    public String extractString(String token, String nameOfClaim) {
        try {
            JWTClaimsSet claims = extractAllClaims(token);
            return claims.getStringClaim(nameOfClaim);
        } catch (ParseException | BadJOSEException | JOSEException exception) {
            return null;
        }
    }

    private JWTClaimsSet extractAllClaims(String token) throws BadJOSEException, ParseException, JOSEException {
        JWTClaimsSet claims = jwtProcessor().process(token, null);

        // Check for encrypted data in the "data" claim and decrypt it if present
        if (claims.getClaim("data") != null) {
            String encryptedPayload = claims.getStringClaim("data");
            Map<String, Object> decryptedData = decryptPayload(encryptedPayload);

            // Merge the decrypted payload with existing claims
            JWTClaimsSet.Builder builder = new JWTClaimsSet.Builder(claims);
            decryptedData.forEach(builder::claim);
            return builder.build();
        }

        return claims;

    }
    /**
     * Decrypts the payload from the provided unencrypted JWT payload.
     *
     * @param unencryptedPayload The unencrypted JWT payload.
     * @return A map containing the decrypted payload merged with additional payloads.
     */
    public Map decryptPayload(String unencryptedPayload) {
        try {
            // Retrieve encryption key and algorithm from environment variables
            String decryptionKey = System.getenv("JWT_ENCRYPTION_KEY");
            String algo = System.getenv("JWT_ENCRYPTION_ALGO");

            if (decryptionKey == null || algo == null) {
                throw new IllegalStateException("Encryption key or algorithm is not configured in environment variables.");
            }

            // Split the data into IV and encrypted parts
            String[] parts = unencryptedPayload.split(":");
            byte[] iv = Base64.getDecoder().decode(parts[0]);
            byte[] encrypted = Base64.getDecoder().decode(parts[1]);

            // Initialize cipher for decryption
            Cipher cipher = Cipher.getInstance(algo);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
            SecretKeySpec secretKeySpec = new SecretKeySpec(Base64.getDecoder().decode(decryptionKey), "AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

            // Decrypt and parse JSON payload
            byte[] decryptedBytes = cipher.doFinal(encrypted);
            String decryptedPayload = new String(decryptedBytes);

            // Convert decrypted payload to a map
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(decryptedPayload, HashMap.class);

        } catch (Exception exception) {
            throw new ProcessFailedException(exception.getMessage());
        }
    }
}