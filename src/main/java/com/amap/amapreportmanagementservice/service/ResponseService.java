package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.*;
import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.AssessmentsDetails;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.AssessmentResultsDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateMetricsDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.ComparativeTestAnalysisDTO;
import com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221.GeneralDetailsDTO;
import com.amap.amapreportmanagementservice.entity.CandidateAssessment;
import com.amap.amapreportmanagementservice.entity.QuestionFlagging;

import java.util.List;
import java.util.Map;

public interface ResponseService {
    GeneralDetailsDTO getGeneralDetails(String organizationId, String assessmentId);
    AssessmentsDetails getAssessmentDetails(String organizationId, String assessmentId);
    CandidateMetricsDTO getCandidateMetrics(String organizationId, String assessmentId);
    ComparativeTestAnalysisDTO getComparableMetrics(String organizationId, String assessmentId,
                                                    String candidateEmail, String candidateId);
    Map<String, List<ComparativeTestAnalysisDTO>> getAllCandidatesComparativeAnalysis(String organizationId, String assessmentId);
    List<FeedbackResponseDTO> getFeedbacks(String organizationId, String assessmentId);
    AllDetailsDTO getAllDetails(String organizationId, String assessmentId);
    List<ComparedInfoDTO> getComparativeAnalysis(String organizationId, String assessmentId);
    List<CandidateTrajectoryInfoDTO> getCandidateTrajectory(String organizationId, String assessmentId);
    List<AssessmentDTO> getOrganizationAssessments(String organizationId);
    AssessmentResultsDTO getCandidateResults(String organizationId, String assessmentId, String candidateId, String candidateEmail);
    List<CandidateAssessment> getCandidatesResults(String organizationId, String assessmentId);
    List<QuestionFlagging> getFlaggedQuestionReasons(String organizationId, String assessmentId, String questionId);

}
