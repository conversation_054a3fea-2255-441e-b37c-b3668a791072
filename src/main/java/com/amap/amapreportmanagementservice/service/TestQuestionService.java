package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.MissedQuestionDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;

import java.util.List;

public interface TestQuestionService {

    void  saveTestQuestions(AssessmentProgressDTO assessmentProgressDTO);

    void updateTestQuestions(AssessmentInputDTO assessmentInputDTO);
    List<MissedQuestionDTO> getMissedQuestions(String organizationId, String assessmentId);
}
