package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.entity.CandidateAssessment;
import com.amap.amapreportmanagementservice.entity.WebhookReportJob;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.WebhookReportJobRepository;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.List;

import static com.amap.amapreportmanagementservice.constants.AppConstant.MAXIMUMWEBHOOKTRAILS;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "services_logger")
public class WebhookReportJobServiceImpl implements WebhookReportJobService{
    private final WebhookReportJobRepository webhookReportJobRepository;

    /**
     * Save job to report to third party platform
     *
     * @param candidateAssessment The Candidate Assessment data
     */
    @Override
    public void saveWebhookReportJob(CandidateAssessment candidateAssessment) {
        try {
            String pk= KeyBuilder.webhookReportPK(candidateAssessment.getOrganizationId());
            String sk= KeyBuilder.webhookReportSK(candidateAssessment.getCandidateId());
            WebhookReportJob webhookReportJob= new WebhookReportJob();

            webhookReportJob.setPk(pk);
            webhookReportJob.setSk(sk);
            webhookReportJob.setOrganizationId(candidateAssessment.getOrganizationId());
            webhookReportJob.setEmail(candidateAssessment.getCandidateEmail());
            webhookReportJob.setCandidateId(candidateAssessment.getCandidateId());
            webhookReportJob.setAssessmentId(candidateAssessment.getAssessmentId());
            webhookReportJob.setReportCallbackURL(candidateAssessment.getReportCallbackURL());
            webhookReportJob.setTrails(MAXIMUMWEBHOOKTRAILS);

            webhookReportJobRepository.saveWebhookReportJob(webhookReportJob);
        } catch(Exception exception){
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

    /**
     * Reduce number of trail by one
     *
     * @param webhookReportJob The job date
     */
    @Override
    public void reduceWebhookReportJobTrial( WebhookReportJob webhookReportJob) {
        try {

            int currentTrail= webhookReportJob.getTrails();
            currentTrail-=1;
            webhookReportJob.setTrails(currentTrail);

            webhookReportJobRepository.updateWebhookReportJob(webhookReportJob);
        } catch(Exception exception){
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

    /**
     * Delete Job data
     *
     * @param webhookReportJob The job data
     */
    @Override
    public void deleteWebhookReportJob(WebhookReportJob webhookReportJob) {
        try {
            Key key = Key.builder()
                    .partitionValue(webhookReportJob.getPk())
                    .sortValue(webhookReportJob.getSk())
                    .build();
            webhookReportJobRepository.deleteWebhookReportJob(key);
        } catch(Exception exception){
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

    /**
     *
     * @return The list of jobs
     */
    @Override
    public List<WebhookReportJob> getWebhookReportJobs() {
        try {
            return webhookReportJobRepository.getWebhookReportJobs();
        } catch (Exception exception){
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }


}
