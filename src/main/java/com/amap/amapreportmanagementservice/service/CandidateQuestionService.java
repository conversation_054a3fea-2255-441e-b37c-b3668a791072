package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.FlaggedInputDTO;

public interface CandidateQuestionService {

    void saveCandidateQuestionResult(AssessmentProgressDTO assessmentProgressDTO);

    void updateCandidateQuestionResult(AssessmentInputDTO assessmentInputDTO);

    void updateFlaggedQuestion(FlaggedInputDTO flaggedInputDTO);

    void updateUnflaggedQuestion(FlaggedInputDTO flaggedInputDTO);
}
