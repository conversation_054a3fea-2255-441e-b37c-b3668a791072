package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.AssessmentResultsDTO;
import com.amap.amapreportmanagementservice.entity.WebhookReportJob;
import com.amap.amapreportmanagementservice.exceptions.CandidateNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "services_logger")
public class ScheduleTasksImpl implements  ScheduledTasks{
    private final RestTemplate restTemplate;
    private final WebhookReportJobService webhookReportJobService;
    private final ResponseService responseService;

    /**
     *
     */
    @Override
    @Scheduled(cron = "0 0/2 * * * ?")
    public void autoDispatchReport() {

        try {

            List<WebhookReportJob> webhookReportJobs= webhookReportJobService.getWebhookReportJobs();

            for (WebhookReportJob webhookReportJob : webhookReportJobs){
                try {
                    AssessmentResultsDTO candidateResult= responseService.getCandidateResults(webhookReportJob.getOrganizationId(),webhookReportJob.getAssessmentId(), webhookReportJob.getCandidateId(), webhookReportJob.getEmail());

                    attemptToReport(webhookReportJob, candidateResult);

                }catch (Exception exception){
                    if(exception instanceof CandidateNotFoundException){
//                        Delete Job when candidate is not found
                        webhookReportJobService.deleteWebhookReportJob(webhookReportJob);
                    }
                    log.error(exception.getMessage());
                }
            }

        }catch (Exception exception){
            log.error(exception.getMessage());
        }
    }

    /**
     *
     */
    private void attemptToReport(WebhookReportJob webhookReportJob, AssessmentResultsDTO result) {
        try {
            restTemplate.postForObject(webhookReportJob.getReportCallbackURL(), result, String.class);
            webhookReportJobService.deleteWebhookReportJob(webhookReportJob);
        } catch (Exception exception) {
            if(webhookReportJob.getTrails()>1){
                webhookReportJobService.reduceWebhookReportJobTrial(webhookReportJob);
                return;
            }

            webhookReportJobService.deleteWebhookReportJob(webhookReportJob);

        }
    }


}
