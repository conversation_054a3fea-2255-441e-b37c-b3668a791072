package com.amap.amapreportmanagementservice.service;



import com.amap.amapreportmanagementservice.dto.results.FlaggedInputDTO;
import com.amap.amapreportmanagementservice.entity.QuestionFlagging;

import java.util.List;

public interface QuestionFlaggingService {

    void saveQuestionFlagging(FlaggedInputDTO flaggedInputDTO);

    List<QuestionFlagging> getQuestionFlaggingsByQuestion(String organizationId, String assessmentId, String questionId);

}
