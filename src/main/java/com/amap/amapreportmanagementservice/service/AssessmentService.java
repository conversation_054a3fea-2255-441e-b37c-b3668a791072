package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.AssessmentDTO;
import com.amap.amapreportmanagementservice.dto.AverageTimesDTO;
import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.AssessmentResponseDTO;
import com.amap.amapreportmanagementservice.dto.feedback.FeedbackSurveyDTO;
import com.amap.amapreportmanagementservice.dto.page_response_dtos.amap221.CompletionRateDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;

import java.util.List;

public interface AssessmentService {

    AverageTimesDTO getAverageTimes(String organizationId, String assessmentId);

    CompletionRateDTO getCompletionRate(String organizationId, String assessmentId);

    void saveAssessment(AssessmentProgressDTO assessmentDTO);

    void updateAssessment(AssessmentInputDTO assessmentInputDTO);

    AssessmentResponseDTO getAssignment(String organizationId, String assessmentId);
    List<AssessmentDTO> getOrganizationAssessments(String organizationId);

    void updateAssessmentFeedback(FeedbackSurveyDTO feedbackSurvey);

}
