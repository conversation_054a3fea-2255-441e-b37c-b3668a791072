/**
 * The AssessmentController class provides REST endpoints for retrieving assessment-related data.
 * It handles requests related to assessments, assessment details, candidate metrics, comparative analysis,
 *  feedback, and more.
 *
 * @RestController Indicates that this class is a Spring MVC Controller and handles HTTP requests.
 * @RequiredArgsConstructor Lombok's annotation to automatically generate a constructor with required fields.
 *
 * @since 1.0
 * @version 1.0
 */
package com.amap.amapreportmanagementservice.controller;

import com.amap.amapreportmanagementservice.dto.ResponseHandler;
import com.amap.amapreportmanagementservice.service.ResponseService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class AssessmentController {
    private final ResponseService responseService;

     /**
     * Retrieve general details of an assessment.
     *
     * @param organizationId The organization's unique identifier.
     * @param assessmentId   The assessment's unique identifier.
     * @return ResponseEntity with the general details.
     */
    @GetMapping("/organizations/{organizationId}/assessments/{assessmentId}/basic-metrics/general-details")
    public ResponseEntity<Object> getGeneralDetails(@PathVariable("organizationId") String organizationId,
                                                    @PathVariable("assessmentId") String assessmentId) {

        return ResponseHandler.successResponse
                (HttpStatus.OK, responseService.getGeneralDetails(organizationId, assessmentId));
    }

    /**
     * Retrieve detailed information about an assessment.
     *
     * @param organizationId The organization's unique identifier.
     * @param assessmentId   The assessment's unique identifier.
     * @return ResponseEntity with the assessment details.
     */
    @GetMapping("/organizations/{organizationId}/assessments/{assessmentId}/assessment-details")
    public ResponseEntity<Object> getAssessmentDetails(@PathVariable("organizationId") String organizationId,
                                                       @PathVariable("assessmentId") String assessmentId) {

        return ResponseHandler.successResponse
                (HttpStatus.OK, responseService.getAssessmentDetails(organizationId, assessmentId));
    }

    /**
     * Retrieve detailed information about an assessment test.
     *
     * @param organizationId The organization's unique identifier.
     * @param assessmentId   The assessment's unique identifier.
     * @return ResponseEntity with the assessment test's details.
     */
    @GetMapping("/organizations/{organizationId}/assessments/{assessmentId}/candidate-metrics")
    public ResponseEntity<Object> getAssessmentTestDetails(@PathVariable("organizationId") String organizationId,
                                                           @PathVariable("assessmentId") String assessmentId) {
        return ResponseHandler.successResponse
                (HttpStatus.OK, responseService.getCandidateMetrics(organizationId, assessmentId));
    }

    /**
     * Retrieve comparative information about an assessment.
     *
     * @param organizationId The organization's unique identifier.
     * @param assessmentId   The assessment's unique identifier.
     * @return ResponseEntity with the assessment's comparative information.
     */
    @GetMapping("/organizations/{organizationId}/assessments/{assessmentId}/comparative-analysis")
    public ResponseEntity<Object> getComparativeInformation(@PathVariable("organizationId") String organizationId,
                                                            @PathVariable("assessmentId") String assessmentId,
                                                            @RequestParam("candidateEmail") String candidateEmail,
                                                            @RequestParam("candidateId") String candidateId) {
        return ResponseHandler.successResponse
                (HttpStatus.OK, responseService.getComparableMetrics(organizationId, assessmentId, candidateEmail, candidateId));
    }

    /**
     * Retrieve feedback about an assessment.
     *
     * @param organizationId The organization's unique identifier.
     * @param assessmentId   The assessment's unique identifier.
     * @return ResponseEntity with the assessment feedback.
     */
    @GetMapping("/organizations/{organizationId}/assessments/{assessmentId}/feedbacks")
    public ResponseEntity<Object> getFeedbacks(@PathVariable("organizationId") String organizationId,
                                               @PathVariable("assessmentId") String assessmentId
    ) {
        return ResponseHandler.successResponse
                (HttpStatus.OK, responseService.getFeedbacks(organizationId, assessmentId));
    }

    /**
     * Retrieve all details for an assessment.
     *
     * @param organizationId The organization's unique identifier.
     * @param assessmentId   The assessment's unique identifier.
     * @return ResponseEntity with the assessment's details.
     */
    @GetMapping("/organizations/{organizationId}/assessments/{assessmentId}/all-details")
    public ResponseEntity<Object> getAllDetails(@PathVariable("organizationId") String organizationId,
                                                @PathVariable("assessmentId") String assessmentId
    ) {
        return ResponseHandler.successResponse
                (HttpStatus.OK, responseService.getAllDetails(organizationId, assessmentId));
    }

    /**
     * Retrieve comparative analysis for an assessment.
     *
     * @param organizationId The organization's unique identifier.
     * @param assessmentId   The assessment's unique identifier.
     * @return ResponseEntity with the comparative analysis details.
     */
    @GetMapping("/organizations/{organizationId}/assessments/{assessmentId}/all-comparative-analysis")
    public ResponseEntity<Object> getAllComparativeAnalysis(@PathVariable("organizationId") String organizationId,
                                                            @PathVariable("assessmentId") String assessmentId
    ) {
        return ResponseHandler.successResponse
                (HttpStatus.OK, responseService.getComparativeAnalysis(organizationId, assessmentId));
    }

     @GetMapping("/organizations/{organizationId}/assessments/{assessmentId}/all-candidates-comparative-analysis")
    public ResponseEntity<Object> getAllCandidatesComparativeAnalysis(@PathVariable("organizationId") String organizationId,
                                                            @PathVariable("assessmentId") String assessmentId
    ) {
        return ResponseHandler.successResponse
                (HttpStatus.OK, responseService.getAllCandidatesComparativeAnalysis(organizationId, assessmentId));
    }

    /**
     * Retrieve all candidate trajectories associated with an assessment.
     *
     * @param organizationId The organization's unique identifier.
     * @param assessmentId   The assessment's unique identifier.
     * @return ResponseEntity with the candidate trajectories.
     */
    @GetMapping("/organizations/{organizationId}/assessments/{assessmentId}/all-candidate-trajectories")
    public ResponseEntity<Object> getAllCandidateTrajectories(@PathVariable("organizationId") String organizationId,
                                                              @PathVariable("assessmentId") String assessmentId
    ) {
        return ResponseHandler.successResponse
                (HttpStatus.OK, responseService.getCandidateTrajectory(organizationId, assessmentId));
    }

    /**
     * Retrieve all assessments associated with an organization.
     *
     * @param organizationId The organization's unique identifier.
     * @return ResponseEntity with the organization's assessments.
     */
    @GetMapping("/organizations/{organizationId}/organization-assessments")
    public ResponseEntity<Object> getOrganizationAssessments(@PathVariable("organizationId") String organizationId
    ) {
        return ResponseHandler.successResponse(HttpStatus.OK, responseService.getOrganizationAssessments(organizationId));
    }

    @GetMapping("/organizations/{organizationId}/assessments/{assessmentId}/candidate/{candidateId}/assessment-results")
    public ResponseEntity<Object> getAssessmentResult(@PathVariable("organizationId") String organizationId,
                                                      @PathVariable String assessmentId, @PathVariable String candidateId, @RequestParam(name = "email") String email) {
        return ResponseHandler.successResponse(HttpStatus.OK, responseService.getCandidateResults(organizationId, assessmentId, candidateId, email));
    }
    @GetMapping("/organizations/{organizationId}/assessments/{assessmentId}/candidates-results")
    public ResponseEntity<Object> getCandidatesResults(@PathVariable("organizationId") String organizationId,
                                                      @PathVariable String assessmentId) {
        return ResponseHandler.successResponse(HttpStatus.OK, responseService.getCandidatesResults(organizationId, assessmentId));

    }

    @GetMapping("/organizations/{organizationId}/assessments/{assessmentId}/question/{questionId}")
    public ResponseEntity<Object> getFlaggedQuestionReasons(@PathVariable("organizationId") String organizationId,
                                                       @PathVariable String assessmentId, @PathVariable String questionId) {

        return ResponseHandler.successResponse(HttpStatus.OK, responseService.getFlaggedQuestionReasons(organizationId, assessmentId, questionId));
    }
}