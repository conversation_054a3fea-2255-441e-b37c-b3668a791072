/**
 * This package contains the controllers for the report management service.
 * These controllers handle HTTP requests and provide endpoints for interacting
 * with assessment-related data and serving the index/welcome page.
 * <p>
 * The controllers include:
 * - {@link com.amap.amapreportmanagementservice.controller.AssessmentController}: Handles assessment-related requests.
 * - {@link com.amap.amapreportmanagementservice.controller.Index}: Provides a welcome message at the root endpoint.
 *
 * @since 1.0
 * @version 1.0
 */
package com.amap.amapreportmanagementservice.controller;