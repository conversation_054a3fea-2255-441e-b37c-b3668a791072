/**
 * The Index class serves as the entry point for the report service application.
 * It provides an index endpoint to welcome users to the service.
 *
 * @RestController Indicates that this class is a Spring MVC Controller and handles HTTP requests.
 *
 * @since 1.0
 * @version 1.0
 */
package com.amap.amapreportmanagementservice.controller;

import com.amap.amapreportmanagementservice.dto.ResponseHandler;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class Index {
      /**
     * Welcome message for the report service.
     *
     * @return ResponseEntity with a welcome message and HTTP status OK.
     */
    @GetMapping
    public ResponseEntity<Object> index(){
       return ResponseHandler.successResponse(HttpStatus.OK,"Welcome to report service");
    }
}
