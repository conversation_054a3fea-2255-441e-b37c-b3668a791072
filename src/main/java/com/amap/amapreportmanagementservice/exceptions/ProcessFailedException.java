/**
 * Custom exception class for indicating a process failure.
 * This exception is typically thrown when a specific operation or process
 * fails to complete successfully for any reason.
 */

package com.amap.amapreportmanagementservice.exceptions;

import lombok.extern.slf4j.Slf4j;

@Slf4j(topic = "application_logger")
public class ProcessFailedException extends RuntimeException {

    /**
     * Constructs a new ProcessFailedException with the specified detail message.
     *
     * @param message The detail message explaining the cause of the exception.
     */
    public ProcessFailedException(String message) {
        super(message);
    }
    public ProcessFailedException(String message, Throwable cause) {
        super(message, cause);
    }
}
