/**
 * Global exception handler for handling and providing consistent error responses
 * across the entire Spring Boot application.
 */
package com.amap.amapreportmanagementservice.exceptions;

import com.amap.amapreportmanagementservice.dto.ResponseHandler;
import io.sentry.Sentry;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

import java.time.LocalDateTime;


@ControllerAdvice
public class GlobalErrorHandler {
    /**
     * Handle exceptions of type Exception and return an internal server error response.
     *
     * @param ex       The exception to handle.
     * @param request  The web request.
     * @return An HTTP response entity with a status of INTERNAL_SERVER_ERROR.
     */
    @ExceptionHandler({Exception.class})
    public final ResponseEntity<Object> handlerAllExceptions(Exception ex, WebRequest request) {

        ErrorResponse errorResponse = new ErrorResponse(LocalDateTime.now(), ex.getMessage(), request.getDescription(false));
        Sentry.captureException(ex);
        return ResponseHandler.errorResponse(HttpStatus.INTERNAL_SERVER_ERROR, errorResponse);
    }

    @ExceptionHandler(SentryException.class)
    public final ResponseEntity<Object> handleSentryExceptions(SentryException ex, WebRequest request){
        ErrorResponse errorResponse = new ErrorResponse(LocalDateTime.now(), ex.getMessage(), request.getDescription(false));
        Sentry.captureException(ex);
        return ResponseHandler.errorResponse(HttpStatus.INTERNAL_SERVER_ERROR, errorResponse);
    }
    /**
     * Handle exceptions of type EntityNotFoundException and CandidateNotFoundException
     * and return a not found error response.
     *
     * @param ex       The exception to handle.
     * @param request  The web request.
     * @return An HTTP response entity with a status of NOT_FOUND.
     */
    @ExceptionHandler({EntityNotFoundException.class, CandidateNotFoundException.class})
    public final ResponseEntity<Object> handlerAllNotFoundExceptions(Exception ex, WebRequest request) {
        ErrorResponse errorResponse = new ErrorResponse(LocalDateTime.now(), ex.getMessage(), request.getDescription(false));
        Sentry.captureException(ex);
        return ResponseHandler.errorResponse(HttpStatus.NOT_FOUND, errorResponse);
    }

    /**
     * Handle authentication-related exceptions and return an unauthorized error response.
     *
     * @param exception The authentication-related exception to handle.
     * @param request   The web request.
     * @return An HTTP response entity with a status of UNAUTHORIZED.
     */
    @ExceptionHandler({InsufficientAuthenticationException.class, JwtAuthenticationException.class, AuthenticationException.class, AccessDeniedException.class})
    public final ResponseEntity<Object> handleAuthenticationExceptions(Exception exception, WebRequest request){

        ErrorResponse errorResponse = new ErrorResponse(LocalDateTime.now(), exception.getMessage(), request.getDescription(false));
        Sentry.captureException(exception);
        return ResponseHandler.errorResponse(HttpStatus.UNAUTHORIZED, errorResponse);
    }

        /**
     * Handle exceptions related to unsupported HTTP request methods and return a bad request error response.
     *
     * @param exception The exception related to unsupported request methods.
     * @param request   The web request.
     * @return An HTTP response entity with a status of BAD_REQUEST.
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public final ResponseEntity<Object> handleBadRequestExceptions(Exception exception, WebRequest request){
        ErrorResponse errorResponse = new ErrorResponse(LocalDateTime.now(), exception.getMessage(), request.getDescription(false));
        Sentry.captureException(exception);
        return ResponseHandler.errorResponse(HttpStatus.BAD_REQUEST, errorResponse);
    }

}
