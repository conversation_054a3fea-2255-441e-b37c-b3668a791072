/**
 * Custom exception class for indicating JWT (JSON Web Token) authentication failures.
 * This exception is typically thrown when there is an issue with JWT-based authentication,
 * such as authentication failure, token expiration, or other related errors.
 * Has status code 401.
 */

package com.amap.amapreportmanagementservice.exceptions;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.UNAUTHORIZED)
@Slf4j(topic = "application_logger")
public class JwtAuthenticationException extends RuntimeException{

    /**
     * Constructs a new JwtAuthenticationException with the specified detail message.
     *
     * @param message The detail message explaining the cause of the exception.
     */
    public JwtAuthenticationException(String message){
        super(message);
    }

    /**
     * Constructs a new JwtAuthenticationException with the specified detail message and cause.
     *
     * @param message The detail message explaining the cause of the exception.
     * @param cause   The cause of the exception (e.g., a caught exception that led to this error).
     */
    public JwtAuthenticationException(String message, Throwable cause){
        super(message, cause);
    }
}