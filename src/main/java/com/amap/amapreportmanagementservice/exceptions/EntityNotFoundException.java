/**
 * Custom exception class for indicating that an entity was not found.
 * This exception is typically thrown when attempting to retrieve or operate
 * on an entity, such as a database record, that does not exist.
 * Has status code 404.
 */

package com.amap.amapreportmanagementservice.exceptions;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
@Slf4j(topic = "application_logger")
public class EntityNotFoundException extends RuntimeException {

    /**
     * Constructs a new EntityNotFoundException with the specified detail message.
     *
     * @param message The detail message explaining the cause of the exception.
     */
    public EntityNotFoundException(String message) {
        super(message);
    }
}
