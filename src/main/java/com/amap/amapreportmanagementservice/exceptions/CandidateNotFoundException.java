/**
 * Custom exception class for indicating that a candidate was not found.
 * This exception is typically thrown when attempting to retrieve or
 * operate on a candidate record that does not exist.
 * Has status code 404
 */

package com.amap.amapreportmanagementservice.exceptions;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
@Slf4j(topic = "application_logger")
public class CandidateNotFoundException extends RuntimeException {
    /**
     * Constructs a new CandidateNotFoundException with the specified detail message.
     *
     * @param message The detail message explaining the cause of the exception.
     */
    public CandidateNotFoundException(String message) {
        super(message);
    }

}
