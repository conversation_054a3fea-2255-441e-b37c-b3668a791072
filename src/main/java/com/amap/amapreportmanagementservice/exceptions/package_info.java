/**
 * This package contains custom exception classes used within the Amap Report Management Service.
 * These exceptions provide specific error information for various scenarios and are thrown when
 * particular exceptional conditions are encountered during the execution of the service.
 *
 * <p>Custom Exception Classes:</p>
 * <ul>
 *     <li>{@link com.amap.amapreportmanagementservice.exceptions.CandidateNotFoundException} - Indicates that a candidate was not found.</li>
 *     <li>{@link com.amap.amapreportmanagementservice.exceptions.EntityNotFoundException} - Indicates that an entity was not found.</li>
 *     <li>{@link com.amap.amapreportmanagementservice.exceptions.JwtAuthenticationException} - Indicates JWT authentication failures.</li>
 *     <li>{@link com.amap.amapreportmanagementservice.exceptions.ProcessFailedException} - Indicates process failures.</li>
 * </ul>
 */
package com.amap.amapreportmanagementservice.exceptions;