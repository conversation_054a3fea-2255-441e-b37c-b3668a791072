/**
 * Configuration class for setting up and providing the DynamoDB client and enhanced client for the application.
 * It initializes the DynamoDB client with the required AWS credentials and region, as well as the DynamoDB endpoint
 * URL if applicable. The enhanced client is also created using the DynamoDB client.
 *
 * @see AwsBasicCredentials
 * @see Region
 * @see DynamoDbClient
 * @see DynamoDbEnhancedClient
 * @since 1.0
 */
package com.amap.amapreportmanagementservice.config.dynamo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

import java.net.URI;


@Configuration
@RequiredArgsConstructor
@Slf4j
public class DynamoDbConfig {
    private final Environment environment;

    @Value("${dynamo.client}")
    String dynamoClient;

    /**
     * Configures and provides a DynamoDB client to interact with the DynamoDB service.
     *
     * @return A configured DynamoDB client instance.
     */
    @Bean
    @ConditionalOnProperty(prefix = "dynamo", name = "client", havingValue = "dev")
    public DynamoDbClient developmentDynamoDbClient() {
        log.info("Development dynamoDB client used");

        AwsBasicCredentials credentialsProvider = AwsBasicCredentials.create(
                environment.getRequiredProperty("AWS_ACCESS_KEY"),
                environment.getRequiredProperty("AWS_SECRET_KEY")
        );

        return DynamoDbClient.builder()
                .region(Region.of(environment.getRequiredProperty("AWS_REGION")))
                //.endpointOverride(URI.create(environment.getRequiredProperty("DYNAMODB_URL")))
                .credentialsProvider(() -> credentialsProvider).build();
    }

    @Bean
    @ConditionalOnProperty(prefix = "dynamo", name = "client", havingValue = "prod")
    public DynamoDbClient productionDynamoDbClient() {
        log.info("Production dynamoDB client used ");

        AwsBasicCredentials credentialsProvider = AwsBasicCredentials.create(
                environment.getRequiredProperty("AWS_ACCESS_KEY"),
                environment.getRequiredProperty("AWS_SECRET_KEY")
        );

        return DynamoDbClient.builder()
                .region(Region.of(environment.getRequiredProperty("AWS_REGION")))
                .credentialsProvider(() -> credentialsProvider).build();
    }


    /**
     * Configures and provides an enhanced client for DynamoDB to streamline interactions with the database.
     *
     * @return A configured DynamoDB enhanced client instance.
     */
    @Bean
    public DynamoDbEnhancedClient enhancedClient() {
        log.info("DynamoDBEnhanced client created successfully ...");

        return dynamoClient.equals("prod")
                ? DynamoDbEnhancedClient.builder().dynamoDbClient(productionDynamoDbClient()).build()
                : DynamoDbEnhancedClient.builder().dynamoDbClient(developmentDynamoDbClient()).build();
    }
}
