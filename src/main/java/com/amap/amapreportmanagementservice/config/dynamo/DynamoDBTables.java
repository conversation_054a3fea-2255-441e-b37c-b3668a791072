package com.amap.amapreportmanagementservice.config.dynamo;

import com.amap.amapreportmanagementservice.entity.*;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

@Configuration
@RequiredArgsConstructor
public class DynamoDBTables {
    private static final Logger logger = LoggerFactory.getLogger(DynamoDBTables.class);

    private final DynamoDbEnhancedClient dynamoDbEnhancedClient;
    private final Environment environment;


    /**
     * Provides the DynamoDB table name based on the configuration.
     *
     * @return The name of the DynamoDB table used in the application.
     */
    @Bean
    public String getTableName() {
        return environment.getRequiredProperty("DYNAMODB_TABLENAME");
    }

    /**
     * Configures and provides a DynamoDB table for storing report entities.
     *
     * @return A DynamoDB table for report entities.
     */
    @Bean
    public DynamoDbTable<ReportBaseEntity> reportTable() {
        TableSchema<ReportBaseEntity> reportTableSchema = TableSchema.fromBean(ReportBaseEntity.class);
        return dynamoDbEnhancedClient.table(getTableName(), reportTableSchema);
    }

    /**
     * Configures and provides a DynamoDB table for storing assessment entities.
     *
     * @return A DynamoDB table for assessment entities.
     */
    @Bean
    public DynamoDbTable<Assessment> assessmentTable() {
        TableSchema<Assessment> projectTableSchema = TableSchema.fromBean(Assessment.class);
        return dynamoDbEnhancedClient.table(getTableName(), projectTableSchema);
    }

    /**
     * Configures and provides a DynamoDB table for storing candidate assessment entities.
     *
     * @return A DynamoDB table for candidate assessment entities.
     */
    @Bean
    public DynamoDbTable<CandidateAssessment> candidateAssessmentTable() {
        TableSchema<CandidateAssessment> projectTableSchema = TableSchema.fromBean(CandidateAssessment.class);
        return dynamoDbEnhancedClient.table(getTableName(), projectTableSchema);
    }

    /**
     * Configures and provides a DynamoDB table for storing assessment test entities.
     *
     * @return A DynamoDB table for assessment test entities.
     */
    @Bean
    public DynamoDbTable<AssessmentTest> assessmentTestTable() {
        TableSchema<AssessmentTest> projectTableSchema = TableSchema.fromBean(AssessmentTest.class);
        return dynamoDbEnhancedClient.table(getTableName(), projectTableSchema);
    }

    /**
     * Configures and provides a DynamoDB table for storing candidate question result entities.
     * Uses explicit converters for CodeConstraintsDTO and CodeExecutionSummaryDTO.
     *
     * @return A DynamoDB table for candidate question result entities.
     */
    @Bean
    public DynamoDbTable<CandidateQuestionResult> candidateQuestionResultTable() {
       TableSchema<CandidateQuestionResult> projectTableSchema = TableSchema.fromBean(CandidateQuestionResult.class);
       return dynamoDbEnhancedClient.table(getTableName(), projectTableSchema);
    }

    /**
     * Configures and provides a DynamoDB table for storing candidate test entities.
     *
     * @return A DynamoDB table for candidate test entities.
     */
    @Bean
    public DynamoDbTable<CandidateTest> candidateTestTable() {
        TableSchema<CandidateTest> projectTableSchema = TableSchema.fromBean(CandidateTest.class);
        return dynamoDbEnhancedClient.table(getTableName(), projectTableSchema);
    }

    /**
     * Configures and provides a DynamoDB table for storing test question entities.
     *
     * @return A DynamoDB table for test question entities.
     */
    @Bean
    public DynamoDbTable<TestQuestion> testQuestionTable() {
        TableSchema<TestQuestion> projectTableSchema = TableSchema.fromBean(TestQuestion.class);
        return dynamoDbEnhancedClient.table(getTableName(), projectTableSchema);
    }

    /**
     * Configures and provides a DynamoDB table for storing candidate feedback entities.
     *
     * @return A DynamoDB table for candidate feedback entities.
     */
    @Bean
    public DynamoDbTable<CandidateFeedback> candidateFeedbackTable() {
        TableSchema<CandidateFeedback> projectTableSchema = TableSchema.fromBean(CandidateFeedback.class);
        return dynamoDbEnhancedClient.table(getTableName(), projectTableSchema);
    }

    /**
     * Configures and provides a DynamoDB table for storing flagged question entities.
     *
     * @return A DynamoDB table for flagged question entities.
     */
    @Bean
    public DynamoDbTable<QuestionFlagging> questionFlaggingTable() {
        TableSchema<QuestionFlagging> projectTableSchema = TableSchema.fromBean(QuestionFlagging.class);
        return dynamoDbEnhancedClient.table(getTableName(), projectTableSchema);
    }

    /**
     * Configures and provides a DynamoDB table for storing Webhook Job entities.
     *
     * @return A DynamoDB table for Webhook Job entities.
     */
    @Bean
    public DynamoDbTable<WebhookReportJob> webhookReportJobDynamoDbTable(){
        TableSchema<WebhookReportJob> projecTableSchema = TableSchema.fromBean(WebhookReportJob.class);
        return dynamoDbEnhancedClient.table(getTableName(), projecTableSchema);
    }
}