/**
 * Configuration class for setting up Redis properties, creating a Redis cache manager, and configuring Redis templates
 * for working with Redis data.
 *
 * @see Value
 * @see Configuration
 * @see Bean
 * @see RedisStandaloneConfiguration
 * @see LettuceConnectionFactory
 * @see RedisCacheManager
 * @see RedisCacheConfiguration
 * @see Duration
 * @see RedisTemplate
 * @see StringRedisTemplate
 * @see GenericJackson2JsonRedisSerializer
 * @see RedisSerializationContext
 * @since 1.0
 */
package com.amap.amapreportmanagementservice.config.redis;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;

import java.time.Duration;
import java.util.List;

@Configuration
public class RedisConfig {
    @Value("${spring.data.redis.host}")
    private String redisHost;
    @Value("${spring.data.redis.port}")
    private int redisPort;

    /**
     * Configures and creates a Redis connection factory using Lettuce.
     *
     * @return A Lettuce Redis connection factory.
     */
    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration(redisHost, redisPort);
        return new LettuceConnectionFactory(configuration);
    }

    /**
     * Creates and configures a Redis cache manager with custom cache configurations for different caches.
     *
     * @return A Redis cache manager.
     */
    @Bean
    public RedisCacheManager cacheManager() {
        RedisCacheConfiguration cacheConfig = myDefaultCacheConfig(Duration.ofMinutes(10)).disableCachingNullValues();
        return RedisCacheManager.builder(redisConnectionFactory())
                .cacheDefaults(cacheConfig)
                .withCacheConfiguration("getGeneralDetailsCache", myDefaultCacheConfig(Duration.ofMinutes(1)))
                .withCacheConfiguration("getAssessmentDetailsCache", myDefaultCacheConfig(Duration.ofMinutes(1)))
                .withCacheConfiguration("getCandidateMetricsCache", myDefaultCacheConfig(Duration.ofMinutes(1)))
                .withCacheConfiguration("getComparativeAnalysisCache", myDefaultCacheConfig(Duration.ofMinutes(1)))
                .withCacheConfiguration("getFeedbacks", myDefaultCacheConfig(Duration.ofMinutes(1)))
                .withCacheConfiguration("getAllDetails", myDefaultCacheConfig(Duration.ofMinutes(1)))
                .withCacheConfiguration("getAllComparativeAnalysis", myDefaultCacheConfig(Duration.ofMinutes(1)))
                .withCacheConfiguration("getAllTrajectories", myDefaultCacheConfig(Duration.ofMinutes(1)))
                .withCacheConfiguration("getOrganizationAssignmentCache", myDefaultCacheConfig(Duration.ofMinutes(1)))
                .withCacheConfiguration("getAllComparativeAnalysisCache", myDefaultCacheConfig(Duration.ofMinutes(1)))
                .build();
    }

    /**
     * Creates a custom Redis cache configuration with the specified time-to-live (TTL) duration.
     *
     * @param duration The TTL duration for cache entries.
     * @return A custom Redis cache configuration.
     */
    private RedisCacheConfiguration myDefaultCacheConfig(Duration duration) {
        return RedisCacheConfiguration
                .defaultCacheConfig()
                .entryTtl(duration)
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }

    /**
     * Creates and configures a Redis template for working with Redis data.
     *
     * @return A Redis template for working with Redis data.
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory());
        return template;
    }

    /**
     * Creates and configures a Redis template for working with lists in Redis.
     *
     * @return A Redis template for working with lists in Redis.
     */
    @Bean
    public RedisTemplate<String, List<String>> redisListTemplate() {
        RedisTemplate<String, List<String>> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory());
        return template;
    }

    /**
     * Creates and configures a Redis template for working with strings in Redis.
     *
     * @return A Redis template for working with strings in Redis.
     */
    @Bean
    StringRedisTemplate stringRedisTemplate() {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(redisConnectionFactory());
        return template;
    }

}
