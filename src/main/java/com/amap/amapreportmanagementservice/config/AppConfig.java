/**
 * Configuration class for defining application-level beans and configurations.
 * Contains the following configurations:
 *      necessary configurations for Spring Security's AuthenticationManager.
 *
 * @see org.springframework.context.annotation.Configuration
 * @see lombok.RequiredArgsConstructor
 * @see lombok.extern.slf4j.Slf4j
 */

package com.amap.amapreportmanagementservice.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;

@Configuration
@RequiredArgsConstructor
@Slf4j
public class AppConfig {

    /**
     * Creates a bean for the AuthenticationManager based on the provided AuthenticationConfiguration.
     *
     * @param authenticationConfiguration The AuthenticationConfiguration to obtain the AuthenticationManager from.
     * @return The configured AuthenticationManager.
     * @throws Exception If an error occurs during the AuthenticationManager setup.
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }
}