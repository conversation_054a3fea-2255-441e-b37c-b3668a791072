package com.amap.amapreportmanagementservice.config.httpClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class HttpClient {

    /**
     * Configures http Client
     *
     * @return A map of http client properties.
     */
    @Bean
    public RestTemplate restTemplate(){
        return new RestTemplate();
    }
}
