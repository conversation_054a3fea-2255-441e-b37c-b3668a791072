/**
 * Configuration class for Cross-Origin Resource Sharing (CORS) in the application.
 * This class allows you to specify the allowed origins for cross-origin requests.
 * The origins allowed are specified using the env variable ALLOWED_ORIGINS
 */

package com.amap.amapreportmanagementservice.config.cors;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@RequiredArgsConstructor
public class CorsConfig implements WebMvcConfigurer {
    private final Environment environment;

    /**
     * Configure CORS mappings for the application.
     *
     * @param registry The CORS registry used for configuration.
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**").allowedOrigins(environment.getProperty("ALLOWED_ORIGINS"));
    }
}