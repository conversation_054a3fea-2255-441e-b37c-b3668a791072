/**
 * Configuration class for setting up Kafka consumer properties and creating a Kafka consumer factory
 * for handling incoming messages from Kafka topics.
 *
 * @see Value
 * @see Configuration
 * @see Bean
 * @see ConsumerConfig
 * @see KafkaListenerContainerFactory
 * @see ConcurrentKafkaListenerContainerFactory
 * @see ConsumerFactory
 * @see DefaultKafkaConsumerFactory
 * @since 1.0
 */
package com.amap.amapreportmanagementservice.config.kafka;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaConsumerConfig {
    @Value("${spring.kafka.bootstrap-servers}")
    private String boostrapServers;

    /**
     * Configures Kafka consumer properties and returns a map of properties.
     *
     * @return A map of Kafka consumer properties.
     */
    public Map<String, Object> consumerConfig() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, boostrapServers);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        props.put(JsonDeserializer.TRUSTED_PACKAGES, "*");
        return props;
    }

    /**
     * Creates and returns a Kafka consumer factory based on the configured consumer properties.
     *
     * @return A Kafka consumer factory.
     */
    @Bean
    public ConsumerFactory<String, Object> consumerFactory() {
        return new DefaultKafkaConsumerFactory<>(consumerConfig());
    }

    /**
     * Creates and returns a Kafka listener container factory for handling Kafka messages concurrently.
     *
     * @param consumerFactory The Kafka consumer factory to use.
     * @return A Kafka listener container factory.
     */
    @Bean
    public KafkaListenerContainerFactory<
            ConcurrentMessageListenerContainer<String, Object>> factory(
            ConsumerFactory<String, Object> consumerFactory
    ) {
        ConcurrentKafkaListenerContainerFactory<String, Object> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);

        return factory;
    }
}
