/**
 * Configuration class for setting up Kafka producer properties and creating a Kafka producer factory
 * for sending messages to Kafka topics.
 *
 * @see Value
 * @see Configuration
 * @see Bean
 * @see ProducerConfig
 * @see KafkaTemplate
 * @see DefaultKafkaProducerFactory
 * @see ProducerFactory
 * @see StringSerializer
 * @see JsonSerializer
 * @since 1.0
 */
package com.amap.amapreportmanagementservice.config.kafka;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaProducerConfig {
    @Value("${spring.kafka.bootstrap-servers}")
    private String boostrapServers;

    /**
     * Configures Kafka producer properties and returns a map of properties.
     *
     * @return A map of Kafka producer properties.
     */
    public Map<String, Object> producerConfig() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, boostrapServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        return props;
    }

    /**
     * Creates and returns a Kafka producer factory based on the configured producer properties.
     *
     * @return A Kafka producer factory.
     */
    @Bean
    public ProducerFactory<String, Object> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfig());
    }

    /**
     * Creates and returns a Kafka template for sending Kafka messages.
     *
     * @param producerFactory The Kafka producer factory to use.
     * @return A Kafka template for sending messages to Kafka topics.
     */
    @Bean
    public KafkaTemplate<String, Object> kafkaTemplate(
            ProducerFactory<String, Object> producerFactory
    ) {
        return new KafkaTemplate<>(producerFactory);
    }
}
