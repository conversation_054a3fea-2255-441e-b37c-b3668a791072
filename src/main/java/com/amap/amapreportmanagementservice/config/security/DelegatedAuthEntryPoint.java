/**
 * An authentication entry point that delegates exception handling based on the specific exception encountered.
 */

package com.amap.amapreportmanagementservice.config.security;

import com.amap.amapreportmanagementservice.exceptions.JwtAuthenticationException;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.proc.BadJOSEException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerExceptionResolver;

import java.text.ParseException;

@Component("delegatedAuthenticationEntryPoint")
@Slf4j(topic = "security_logger")
public class DelegatedAuthEntryPoint implements AuthenticationEntryPoint {
    private final HandlerExceptionResolver resolver;

    /**
     * Constructs a DelegatedAuthEntryPoint with a handler exception resolver.
     *
     * @param exceptionResolver The handler exception resolver for resolving exceptions.
     */
    public DelegatedAuthEntryPoint(@Qualifier("handlerExceptionResolver") HandlerExceptionResolver exceptionResolver) {
        this.resolver = exceptionResolver;
    }

    /**
     * Commences authentication by delegating exception handling based on the specific exception encountered.
     *
     * @param request       The HTTP request.
     * @param response      The HTTP response.
     * @param authException The authentication exception.
     */
    @Override
    public void commence(HttpServletRequest request,
                         HttpServletResponse response,
                         AuthenticationException authException) {


        Object authError = request.getAttribute("error");
        switch (authError) {
            case ParseException parseException ->
                    resolver.resolveException(request, response, null, new ParseException(parseException.getMessage(), parseException.getErrorOffset()));
            case BadJOSEException badJOSEException ->
                    resolver.resolveException(request, response, null, new BadJOSEException(badJOSEException.getMessage()));
            case JOSEException joseException ->
                    resolver.resolveException(request, response, null, new JOSEException(joseException.getMessage()));
            case InsufficientAuthenticationException insufficientAuthenticationException ->
                    resolver.resolveException(request, response, null, new InsufficientAuthenticationException(insufficientAuthenticationException.getMessage(), insufficientAuthenticationException.getCause()));
            case JwtAuthenticationException jwtAuthenticationException ->
                    resolver.resolveException(request, response, null, new JwtAuthenticationException(jwtAuthenticationException.getMessage(), jwtAuthenticationException.getCause()));
            case AccessDeniedException accessDeniedException ->
                    resolver.resolveException(request, response, null, new AccessDeniedException(accessDeniedException.getMessage(), accessDeniedException.getCause()));
            case null, default -> resolver.resolveException(request, response, null, authException);
        }

    }
}
