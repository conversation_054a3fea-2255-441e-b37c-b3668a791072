/**
 * This package contains Spring Security configurations for authentication and authorization.
 * It includes various classes and components related to authentication providers, authorization
 * managers, security filters, and configuration settings for securing the application.
 * <p>
 * Classes:
 * - {@link com.amap.amapreportmanagementservice.config.security.authentication_providers.JwtAuthenticationProvider}
 * - {@link com.amap.amapreportmanagementservice.config.security.authorization_managers.UserAuthorizationManager}
 * - {@link com.amap.amapreportmanagementservice.config.security.authorization_tokens.JwtAuthenticationToken}
 * - {@link com.amap.amapreportmanagementservice.config.security.authorization_tokens.UserAuthorizationToken}
 * - {@link com.amap.amapreportmanagementservice.config.security.filters.JwtAuthenticationFilter}
 * - {@link com.amap.amapreportmanagementservice.config.security.filters.OrganizationAuthenticationFilter}
 * - {@link com.amap.amapreportmanagementservice.config.security.DelegatedAuthEntryPoint}
 * - {@link com.amap.amapreportmanagementservice.config.security.SecurityConfig}
 *
 * These configurations are essential for securing the application and managing authentication
 * and authorization processes. The JwtAuthenticationProvider handles JWT authentication,
 * while UserAuthorizationManager manages user authorization. Filters like JwtAuthenticationFilter
 * and OrganizationAuthenticationFilter are responsible for request interception and security checks.
 * The DelegatedAuthEntryPoint provides a custom authentication entry point.
 *
 * @see com.amap.amapreportmanagementservice.config.security.SecurityConfig
 */
package com.amap.amapreportmanagementservice.config.security;

public class package_info {
}
