/**
 * Represents a user-specific authorization token used in Spring Security.
 * This token holds information about the user's organization, role, and authorities.
 */

package com.amap.amapreportmanagementservice.config.security.authorization_tokens;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.*;

public class UserAuthorizationToken extends AbstractAuthenticationToken {
    private final String organizationId;
    private final String role;
    private final Collection<GrantedAuthority> authorities;

    /**
     * Constructs a UserAuthorizationToken with the specified organization ID, role, and permissions.
     *
     * @param organizationId The organization's ID associated with the user.
     * @param role           The user's role.
     * @param permissions    The user's permissions.
     */
    public UserAuthorizationToken(String organizationId, String role, String[] permissions) {
        super(Collections.singletonList(new SimpleGrantedAuthority(role.toUpperCase())));
        this.organizationId = organizationId;
        this.role = role;
        this.authorities = convertPermissionListToGrantedAuthorities(permissions);
    }

    /**
     * Get the user's role as credentials.
     *
     * @return The user's role.
     */
    @Override
    public Object getCredentials() {
        return role;
    }

    /**
     * Get the user's organization ID as principal.
     *
     * @return The organization's ID.
     */
    @Override
    public Object getPrincipal() {
        return organizationId;
    }

    /**
     * Get the user's authorities.
     *
     * @return The user's authorities.
     */
    @Override
    public Collection<GrantedAuthority> getAuthorities() {
        return authorities;
    }

    /**
     * Compare this UserAuthorizationToken with another object for equality.
     *
     * @param o The object to compare.
     * @return True if the objects are equal; otherwise, false.
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserAuthorizationToken that = (UserAuthorizationToken) o;
        return Objects.equals(organizationId, that.organizationId) && Objects.equals(role, that.role);
    }

    /**
     * Generate a hash code for this UserAuthorizationToken.
     *
     * @return The hash code.
     */
    @Override
    public int hashCode() {
        return Objects.hash(organizationId, role);
    }

    // Private methods for internal use
    String convertToUpperSnakeCase(String s) {
        String[] strings = s.split(" ");
        if (strings.length > 1) {
            return strings[0].toUpperCase() + "_" + strings[1].toUpperCase();
        }
        return strings[0].toUpperCase();
    }

    Collection<GrantedAuthority> convertPermissionListToGrantedAuthorities(String[] permissions) {
        Collection<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        for (String permission :
                permissions) {
            grantedAuthorities.add(new SimpleGrantedAuthority(convertToUpperSnakeCase(permission)));
        }
        return grantedAuthorities;
    }
}
