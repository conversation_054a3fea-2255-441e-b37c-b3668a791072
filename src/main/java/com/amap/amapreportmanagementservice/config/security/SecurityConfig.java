/**
 * Configuration class for Spring Security settings and filters.
 */

package com.amap.amapreportmanagementservice.config.security;

import com.amap.amapreportmanagementservice.config.security.authentication_providers.JwtAuthenticationProvider;
import com.amap.amapreportmanagementservice.config.security.authorization_managers.UserAuthorizationManager;
import com.amap.amapreportmanagementservice.config.security.filters.JwtAuthenticationFilter;
import com.amap.amapreportmanagementservice.config.security.filters.OrganizationAuthenticationFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final JwtAuthenticationProvider authenticationProvider;
    @Qualifier("delegatedAuthenticationEntryPoint")
    private final AuthenticationEntryPoint authenticationEntryPoint;
    private final OrganizationAuthenticationFilter organizationAuthenticationFilter;

    /**
     * Configures the security filter chain for handling HTTP security.
     *
     * @param httpSecurity The HttpSecurity object to configure.
     * @return A SecurityFilterChain representing the configured security filter chain.
     * @throws Exception If an error occurs during configuration.
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity  httpSecurity) throws Exception{
        httpSecurity.csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(request -> request
//                        .anyRequest().permitAll());
                        .requestMatchers("/", "/report-service").permitAll()
                        .requestMatchers(
                                "/organizations/{organizationId}/assessments/{assessmentId}/basic-metrics/general-details",
                                "/organizations/{organizationId}/assessments/{assessmentId}/assessment-details",
                                "/organizations/{organizationId}/assessments/{assessmentId}/candidate-metrics",
                                "/organizations/{organizationId}/assessments/{assessmentId}/comparative-analysis",
                                "/organizations/{organizationId}/assessments/{assessmentId}/all-candidates-comparative-analysis",
                                "/organizations/{organizationId}/assessments/{assessmentId}/feedbacks",
                                "/organizations/{organizationId}/assessments/{assessmentId}/all-details",
                                "/organizations/{organizationId}/assessments/{assessmentId}/all-comparative-analysis",
                                "/organizations/{organizationId}/assessments/{assessmentId}/all-candidate-trajectories",
                                "/organizations/{organizationId}/organization-assessments",
                                "/organizations/{organizationId}/assessments/{assessmentId}/candidates-results",
                                "/organizations/{organizationId}/assessments/{assessmentId}/question/{questionId}",
                                "/organizations/{organizationId}/assessments/{assessmentId}/candidate/{candidateId}/assessment-results"
                                )
                        .access(new UserAuthorizationManager("VIEW_REPORTS", "ORGANIZATION_ADMIN", "ADMIN"))

                        .anyRequest().authenticated()
                )
                .sessionManagement(httpSecuritySessionManagementConfigurer -> httpSecuritySessionManagementConfigurer.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authenticationProvider(authenticationProvider)
                .exceptionHandling(httpSecurityExceptionHandlingConfigurer -> httpSecurityExceptionHandlingConfigurer.authenticationEntryPoint(authenticationEntryPoint))
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .addFilterAfter(organizationAuthenticationFilter, jwtAuthenticationFilter.getClass());
        return httpSecurity.build();
    }

}