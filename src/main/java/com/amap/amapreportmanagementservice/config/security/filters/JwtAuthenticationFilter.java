package com.amap.amapreportmanagementservice.config.security.filters;

import com.amap.amapreportmanagementservice.config.security.authorization_tokens.JwtAuthenticationToken;
import com.amap.amapreportmanagementservice.exceptions.JwtAuthenticationException;
import com.amap.amapreportmanagementservice.service.JwtService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.proc.BadJOSEException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.text.ParseException;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    private final JwtService jwtService;
    private final ObjectMapper mapper;

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull FilterChain filterChain) throws ServletException, IOException {
        try {
            Optional<String> authHeader = Optional.ofNullable(request.getHeader("Authorization"));
            authHeader.filter(head -> head.startsWith("Bearer "))
                    .map(head -> head.substring(7))
                    .ifPresent(token -> {
                        Optional<String> usernameOptional;
                        try {
                            usernameOptional = Optional.ofNullable(jwtService.extractUserName(token));
                        } catch (BadJOSEException | ParseException | JOSEException e) {
                            throw new JwtAuthenticationException(e.getMessage());
                        }
                        usernameOptional.filter(username -> SecurityContextHolder.getContext().getAuthentication() == null)
                                .ifPresent(username -> {
                                    try {
                                        if (Boolean.TRUE.equals(jwtService.isTokenValid(token))) {
                                            JwtAuthenticationToken authenticationToken;
                                            authenticationToken = new JwtAuthenticationToken(jwtService.extractString(token, "userId"), jwtService.extractString(token, "role"), mapper.convertValue(jwtService.extract(token, "permissions"), String[].class));
                                            authenticationToken.setAuthenticated(true);
                                            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                                            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                                        }
                                    } catch (BadJOSEException | ParseException | JOSEException e) {
                                        throw new JwtAuthenticationException(e.getMessage());
                                    }
                                });
                    });
            filterChain.doFilter(request, response);
        } catch (Exception e) {
            request.setAttribute("error", e);
            filterChain.doFilter(request, response);
        }
    }
}