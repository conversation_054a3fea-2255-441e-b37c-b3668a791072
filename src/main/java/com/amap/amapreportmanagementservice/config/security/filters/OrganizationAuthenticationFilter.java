/**
 * A Spring Security filter for organization-based authentication.
 * This filter intercepts incoming requests, validates organization-based JWT tokens, and sets the authentication context if the token is valid.
 */

package com.amap.amapreportmanagementservice.config.security.filters;

import com.amap.amapreportmanagementservice.config.security.authorization_tokens.UserAuthorizationToken;
import com.amap.amapreportmanagementservice.exceptions.JwtAuthenticationException;
import com.amap.amapreportmanagementservice.service.JwtService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.proc.BadJOSEException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.text.ParseException;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j(topic = "security_logger")
public class OrganizationAuthenticationFilter extends OncePerRequestFilter {
    private final JwtService jwtService;
    private final ObjectMapper mapper;

    /**
     * Perform the filter logic to authenticate and authorize incoming requests based on organization-based JWT tokens.
     *
     * @param request     The incoming HTTP request.
     * @param response    The HTTP response.
     * @param filterChain The filter chain for processing subsequent filters.
     * @throws ServletException If a servlet error occurs.
     * @throws IOException      If an I/O error occurs.
     */
    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull FilterChain filterChain) throws ServletException, IOException {
        try {
            Optional<String> authHeader = Optional.ofNullable(request.getHeader("Authorization"));
            authHeader.filter(head -> head.startsWith("Bearer "))
                    .map(head -> head.substring(7))
                    .ifPresent(token -> {
                        Optional<String> organizationIdOptional;
                        Optional<String> roleOptional;
                        Optional<String[]> permissionsOptional;
                        try {
                            organizationIdOptional = Optional.ofNullable(jwtService.extractString(token, "organizationId"));
                            roleOptional = Optional.ofNullable(jwtService.extractString(token, "role"));
                            permissionsOptional = Optional.ofNullable(mapper.convertValue(jwtService.extract(token, "permissions"), String[].class));

                        } catch (BadJOSEException | ParseException | JOSEException e) {
                            throw new JwtAuthenticationException(e.getMessage());
                        }
                        if (organizationIdOptional.isPresent()) {
                            String requestUrl = request.getRequestURI();
                            String[] pathVariables = requestUrl.split("/");
                            String organizationIdFromRequest = pathVariables[2];

                            if (organizationIdFromRequest.equals(organizationIdOptional.get())) {
                                UserAuthorizationToken userAuthorizationToken = new UserAuthorizationToken(organizationIdOptional.get(), roleOptional.orElse(null), permissionsOptional.orElse(null));
                                userAuthorizationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                                SecurityContextHolder.getContext().setAuthentication(userAuthorizationToken);
                            }else {
                                UserAuthorizationToken userAuthorizationToken = new UserAuthorizationToken(organizationIdOptional.get(), roleOptional.orElse(null), permissionsOptional.orElse(null));
                                userAuthorizationToken.setAuthenticated(false);
                                userAuthorizationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                                SecurityContextHolder.getContext().setAuthentication(userAuthorizationToken);
                                throw new AccessDeniedException("Access Denied, You do not have access to this resource");
                            }
                        }
                    });
            filterChain.doFilter(request, response);
        } catch (AccessDeniedException e){
            request.setAttribute("error", e);
            filterChain.doFilter(request, response);
        } catch (Exception exception) {
            log.error(exception.getMessage());
            request.setAttribute("error", exception);
            filterChain.doFilter(request, response);
        }
    }
}
