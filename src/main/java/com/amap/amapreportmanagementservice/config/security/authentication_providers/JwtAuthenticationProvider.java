/**
 * Custom authentication provider for JWT (JSON Web Token) authentication.
 * This provider validates JWT tokens and creates a JwtAuthenticationToken object upon successful authentication.
 */

package com.amap.amapreportmanagementservice.config.security.authentication_providers;

import com.amap.amapreportmanagementservice.config.security.authorization_tokens.JwtAuthenticationToken;
import com.amap.amapreportmanagementservice.exceptions.JwtAuthenticationException;
import com.amap.amapreportmanagementservice.service.JwtService;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.proc.BadJOSEException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import java.text.ParseException;

@Component
@RequiredArgsConstructor
@Slf4j(topic = "security_logger")
public class JwtAuthenticationProvider implements AuthenticationProvider {
    @Qualifier("jwtServiceImpl")
    private final JwtService jwtService;

    /**
     * Authenticate the given JWT token and return an Authentication object if successful.
     *
     * @param authentication The authentication request token containing the JWT token as principal.
     * @return An authenticated JwtAuthenticationToken if successful.
     * @throws AuthenticationException If authentication fails due to an invalid or expired JWT token.
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String token = (String) authentication.getPrincipal();
        try {
            String userId = jwtService.extractUserName(token);
            String role = jwtService.extractString(token, "role");
            String[] permissions = (String[]) jwtService.extract(token, "permissions");
            return new JwtAuthenticationToken(userId, role, permissions);
        } catch (BadJOSEException | ParseException | JOSEException e) {
            log.error(e.getMessage());
            throw new JwtAuthenticationException("Invalid JWT token");
        }
    }

     /**
     * Check if this authentication provider supports the given authentication class (JwtAuthenticationToken).
     *
     * @param authentication The class to check.
     * @return True if the authentication class is supported; otherwise, false.
     */
    @Override
    public boolean supports(Class<?> authentication) {
        return JwtAuthenticationToken.class.isAssignableFrom(authentication);
    }
}