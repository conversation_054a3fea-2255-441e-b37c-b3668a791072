/**
 * This class provides authorization management based on user-specific criteria.
 * It checks whether a user is authorized to access a specific resource based on their organization and authorities.
 * Note that in this application's context authorities are permissions, which are obtained from the jwt.
 */

package com.amap.amapreportmanagementservice.config.security.authorization_managers;

import com.amap.amapreportmanagementservice.config.security.authorization_tokens.UserAuthorizationToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authorization.AuthorityAuthorizationDecision;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.AuthorizationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.access.intercept.RequestAuthorizationContext;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.function.Supplier;

@Component
@Slf4j(topic = "security_logger")
public class UserAuthorizationManager implements AuthorizationManager<RequestAuthorizationContext> {
    private final Set<String> authorities;

    /**
     * Constructs a UserAuthorizationManager with the specified authorities.
     *
     * @param authorities The authorities required for authorization.
     */
    public UserAuthorizationManager(String... authorities) {
        this.authorities = Set.of(authorities);
    }

    /**
     * Checks whether the user is authorized to access the specified resource.
     *
     * @param authentication The supplier for retrieving authentication information.
     * @param object         The context of the authorization request.
     * @return An AuthorizationDecision indicating whether access is granted or denied.
     */
    @Override
    public AuthorizationDecision check(Supplier<Authentication> authentication, RequestAuthorizationContext object) {
        try {
            if (authentication.get() instanceof UserAuthorizationToken authToken) {
            String requestUrl = object.getRequest().getRequestURI();
            String[] pathVariables = requestUrl.split("/");
            String organizationIdFromRequest = pathVariables[2];
            String organizationIdUser = (String) authToken.getPrincipal();
            if (organizationIdUser != null && organizationIdUser.equals(organizationIdFromRequest)) {
                Authentication authentication1 = SecurityContextHolder.getContext().getAuthentication();
                if (authentication1.getAuthorities().stream().anyMatch(auth -> authorities.contains(auth.getAuthority()))) {
                    return new AuthorityAuthorizationDecision(true, AuthorityUtils.createAuthorityList(authorities));
                }
            }
            return new AuthorityAuthorizationDecision(false, AuthorityUtils.createAuthorityList(authorities));
        }
        } catch (Exception e){
            log.error(e.getMessage());
            return new AuthorityAuthorizationDecision(false, AuthorityUtils.createAuthorityList(authorities));
        }

        return new AuthorityAuthorizationDecision(false, AuthorityUtils.createAuthorityList(authorities));
    }
}