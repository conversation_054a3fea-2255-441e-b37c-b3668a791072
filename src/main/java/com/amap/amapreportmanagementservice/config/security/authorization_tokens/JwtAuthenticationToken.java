/**
 * Represents a JWT (JSON Web Token) authentication token used for Spring Security authentication.
 * This token holds user-specific information such as the user ID, role, and authorities.
 */

package com.amap.amapreportmanagementservice.config.security.authorization_tokens;

import lombok.Getter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Objects;

public class JwtAuthenticationToken extends AbstractAuthenticationToken {
    private final String userId;
    @Getter
    private final String role;
    private final Collection<GrantedAuthority> authorities;

    /**
     * Constructs a JwtAuthenticationToken with the specified user ID, role, and permissions.
     *
     * @param userId      The user's ID extracted from the JWT token.
     * @param role        The user's role extracted from the JWT token.
     * @param permissions The user's permissions extracted from the JWT token.
     */
    public JwtAuthenticationToken(String userId, String role, String[] permissions) {
        super(Collections.singletonList(new SimpleGrantedAuthority(role)));
        this.userId = userId;
        this.role = role;
        this.authorities = convertPermissionsListToGrantedAuthorities(permissions);
    }

    /**
     * Get the user's role as credentials.
     *
     * @return The user's role.
     */
    @Override
    public Object getCredentials() {
        return role;
    }

    /**
     * Get the user's ID as principal.
     *
     * @return The user's ID.
     */
    @Override
    public Object getPrincipal() {
        return userId;
    }

    /**
     * Get the user's authorities.
     *
     * @return The user's authorities.
     */
    @Override
    public Collection<GrantedAuthority> getAuthorities() {
        return authorities;
    }

    /**
     * Compare this JwtAuthenticationToken with another object for equality.
     *
     * @param o The object to compare.
     * @return True if the objects are equal; otherwise, false.
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        JwtAuthenticationToken that = (JwtAuthenticationToken) o;
        return Objects.equals(userId, that.userId) && Objects.equals(role, that.role);
    }

    /**
     * Generate a hash code for this JwtAuthenticationToken.
     *
     * @return The hash code.
     */
    @Override
    public int hashCode() {
        return Objects.hash(userId, role);
    }

    // Private methods for internal use
    String convertToUpperSnakeCase(String s) {
        String[] strings = s.split(" ");
        if (strings.length > 1) {
            return strings[0].toUpperCase() + "_" + strings[1].toUpperCase();
        }
        return strings[0].toUpperCase();
    }

    Collection<GrantedAuthority> convertPermissionsListToGrantedAuthorities(String[] permissions) {
        Collection<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        for (String permission :
                permissions) {
            grantedAuthorities.add(new SimpleGrantedAuthority(convertToUpperSnakeCase(permission)));
        }
        return grantedAuthorities;
    }

}