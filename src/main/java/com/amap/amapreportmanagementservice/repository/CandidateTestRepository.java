package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.CandidateTest;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.List;

public interface CandidateTestRepository {
    void saveCandidateTest(CandidateTest candidateTest);
    void updateCandidateTest(CandidateTest candidateTest);

    List<CandidateTest> getCandidateTests(Key key);
    CandidateTest getSpecificCandidateTest(Key key);
}
