package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.WebhookReportJob;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.*;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.util.ArrayList;
import java.util.List;

import static com.amap.amapreportmanagementservice.constants.DbKeysPrefixes.WEBHOOK_PREFIX;


@RequiredArgsConstructor
@Slf4j(topic = "repositories_logger")
@Repository
public class WebhookReportJobRepositoryImpl implements  WebhookReportJobRepository{
    private final DynamoDbTable<WebhookReportJob> webhookReportJobTable;

    @Override
    public void saveWebhookReportJob(WebhookReportJob webhookReportJob) {
        try {
            webhookReportJobTable.putItem(webhookReportJob);
        }catch (Exception exception){
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

    @Override
    public void updateWebhookReportJob(WebhookReportJob webhookReportJob) {
        try {
            UpdateItemEnhancedRequest<WebhookReportJob> updateItemEnhancedRequest = UpdateItemEnhancedRequest.builder(WebhookReportJob.class)
                    .item(webhookReportJob)
                    .ignoreNulls(true)
                    .build();
            webhookReportJobTable.updateItem(updateItemEnhancedRequest);
        }catch (Exception exception){
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }


    @Override
    public void deleteWebhookReportJob(Key key) {
        try {

            // Delete the item from the table
            webhookReportJobTable.deleteItem(key);
        } catch (Exception exception) {
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }


    @Override
    public List<WebhookReportJob> getWebhookReportJobs() {

        Expression expression = Expression.builder()
                .expression("begins_with(#sk, :sk)")
                .putExpressionName("#sk", "sk") // Map the attribute name
                .putExpressionValue(":sk", AttributeValue.fromS(WEBHOOK_PREFIX)) // Map the filter value
                .build();
        ScanEnhancedRequest scanEnhancedRequest= ScanEnhancedRequest.builder()
                .filterExpression(expression)
                .build();
        PageIterable<WebhookReportJob> webhookReportJobPageIterable= webhookReportJobTable.scan(scanEnhancedRequest);

        List<WebhookReportJob> webhookReportJobs= new ArrayList<>();
        for (WebhookReportJob webhookReportJob : webhookReportJobPageIterable.items()){
            webhookReportJobs.add((webhookReportJob));
        }
        return webhookReportJobs;
    }

    /**
     * @param key
     * @return
     */
    @Override
    public WebhookReportJob getWebhookReportJob(Key key) {
        try {
            return webhookReportJobTable.getItem(key);
        }catch(Exception exception){
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }
}
