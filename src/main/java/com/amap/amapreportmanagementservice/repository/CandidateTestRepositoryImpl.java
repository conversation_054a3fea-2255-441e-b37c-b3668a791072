/**
 * Implementation of the CandidateTestRepository interface for DynamoDB.
 */
package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.CandidateTest;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Slf4j(topic = "repositories_logger")
@Repository
public class CandidateTestRepositoryImpl implements CandidateTestRepository{
    private final DynamoDbTable<CandidateTest> candidateTestTable;

    /**
     * Save a CandidateTest to the DynamoDB table.
     *
     * @param candidateTest The CandidateTest object to be saved.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public void saveCandidateTest(CandidateTest candidateTest) {
        try {
            candidateTestTable.putItem(candidateTest);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Update an existing CandidateTest in the DynamoDB table.
     *
     * @param candidateTest The updated CandidateTest object.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public void updateCandidateTest(CandidateTest candidateTest) {
        try {
            UpdateItemEnhancedRequest<CandidateTest> updateItemEnhancedRequest =  UpdateItemEnhancedRequest.builder(CandidateTest.class)
                    .item(candidateTest)
                    .ignoreNulls(true)
                    .build();


            candidateTestTable.updateItem(updateItemEnhancedRequest);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

     /**
     * Retrieve a list of CandidateTests from the DynamoDB table using a specified key.
     *
     * @param key The key used to query for CandidateTests.
     * @return A list of retrieved CandidateTests, or an empty list if none are found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public List<CandidateTest> getCandidateTests(Key key) {
        try {
            QueryConditional queryConditional = QueryConditional.sortBeginsWith(key);
            QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build();
            PageIterable<CandidateTest> candidateTestPageIterable = candidateTestTable.query(queryEnhancedRequest);

            List<CandidateTest> candidateTests = new ArrayList<>();

            for (CandidateTest candidateTest : candidateTestPageIterable.items()) {
                candidateTests.add(candidateTest);
            }
            return candidateTests;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve a specific CandidateTest from the DynamoDB table using a specified key.
     *
     * @param key The key used to retrieve the specific CandidateTest.
     * @return The retrieved CandidateTest, or null if not found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public CandidateTest getSpecificCandidateTest(Key key) {
        try {
            return candidateTestTable.getItem(key);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

}
