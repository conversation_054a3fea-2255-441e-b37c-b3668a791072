/**
 * This package contains repository classes that interface with the DynamoDB tables
 * to perform CRUD operations on various entities related to the AMAP Report Management Service.
 * These repository classes provide data access and storage functionality.
 * <p>
 * The package includes repositories for the following entities:
 * <ul>
 *   <li>{@link com.amap.amapreportmanagementservice.repository.AssessmentRepositoryImpl}
 *   </li>
 *   <li>{@link com.amap.amapreportmanagementservice.repository.AssessmentTestRepositoryImpl}
 *   </li>
 *   <li>{@link com.amap.amapreportmanagementservice.repository.CandidateAssessmentRepositoryImpl}
 *   </li>
 *   <li>{@link com.amap.amapreportmanagementservice.repository.CandidateFeedbackRepositoryImpl}
 *   </li>
 *   <li>{@link com.amap.amapreportmanagementservice.repository.CandidateQuestionRepositoryImpl}
 *   </li>
 *   <li>{@link com.amap.amapreportmanagementservice.repository.CandidateTestRepositoryImpl}
 *   </li>
 *   <li>{@link com.amap.amapreportmanagementservice.repository.TestQuestionRepositoryImpl}
 *   </li>
 * </ul>
 * </p>
 * @since 1.0
 * @version 1.0
 */
package com.amap.amapreportmanagementservice.repository;