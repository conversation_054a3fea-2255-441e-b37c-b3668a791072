/**
 * Implementation of the CandidateAssessmentRepository interface for DynamoDB.
 */
package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.CandidateAssessment;
import com.amap.amapreportmanagementservice.entity.CandidateQuestionResult;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;

import java.util.ArrayList;
import java.util.List;

@Repository
@RequiredArgsConstructor
@Slf4j(topic = "repositories_logger")
public class CandidateAssessmentRepositoryImpl implements CandidateAssessmentRepository {
    private final DynamoDbTable<CandidateAssessment> candidateAssessmentTable;
    private final DynamoDbTable<CandidateQuestionResult> candidateQuestionResultTable;

     /**
     * Save a CandidateAssessment to the DynamoDB table.
     *
     * @param candidateAssessment The CandidateAssessment object to be saved.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public void saveCandidateAssessment(CandidateAssessment candidateAssessment) {
        try {
            candidateAssessmentTable.putItem(candidateAssessment);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Update an existing CandidateAssessment in the DynamoDB table.
     *
     * @param candidateAssessment The updated CandidateAssessment object.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public void updateCandidateAssessment(CandidateAssessment candidateAssessment) {
        try {
            UpdateItemEnhancedRequest<CandidateAssessment> updateItemEnhancedRequest = UpdateItemEnhancedRequest.builder(CandidateAssessment.class)
                    .item(candidateAssessment)
                    .ignoreNulls(true)
                    .build();


            candidateAssessmentTable.updateItem(updateItemEnhancedRequest);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

     /**
     * Retrieve a list of CandidateAssessments from the DynamoDB table using a specified key.
     *
     * @param key The key used to query for CandidateAssessments.
     * @return A list of retrieved CandidateAssessments, or an empty list if none are found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public List<CandidateAssessment> getCandidateAssessment(Key key) {
        try {
            QueryConditional queryConditional = QueryConditional.sortBeginsWith(key);
            QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build();
            PageIterable<CandidateAssessment> assessmentPageIterable = candidateAssessmentTable.query(queryEnhancedRequest);
            List<CandidateAssessment> candidateAssessments = new ArrayList<>();

            for (CandidateAssessment candidateAssessment : assessmentPageIterable.items()) {
                candidateAssessments.add(candidateAssessment);
            }

            return candidateAssessments;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve a specific CandidateAssessment from the DynamoDB table using a specified key.
     *
     * @param key The key used to retrieve the specific CandidateAssessment.
     * @return The retrieved CandidateAssessment, or null if not found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public CandidateAssessment getSpecificCandidateAssessment(Key key) {
        try {
            return candidateAssessmentTable.getItem(key);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve a list of CandidateAssessments from the DynamoDB table using a specified key.
     * This method is used for querying the trajectory of a candidate.
     *
     * @param key The key used to query for CandidateAssessments.
     * @return A list of retrieved CandidateAssessments, or an empty list if none are found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public List<CandidateAssessment> getCandidateTrajectory(Key key) {
        try {
            QueryConditional queryConditional = QueryConditional.keyEqualTo(key);
            QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build();
            PageIterable<CandidateAssessment> candidateAssessmentPageIterable = candidateAssessmentTable.query(queryEnhancedRequest);

            List<CandidateAssessment> candidateAssessments = new ArrayList<>();

            for (CandidateAssessment candidateAssessment : candidateAssessmentPageIterable.items()) {
                candidateAssessments.add(candidateAssessment);
            }
            return candidateAssessments;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Updates the isAnswerCorrect field for a specific candidate assessment result.
     * This method recalculates the answer status based on the test taker answers and correct answers.
     *
     * @param candidateAssessment The candidate assessment to update
     * @return The updated candidate assessment
     * @throws ProcessFailedException if the operation fails
     */
    @Override
    public CandidateAssessment updateAnswerCorrectStatus(CandidateAssessment candidateAssessment) {
        try {
            String organizationId = candidateAssessment.getOrganizationId();
            String assessmentId = candidateAssessment.getAssessmentId();
            String candidateId = candidateAssessment.getCandidateId();
            String candidateEmail = candidateAssessment.getCandidateEmail();
            
            // Fetch the question results
            List<CandidateQuestionResult> questionResults = getCandidateQuestionResults(
                organizationId, assessmentId, candidateId, candidateEmail);
            
            if (questionResults != null && !questionResults.isEmpty()) {
                for (CandidateQuestionResult result : questionResults) {
                    // Update isAnswerCorrect based on the logic
                    String answerStatus = determineAnswerStatus(result);
                    result.setIsAnswerCorrect(answerStatus);
                    
                    // Update each question result in the database
                    updateCandidateQuestionResult(result);
                }
            }
            
            return candidateAssessment;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Fetches candidate question results for a specific candidate and assessment.
     *
     * @param organizationId The organization ID
     * @param assessmentId The assessment ID
     * @param candidateId The candidate ID
     * @param candidateEmail The candidate email
     * @return List of candidate question results
     */
    private List<CandidateQuestionResult> getCandidateQuestionResults(String organizationId, String assessmentId, 
                                                                     String candidateId, String candidateEmail) {
        try {
            // Create the partition key for candidate questions
            // Format should match how questions are stored in your DynamoDB table
            String pk = KeyBuilder.candidateQuestionPK(organizationId, assessmentId, "*");
            
            // Query for all questions for this candidate
            QueryConditional queryConditional = QueryConditional.sortBeginsWith(
                Key.builder()
                    .partitionValue(pk)
                    .sortValue(KeyBuilder.candidateQuestionSK(organizationId, assessmentId, "*"))
                    .build()
            );
            
            QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build();
            
            PageIterable<CandidateQuestionResult> resultPageIterable = 
                candidateQuestionResultTable.query(queryEnhancedRequest);
            
            List<CandidateQuestionResult> results = new ArrayList<>();
            for (CandidateQuestionResult result : resultPageIterable.items()) {
                results.add(result);
            }
            
            return results;
        } catch (Exception e) {
            log.error("Failed to fetch candidate question results: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Updates a candidate question result in the database.
     *
     * @param result The question result to update
     */
    private void updateCandidateQuestionResult(CandidateQuestionResult result) {
        try {
            UpdateItemEnhancedRequest<CandidateQuestionResult> updateRequest = 
                UpdateItemEnhancedRequest.builder(CandidateQuestionResult.class)
                    .item(result)
                    .ignoreNulls(true)
                    .build();
            
            candidateQuestionResultTable.updateItem(updateRequest);
        } catch (Exception e) {
            log.error("Failed to update candidate question result: {}", e.getMessage());
        }
    }

    /**
     * Determines the answer status based on test taker answers and correct answers.
     *
     * @param result The candidate question result
     * @return The answer status: "CORRECT", "WRONG", or "SKIPPED"
     */
    private String determineAnswerStatus(CandidateQuestionResult result) {
        List<String> testTakerAnswers = result.getTestTakerAnswers();
        List<String> correctAnswers = result.getCorrectAnswers();
        boolean isAnswered = result.isAnswered();

        // First check if test taker answers are null or empty - return SKIPPED
        if (testTakerAnswers == null || testTakerAnswers.isEmpty() ||
                testTakerAnswers.stream().anyMatch(ans -> ans == null || ans.trim().isEmpty())) {
            return "SKIPPED";
        }

        boolean answersMatch = compareAnswers(testTakerAnswers, correctAnswers);
        return answersMatch ? "CORRECT" : "WRONG";
    }

    private boolean compareAnswers(List<String> testTakerAnswers, List<String> correctAnswers) {
        if (testTakerAnswers == null || correctAnswers == null) {
            // Consider both null or both empty as matching
            return (testTakerAnswers == null || testTakerAnswers.isEmpty()) &&
                    (correctAnswers == null || correctAnswers.isEmpty());
        }

        if (testTakerAnswers.isEmpty() && correctAnswers.isEmpty()) {
            return true;
        }

        // Normalize answers for comparison
        List<String> normalizedTestTakerAnswers = testTakerAnswers.stream()
                .map(answer -> answer != null ? answer.trim().toLowerCase() : "")
                .toList();

        List<String> normalizedCorrectAnswers = correctAnswers.stream()
                .map(answer -> answer != null ? answer.trim().toLowerCase() : "")
                .toList();

        return normalizedCorrectAnswers.stream().allMatch(normalizedTestTakerAnswers::contains) &&
                normalizedTestTakerAnswers.stream().allMatch(normalizedCorrectAnswers::contains);
    }
}
