/**
 * Implementation of the CandidateQuestionRepository interface for DynamoDB.
 */
package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.CandidateQuestionResult;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;

import java.util.ArrayList;
import java.util.List;


@Repository
@RequiredArgsConstructor
@Slf4j(topic = "repositories_logger")
public class CandidateQuestionRepositoryImpl implements CandidateQuestionRepository{
    private final DynamoDbTable<CandidateQuestionResult> candidateQuestionResultTable;

     /**
     * Save a CandidateQuestionResult to the DynamoDB table.
     *
     * @param candidateQuestionResult The CandidateQuestionResult object to be saved.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public void saveCandidateQuestion(CandidateQuestionResult candidateQuestionResult) {
        try {
            candidateQuestionResultTable.putItem(candidateQuestionResult);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }

    }

     /**
     * Update an existing CandidateQuestionResult in the DynamoDB table.
     *
     * @param candidateQuestionResult The updated CandidateQuestionResult object.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public void updateCandidateQuestion(CandidateQuestionResult candidateQuestionResult) {
        try {
            UpdateItemEnhancedRequest<CandidateQuestionResult> updateItemEnhancedRequest =  UpdateItemEnhancedRequest.builder(CandidateQuestionResult.class)
                    .item(candidateQuestionResult)
                    .ignoreNulls(true)
                    .build();
            candidateQuestionResultTable.updateItem(updateItemEnhancedRequest);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

     /**
     * Retrieve a specific CandidateQuestionResult from the DynamoDB table using a specified key.
     *
     * @param key The key used to retrieve the specific CandidateQuestionResult.
     * @return The retrieved CandidateQuestionResult, or null if not found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public CandidateQuestionResult getCandidateQuestion(Key key) {
        try {
            CandidateQuestionResult result = candidateQuestionResultTable.getItem(key);

            // Update isAnswerCorrect field if it's null
            if (result != null && result.getIsAnswerCorrect() == null) {
                result.setIsAnswerCorrect(determineAnswerStatus(result));
                updateCandidateQuestion(result);
            };
            return result;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    private String determineAnswerStatus(CandidateQuestionResult result) {
        List<String> testTakerAnswers = result.getTestTakerAnswers();
        List<String> correctAnswers = result.getCorrectAnswers();
        boolean isAnswered = result.isAnswered();

        // First check if test taker answers are null or empty - return SKIPPED
        if (testTakerAnswers == null || testTakerAnswers.isEmpty() ||
                testTakerAnswers.stream().anyMatch(ans -> ans == null || ans.trim().isEmpty())) {
            return "SKIPPED";
        }

        // If we reach here, testTakerAnswers has valid content
        // Compare answers to determine CORRECT or WRONG
        boolean answersMatch = compareAnswers(testTakerAnswers, correctAnswers);
        return answersMatch ? "CORRECT" : "WRONG";
    }

    private boolean compareAnswers(List<String> testTakerAnswers, List<String> correctAnswers) {
        if (testTakerAnswers == null || correctAnswers == null) {
            // Consider both null or both empty as matching
            return (testTakerAnswers == null || testTakerAnswers.isEmpty()) &&
                    (correctAnswers == null || correctAnswers.isEmpty());
        }

        if (testTakerAnswers.isEmpty() && correctAnswers.isEmpty()) {
            return true;
        }

        // Normalize answers for comparison
        List<String> normalizedTestTakerAnswers = testTakerAnswers.stream()
                .map(answer -> answer != null ? answer.trim().toLowerCase() : "")
                .toList();

        List<String> normalizedCorrectAnswers = correctAnswers.stream()
                .map(answer -> answer != null ? answer.trim().toLowerCase() : "")
                .toList();

        return normalizedCorrectAnswers.stream().allMatch(normalizedTestTakerAnswers::contains) &&
                normalizedTestTakerAnswers.stream().allMatch(normalizedCorrectAnswers::contains);
    }
    /**
     * Retrieve a list of CandidateQuestionResults from the DynamoDB table using a specified key.
     *
     * @param key The key used to query for CandidateQuestionResults.
     * @return A list of retrieved CandidateQuestionResults, or an empty list if none are found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public List<CandidateQuestionResult> getCandidateQuestions(Key key) {
        try {
            QueryConditional queryConditional = QueryConditional.keyEqualTo(key);
            QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build();
            PageIterable<CandidateQuestionResult> candidateQuestionResultPageIterable = candidateQuestionResultTable.query(queryEnhancedRequest);
            List<CandidateQuestionResult> candidateQuestionResults = new ArrayList<>();
            for (CandidateQuestionResult result : candidateQuestionResultPageIterable.items()) {
                // Update isAnswerCorrect field if it's null
                if (result.getIsAnswerCorrect() == null) {
                    result.setIsAnswerCorrect(determineAnswerStatus(result));
                    updateCandidateQuestion(result);
                }
                candidateQuestionResults.add(result);
            }
            return candidateQuestionResults;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

}
