package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.AssessmentTest;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.List;

public interface AssessmentTestRepository {
    void saveAssessmentTest(AssessmentTest assessmentTest);

    void updateAssessmentTest(AssessmentTest assessmentTest);

    List<AssessmentTest> getAssessmentTests(Key key);

    List<AssessmentTest> getAssessmentTestsWithoutCandidateTest(Key key);

    AssessmentTest getAssessmentTest(Key key);

    boolean checkIfExists(String organizationId, String assessmentId, String testId);
}
