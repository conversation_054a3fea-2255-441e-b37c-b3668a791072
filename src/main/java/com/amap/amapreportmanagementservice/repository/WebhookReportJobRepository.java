package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.WebhookReportJob;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.List;

public interface WebhookReportJobRepository {

    void saveWebhookReportJob(WebhookReportJob webhookReportJob);

    void updateWebhookReportJob(WebhookReportJob webhookReportJob);

    void deleteWebhookReportJob(Key key);

    List<WebhookReportJob> getWebhookReportJobs();

    WebhookReportJob getWebhookReportJob(Key key);



}
