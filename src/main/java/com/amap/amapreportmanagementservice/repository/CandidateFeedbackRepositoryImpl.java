/**
 * Implementation of the CandidateFeedbackRepository interface for DynamoDB.
 */
package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.CandidateFeedback;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;

import java.util.ArrayList;
import java.util.List;

@Repository
@RequiredArgsConstructor
@Slf4j(topic = "repositories_logger")
public class CandidateFeedbackRepositoryImpl implements CandidateFeedbackRepository {
    private final DynamoDbTable<CandidateFeedback> candidateFeedbackTable;

     /**
     * Save a CandidateFeedback to the DynamoDB table.
     *
     * @param candidateFeedback The CandidateFeedback object to be saved.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public void saveCandidateFeedback(CandidateFeedback candidateFeedback) {
        try {
            candidateFeedbackTable.putItem(candidateFeedback);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve a specific CandidateFeedback from the DynamoDB table using a specified key.
     *
     * @param key The key used to retrieve the specific CandidateFeedback.
     * @return The retrieved CandidateFeedback, or null if not found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public CandidateFeedback getCandidateFeedback(Key key) {
        try {
            return candidateFeedbackTable.getItem(key);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve a list of CandidateFeedbacks from the DynamoDB table using a specified key.
     *
     * @param key The key used to query for CandidateFeedbacks.
     * @return A list of retrieved CandidateFeedbacks, or an empty list if none are found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public List<CandidateFeedback> getAllFeedbacks(Key key) {
        try {
            QueryConditional queryConditional = QueryConditional.sortBeginsWith(key);
            QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build();
            PageIterable<CandidateFeedback> candidateFeedbackPageIterable = candidateFeedbackTable.query(queryEnhancedRequest);
            List<CandidateFeedback> candidateFeedbacks = new ArrayList<>();

            for (CandidateFeedback candidateFeedback : candidateFeedbackPageIterable.items()) {
                candidateFeedbacks.add(candidateFeedback);
            }

            return candidateFeedbacks;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }
}
