package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.TestQuestion;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.List;

public interface TestQuestionRepository {

    void saveTestQuestion(TestQuestion testQuestion);

    void updateTestQuestion(TestQuestion testQuestion);

    List<TestQuestion> getTestQuestions(Key key);
    List<TestQuestion> getTestQuestionsWithoutCandidate(Key key);

    TestQuestion getTestQuestion(Key key);

    boolean checkIfExist(String organizationId, String assessmentId, String testId, String questionId);
}
