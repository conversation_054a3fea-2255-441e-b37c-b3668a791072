package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.CandidateQuestionResult;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.List;

public interface CandidateQuestionRepository {

    void saveCandidateQuestion(CandidateQuestionResult candidateQuestionResult);

    void updateCandidateQuestion(CandidateQuestionResult candidateQuestionResult);

    CandidateQuestionResult getCandidateQuestion(Key key);
    List<CandidateQuestionResult> getCandidateQuestions(Key key);

}
