/**
 * Implementation of the AssessmentTestRepository interface for DynamoDB.
 */
package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.AssessmentTest;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;

import java.util.ArrayList;
import java.util.List;

@Repository
@RequiredArgsConstructor
@Slf4j(topic = "repositories_logger")
public class AssessmentTestRepositoryImpl implements AssessmentTestRepository {
    private final DynamoDbTable<AssessmentTest> assessmentTestTable;

     /**
     * Save an AssessmentTest to the DynamoDB table.
     *
     * @param assessmentTest The AssessmentTest object to be saved.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public void saveAssessmentTest(AssessmentTest assessmentTest) {
        try {
            assessmentTestTable.putItem(assessmentTest);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }

    }

    /**
     * Update an existing AssessmentTest in the DynamoDB table.
     *
     * @param assessmentTest The updated AssessmentTest object.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public void updateAssessmentTest(AssessmentTest assessmentTest) {
        try {
            UpdateItemEnhancedRequest<AssessmentTest> updateItemEnhancedRequest = UpdateItemEnhancedRequest.builder(AssessmentTest.class)
                    .item(assessmentTest)
                    .ignoreNulls(true)
                    .build();


            assessmentTestTable.updateItem(updateItemEnhancedRequest);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }

    }

    /**
     * Retrieve a list of AssessmentTests from the DynamoDB table using a specified key.
     *
     * @param key The key used to query for AssessmentTests.
     * @return A list of retrieved AssessmentTests, or an empty list if none are found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public List<AssessmentTest> getAssessmentTests(Key key) {
        try {
            QueryConditional queryConditional = QueryConditional.sortBeginsWith(key);

            QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build();
            PageIterable<AssessmentTest> assessmentTestPageIterable = assessmentTestTable.query(queryEnhancedRequest);

            List<AssessmentTest> assessmentTests = new ArrayList<>();

            for (AssessmentTest assessmentTest : assessmentTestPageIterable.items()) {
                assessmentTests.add(assessmentTest);
            }
            return assessmentTests;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

     /**
     * Retrieve a list of AssessmentTests from the DynamoDB table using a specified key,
     * excluding those with a candidate test.
     *
     * @param key The key used to query for AssessmentTests.
     * @return A list of retrieved AssessmentTests, or an empty list if none are found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public List<AssessmentTest> getAssessmentTestsWithoutCandidateTest(Key key) {
        return getAssessmentTests(key);
    }

    /**
     * Retrieve an AssessmentTest from the DynamoDB table using a specified key.
     *
     * @param key The key used to retrieve the AssessmentTest.
     * @return The retrieved AssessmentTest, or null if not found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public AssessmentTest getAssessmentTest(Key key) {
        try {
            return assessmentTestTable.getItem(key);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

      /**
     * Check if an AssessmentTest with the given organizationId, assessmentId, and testId exists in the table.
     *
     * @param organizationId The organizationId of the AssessmentTest.
     * @param assessmentId   The assessmentId of the AssessmentTest.
     * @param testId         The testId of the AssessmentTest.
     * @return True if the AssessmentTest exists, false otherwise.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public boolean checkIfExists(String organizationId, String assessmentId, String testId) {
        try {
            String pk = KeyBuilder.assessmentTestPK(organizationId, assessmentId);
            String sk = KeyBuilder.assessmentTestSK(assessmentId, testId);
            Key key = Key.builder().partitionValue(pk).sortValue(sk).build();
            AssessmentTest getItemResponse = assessmentTestTable.getItem(key);
            return getItemResponse != null;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }
}
