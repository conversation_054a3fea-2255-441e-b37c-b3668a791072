/**
 * Implementation of the TestQuestionRepository interface for DynamoDB.
 */
package com.amap.amapreportmanagementservice.repository;


import com.amap.amapreportmanagementservice.entity.TestQuestion;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.UpdateItemEnhancedRequest;

import java.util.ArrayList;
import java.util.List;

@Repository
@RequiredArgsConstructor
@Slf4j(topic = "repositories_logger")
public class TestQuestionRepositoryImpl implements TestQuestionRepository {
    private final DynamoDbTable<TestQuestion> testQuestionTable;

    /**
     * Save a TestQuestion to the DynamoDB table.
     *
     * @param testQuestion The TestQuestion object to be saved.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public void saveTestQuestion(TestQuestion testQuestion) {
        try {
            testQuestionTable.putItem(testQuestion);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Update an existing TestQuestion in the DynamoDB table.
     *
     * @param testQuestion The updated TestQuestion object.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public void updateTestQuestion(TestQuestion testQuestion) {
        try {
            UpdateItemEnhancedRequest<TestQuestion> updateItemEnhancedRequest = UpdateItemEnhancedRequest.builder(TestQuestion.class)
                    .item(testQuestion)
                    .ignoreNulls(true)
                    .build();


            testQuestionTable.updateItem(updateItemEnhancedRequest);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }

    }

    /**
     * Retrieve a list of TestQuestions from the DynamoDB table using a specified key.
     *
     * @param key The key used to query for TestQuestions.
     * @return A list of retrieved TestQuestions, or an empty list if none are found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public List<TestQuestion> getTestQuestions(Key key) {
        try {
            QueryConditional queryConditional = QueryConditional.sortBeginsWith(key);
            QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build();
            PageIterable<TestQuestion> testQuestionPageIterable = testQuestionTable.query(queryEnhancedRequest);

            List<TestQuestion> testQuestions = new ArrayList<>();

            for (TestQuestion testQuestion : testQuestionPageIterable.items()) {
                testQuestions.add(testQuestion);
            }
            return testQuestions;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve a list of TestQuestions from the DynamoDB table without a candidate.
     *
     * @param key The key used to query for TestQuestions.
     * @return A list of retrieved TestQuestions, or an empty list if none are found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public List<TestQuestion> getTestQuestionsWithoutCandidate(Key key) {
        try {
            QueryConditional queryConditional = QueryConditional.keyEqualTo(key);
            QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build();
            PageIterable<TestQuestion> testQuestionPageIterable = testQuestionTable.query(queryEnhancedRequest);

            List<TestQuestion> testQuestions = new ArrayList<>();

            for (TestQuestion testQuestion : testQuestionPageIterable.items()) {
                testQuestions.add(testQuestion);
            }
            return testQuestions;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Retrieve a specific TestQuestion from the DynamoDB table using a specified key.
     *
     * @param key The key used to retrieve the specific TestQuestion.
     * @return The retrieved TestQuestion, or null if not found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public TestQuestion getTestQuestion(Key key) {
        try {
            return testQuestionTable.getItem(key);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Check if a specific TestQuestion exists in the DynamoDB table.
     *
     * @param organizationId The organization ID.
     * @param assessmentId   The assessment ID.
     * @param testId         The test ID.
     * @param questionId     The question ID.
     * @return true if the TestQuestion exists, false otherwise.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public boolean checkIfExist(String organizationId, String assessmentId, String testId, String questionId) {
        try {
            String pk = KeyBuilder.testQuestionPK(organizationId, assessmentId, testId);
            String sk = KeyBuilder.testQuestionSK(questionId);
            Key key = Key.builder().partitionValue(pk).sortValue(sk).build();
            TestQuestion getItemResponse = testQuestionTable.getItem(key);
            return getItemResponse != null;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }


}
