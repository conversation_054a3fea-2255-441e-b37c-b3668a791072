package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.QuestionFlagging;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;

import java.util.ArrayList;
import java.util.List;

@Repository
@RequiredArgsConstructor
@Slf4j(topic = "repositories_logger")
public class QuestionFlaggingRepositoryImpl implements QuestionFlaggingRepository {
    private final DynamoDbTable<QuestionFlagging> questionFlaggingTable;


    /**
     * Save a QuestionFlagging to the DynamoDB table.
     *
     * @param questionFlagging The QuestionFlagging object to be saved.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public void saveQuestionFlagging(QuestionFlagging questionFlagging) {
        try {
            questionFlaggingTable.putItem(questionFlagging);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }




    /**
     * Retrieve a list of TestQuestions from the DynamoDB table using a specified key.
     *
     * @param key The key used to query for QuestionFlagging.
     * @return A list of retrieved QuestionFlagging, or an empty list if none are found.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public List<QuestionFlagging> getQuestionFlaggings(Key key) {
        try {
            QueryConditional queryConditional = QueryConditional.sortBeginsWith(key);
            QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build();
            PageIterable<QuestionFlagging> guestionFlaggingPageIterable = questionFlaggingTable.query(queryEnhancedRequest);

            List<QuestionFlagging> questionFlaggings = new ArrayList<>();

            for (QuestionFlagging questionFlagging : guestionFlaggingPageIterable.items()) {
                questionFlaggings.add(questionFlagging);
            }
            return questionFlaggings;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Check if a specific TestQuestion exists in the DynamoDB table.
        * @param organizationId The organization ID.
            * @param questionId     The question ID.
            * @return true if the TestQuestion exists, false otherwise.
     * @throws ProcessFailedException if the operation fails.
     */
    @Override
    public boolean checkIfExist(String organizationId, String assessmentId, String questionId) {
        try {
            String pk = KeyBuilder.questionFlaggingPK(organizationId, assessmentId);
            String sk = KeyBuilder.questionFlaggingSK(questionId);
            Key key = Key.builder().partitionValue(pk).sortValue(sk).build();
            QuestionFlagging getItemResponse = questionFlaggingTable.getItem(key);
            return getItemResponse != null;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }

    }


}
