package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.CandidateFeedback;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.List;

public interface CandidateFeedbackRepository {
    void saveCandidateFeedback(CandidateFeedback candidateFeedback);

    CandidateFeedback getCandidateFeedback(Key key);

    List<CandidateFeedback> getAllFeedbacks(Key key);
}
