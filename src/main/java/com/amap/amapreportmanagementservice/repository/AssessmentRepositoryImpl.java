/**
 * Implementation of the AssessmentRepository interface for DynamoDB.
 */
package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.Assessment;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.*;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Slf4j(topic = "repositories_logger")
@Repository
public class AssessmentRepositoryImpl implements AssessmentRepository {
    private final DynamoDbTable<Assessment> assessmentTable;

    /**
     * Save an Assessment to the DynamoDB table.
     *
     * @param assessment The Assessment object to be saved.
     *                   @throws ProcessFailedException if any error is encountered.
     */
    @Override
    public void saveAssessment(Assessment assessment) {
        try{
            assessmentTable.putItem(assessment);
        }catch (Exception exception){
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

    /**
     * Update an existing Assessment in the DynamoDB table.
     *
     * @param assessment The updated Assessment object.
     *                   @throws ProcessFailedException if any error is encountered.
     */
    @Override
    public void updateAssessment(Assessment assessment) {
        try {
            UpdateItemEnhancedRequest<Assessment> updateItemEnhancedRequest = UpdateItemEnhancedRequest.builder(Assessment.class)
                    .item(assessment)
                    .ignoreNulls(true)
                    .build();
            assessmentTable.updateItem(updateItemEnhancedRequest);
        }catch (Exception exception){
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

    /**
     * Retrieve an Assessment from the DynamoDB table using a specified key.
     *
     * @param key The key used to retrieve the Assessment.
     * @return The retrieved Assessment, or null if not found.
     * @throws ProcessFailedException if any error is encountered.
     */
    @Override
    public Assessment getAssessment(Key key) {
        try {
            return assessmentTable.getItem(key);
        }catch (Exception exception){
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

    /**
     * Retrieve a list of Assessments from the DynamoDB table using a specified key.
     *
     * @param key The key used to query for Assessments.
     * @return A list of retrieved Assessments.
     * @throws ProcessFailedException if any error is encountered.
     */
    @Override
    public List<Assessment> getAssessments(Key key) {
        try {
            QueryConditional queryConditional = QueryConditional.keyEqualTo(key);

            QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build();
            PageIterable<Assessment> assessmentPageIterable = assessmentTable.query(queryEnhancedRequest);

            List<Assessment> assessments = new ArrayList<>();

            for (Assessment assessmentTest : assessmentPageIterable.items()) {
                assessments.add(assessmentTest);
            }
            return assessments;
        }catch (Exception exception){
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

    /**
     * Query for Assessments using a Global Secondary Index (GSI) based on organizationId.
     *
     * @param organizationId The organizationId used for querying Assessments in the GSI.
     * @return A list of Assessments that match the query criteria.
     * @throws ProcessFailedException if any error is encountered.
     */
    @Override
    public List<Assessment> queryUsingGSI(String organizationId) {
        try {
            String organizationPk = "ORG#" + organizationId;
            ScanEnhancedRequest scanEnhancedRequest = ScanEnhancedRequest.builder()
                    .filterExpression(Expression.builder()
                            .expression("#93320 = :93320 And begins_with(#93321, :93321)")
                            .putExpressionName("#93320", "organizationId")
                            .putExpressionName("#93321", "sk")
                            .putExpressionValue(":93320", AttributeValue.fromS(organizationId))
                            .putExpressionValue(":93321", AttributeValue.fromS(organizationPk))
                            .build())
                    .build();

            PageIterable<Assessment> response = assessmentTable.scan(scanEnhancedRequest);

            List<Assessment> assessments = new ArrayList<>();
            for (Assessment item : response.items()) {

                assessments.add(item);
            }

            return assessments;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }

    /**
     * Check if an Assessment with the given organizationId and assessmentId exists in the table.
     *
     * @param organizationId The organizationId of the Assessment.
     * @param assessmentId   The assessmentId of the Assessment.
     * @return True if the Assessment exists.
     * @throws ProcessFailedException if any error is encountered.
     */
    @Override
    public boolean checkIfExist(String organizationId, String assessmentId) {
        try {
            String pk = KeyBuilder.assessmentPK(organizationId, assessmentId);
            Key key = Key.builder().partitionValue(pk).sortValue(pk).build();
            Assessment getItemResponse = assessmentTable.getItem(key);
            return getItemResponse != null;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }
    }


}
