package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.Assessment;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.List;


public interface AssessmentRepository {
    void saveAssessment(Assessment assessment);

    void updateAssessment(Assessment assessment);


    Assessment getAssessment(Key key);


    List<Assessment> getAssessments(Key assessmentkey);
    List<Assessment> queryUsingGSI(String gsiPartitionKeyValue);

    boolean checkIfExist(String organizationId, String assessmentId);
}
