package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.CandidateAssessment;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.List;

public interface CandidateAssessmentRepository {
    void saveCandidateAssessment(CandidateAssessment candidateAssessment);
    void updateCandidateAssessment(CandidateAssessment candidateAssessment);
    List<CandidateAssessment> getCandidateAssessment(Key key);
    CandidateAssessment getSpecificCandidateAssessment(Key key);

    List<CandidateAssessment> getCandidateTrajectory(Key key);

    CandidateAssessment updateAnswerCorrectStatus(CandidateAssessment candidateAssessment);
}
