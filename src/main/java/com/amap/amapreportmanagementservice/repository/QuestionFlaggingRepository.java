package com.amap.amapreportmanagementservice.repository;

import com.amap.amapreportmanagementservice.entity.QuestionFlagging;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.List;

public interface QuestionFlaggingRepository {

     void saveQuestionFlagging(QuestionFlagging questionFlagging);
    List<QuestionFlagging> getQuestionFlaggings(Key key);

    boolean checkIfExist(String organizationId, String assessmentId, String questionId);


}
