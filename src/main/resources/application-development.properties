server.port=${SERVER_PORT}
spring.kafka.bootstrap-servers=${KAFKA_BOOTSTRAP_SERVERS}
spring.data.redis.host=${REDIS_HOST}
spring.data.redis.port=${REDIS_PORT}
spring.docker.compose.file=./compose-dev.yaml
spring.docker.compose.profiles.active=development
spring.docker.compose.lifecycle-management=start-and-stop
logging.file.path=/
logging.file.name=logfile.log
sentry.dsn=${sentry.dsn}
sentry.traces-sample-rate=1.0
sentry.exception-resolver-order=-2147483647
#logging.level.root=trace


