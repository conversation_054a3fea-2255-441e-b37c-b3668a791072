<configuration>
    <property name="LOG_PATTERN" value="%d{HH:mm:ss.SSS} %-5level %logger{36} [%file:%line] - %msg%n"/>
    <property name="COLORED_LOG_PATTERN"
              value="%d{HH:mm:ss.SSS} %highlight(%-5level) %cyan(%logger{36}) - %msg%n%throwable{full}"/>
    <property name="APP_LOGS_PATH" value="./src/main/resources/logs"/>

    <appender name="emailAppender" class="ch.qos.logback.classic.net.SMTPAppender">
    <smtpHost>${SMTP_HOST}</smtpHost>
    <smtpPort>${SMTP_PORT}</smtpPort>
    <STARTTLS>true</STARTTLS>
    <asynchronousSending>false</asynchronousSending>
    <username>${EMAIL_USERNAME}</username>
    <password>${EMAIL_PASSWORD}</password>
    <to>${EMAIL_RECIPIENT}</to>
    <from>${EMAIL_SENDER}</from>
    <subject>AMAP-REANA: %logger{20} - %msg</subject>
    <layout class="ch.qos.logback.classic.html.HTMLLayout"/>
</appender>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>${LOG_PATTERN}</pattern>
            </layout>
        </encoder>
    </appender>
    <appender name="APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APP_LOGS_PATH}/application.log</file>
        <encoder>
            <pattern>${COLORED_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${APP_LOGS_PATH}/application-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
    </appender>
    <appender name="SERVICES" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APP_LOGS_PATH}/services.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${APP_LOGS_PATH}/services-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
    </appender>
    <appender name="REPOSITORIES" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APP_LOGS_PATH}/repositories.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${APP_LOGS_PATH}/repositories-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
    </appender>
    <appender name="SECURITY" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APP_LOGS_PATH}/security.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${APP_LOGS_PATH}/security-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
    </appender>
    <!--    appenders for project dependencies-->
    <appender name="REDIS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APP_LOGS_PATH}/redis.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${APP_LOGS_PATH}/redis-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
    </appender>
    <appender name="SPRING" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APP_LOGS_PATH}/springboot.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${APP_LOGS_PATH}/springboot-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
    </appender>
    <appender name="DYNAMO-DB" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APP_LOGS_PATH}/dynamodb.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${APP_LOGS_PATH}/dynamodb-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
    </appender>
    <appender name="KAFKA" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APP_LOGS_PATH}/kafka.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${APP_LOGS_PATH}/kafka-%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
    </appender>

    <logger name="application_logger" level="INFO">
        <appender-ref ref="APPLICATION"/>
        <appender-ref ref="console"/>
    </logger>
    <logger name="redis_logger" level="INFO">
        <appender-ref ref="REDIS"/>
        <appender-ref ref="console"/>
    </logger>
    <logger name="dynamodb_logger" level="INFO">
        <appender-ref ref="DYNAMO-DB"/>
        <appender-ref ref="console"/>
    </logger>
    <logger name="security_logger" level="INFO">
        <appender-ref ref="SECURITY"/>
        <appender-ref ref="console"/>
    </logger>
    <logger name="services_logger" level="INFO">
        <appender-ref ref="SERVICES"/>
        <appender-ref ref="console"/>
    </logger>
    <logger name="repositories_logger" level="INFO">
        <appender-ref ref="REPOSITORIES"/>
        <appender-ref ref="console"/>
    </logger>
    <logger name="application_logger" level="INFO">
        <appender-ref ref="APPLICATION"/>
        <appender-ref ref="console"/>
    </logger>
    <!--    loggers for project dependencies-->
    <logger name="org.springframework.boot" level="ERROR">
        <appender-ref ref="SPRING"/>
        <appender-ref ref="console"/>
    </logger>
    <logger name="org.springframework.kafka" level="ERROR">
        <appender-ref ref="KAFKA"/>
        <appender-ref ref="console"/>
    </logger>
    <logger name="software.amazon.awssdk" level="ERROR">
        <appender-ref ref="DYNAMO-DB"/>
        <appender-ref ref="console"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="emailAppender"/>
    </root>
</configuration>