# Application Configuration
NODE_ENV=development
PORT=3000

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key
DYNAMODB_ENDPOINT=http://localhost:8000
DYNAMODB_TABLE_NAME=amap-report-management

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_CLIENT_ID=amap-report-service
KAFKA_GROUP_ID=amap-report-group

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Sentry Configuration (Optional)
SENTRY_DSN=your-sentry-dsn
SENTRY_TRACES_SAMPLE_RATE=1.0

# Logging
LOG_LEVEL=info
