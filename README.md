#Setting Up Locally For Development
---

Table Of Contents


- [Setting Up Locally For Development](#setting-up-locally-for-development)
  - [Requirements](#requirements)
  - [Clone the Repository](#Clone-the-Repository)
  - [Set Environment Variables](#Set-environment-variables)
  - [Build the Project](#build-the-project)
  - [Start Docker Compose Services](#start-docker-compose-services)
  - [Run the Spring Boot Application:](#Run-the-Spring-Boot-Application)
  - [Access the Application](#Access-the-Application)


## Requirements
    
**Java**

[Click To Download Java SE 20](https://www.oracle.com/java/technologies/javase/jdk20-archive-downloads.html)

**Docker**

[Click To Download Docker](https://www.docker.com/get-started/)

**Maven**

[Click To Download Maven](https://maven.apache.org/download.cgi)

---
## Clone the Repository

Clone the project and navigate into the main project folder 

---

## Set Environment Variables

The project requires environment variables.
Refer to the .env.example for what they are and what they should contain.
You can either define these environment variables directly on your system or create an environment file
(e.g., a .env file) that contains the required variables.
Make sure you have the necessary values for these environment variables.

---
## Build the Project

Use Maven to build the Spring Boot project. Navigate to the project's root directory and run the following command:
```bash
mvn clean install
```
This will compile the project, run tests, and package it into a JAR or WAR file.

---
## Start Docker Compose Services
The project includes a docker-compose.yml file that defines services required for the application
(e.g., databases, message brokers);
you can start these services using Docker Compose.
Navigate to the directory containing the docker-compose.yml file and run:
```docker
docker-compose up -d
```
or
```docker
docker compose up -d
```
This command will start the defined services in detached mode.
Ensure that Docker is installed and running on your system.

---
## Run the Spring Boot Application
After building the project and starting the required services, you can run the Spring Boot application.
Use the following command from the project's root directory:
```bash
java --enable-preview -jar target/application.jar
```
---
## Access the Application

Once the Spring Boot application is running,
you can access it using the application's URL.
The application should be available at http://localhost:5000 by default.

---