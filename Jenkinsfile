def appname = "dodokpo-codedeploy-app"
def deploy_group = "dodokpo-report-service-dev"
def deploy_group_staging =  "dodokpo-report-service-staging"
def deploy_group_prod = "dodokpo-report-service-production"
def s3_bucket = "dodokpo-artefacts"
def s3_filename = "dodokpo-codedeploy-src-report-service"

//Slack Notification Integration
def gitName = env.GIT_BRANCH
def jobName = env.JOB_NAME
def branchName = env.BRANCH_NAME
def main_branch = ['staging', 'develop']

// Environments Declaration
environment {
  jobName = env.JOB_NAME
  branchName = env.BRANCH_NAME
}

// Successful Build
def buildSuccess = [
  [text: "AMAP Report Management Service Build Successful on ${branchName}",
  fallback: "AMAP Report Management Service Build Successful on ${branchName}",
  color: "#00FF00"
  ]
]

// Failed Build
def buildError = [
  [text: "AMAP Report Management Service Build Failed on ${branchName}",
  fallback: "AMAP Report Management Service Build Failed on ${branchName}",
  color: "#FF0000"
  ]
]

pipeline {
  agent any

  tools {
    jdk "jdk21"
  }

  stages {
    
    stage('SonarQube Analysis') {
        when {
                  branch 'develop';
                }
      steps{
        script{
          def mvn = tool 'maven';

          withSonarQubeEnv('SonarQube') {
            sh "${mvn}/bin/mvn clean verify sonar:sonar -Dsonar.projectKey=Amali-Tech_amap-report-management-service_AY5hTNuyiBozlGzLDGFy -Dsonar.projectName='amap-report-management-service' -Dsonar.coverage.exclusions=**/constants/*.java,**/dto/**/*.java,**/entity/*.java,**/exceptions/*.java"
          }
        }
      }
    }

     stage('Prepare to Deploy') {
         when {
            anyOf {
              branch 'staging';
              branch 'develop';
              branch 'main';
            }
         }

       steps {
         withAWS(region:'eu-west-1',credentials:'dodokpo-aws-creds') {
           script {
             def gitsha = sh(script: 'git log -n1 --format=format:"%H"', returnStdout: true)
             s3_filename = "${s3_filename}-${gitsha}"
             sh """
                 aws deploy push \
                 --application-name ${appname} \
                 --description "This is a revision for the ${appname}-${gitsha}" \
                 --no-ignore-hidden-files \
                 --s3-location s3://${s3_bucket}/${s3_filename}.zip \
                 --source .
               """
           }
         }
       }
     }

	 stage('Deploy to Development') {
         when {
             branch 'develop'
         }

       steps {
         withAWS(region:'eu-west-1',credentials:'dodokpo-aws-creds') {
           script {
             sh """
                 aws deploy create-deployment \
                 --application-name ${appname} \
                 --deployment-config-name CodeDeployDefault.OneAtATime \
                 --deployment-group-name ${deploy_group} \
                 --file-exists-behavior OVERWRITE \
                 --s3-location bucket=${s3_bucket},key=${s3_filename}.zip,bundleType=zip
               """
           }
         }
	   }
	 }

    stage('Deploy To Staging') {
      when {
        branch 'staging'
      }

      steps {
        withAWS(region:'eu-west-1',credentials:'dodokpo-aws-creds') {
          script {
            sh """
                aws deploy create-deployment \
                --application-name ${appname} \
                --deployment-config-name CodeDeployDefault.OneAtATime \
                --deployment-group-name ${deploy_group_staging} \
                --file-exists-behavior OVERWRITE \
                --s3-location bucket=${s3_bucket},key=${s3_filename}.zip,bundleType=zip
              """
          }
        }
      }
    }
  
  stage('Deploy To Production') {
      when {
        branch 'main'
      }
      steps {
        withAWS(region:'eu-west-1',credentials:'dodokpo-aws-creds') {
          script {
            sh """
                aws deploy create-deployment \
                --application-name ${appname} \
                --deployment-config-name CodeDeployDefault.OneAtATime \
                --deployment-group-name ${deploy_group_prod} \
                --file-exists-behavior OVERWRITE \
                --s3-location bucket=${s3_bucket},key=${s3_filename}.zip,bundleType=zip
              """
          }
        }
      }
    }

  stage('Clean WS') {
    steps {
        cleanWs()
      }
   	}
 }

 post {
    always {
      echo 'One way or another, I have finished'
      cleanWs()
    }

    success {
      script {
        if (BRANCH_NAME in main_branch) {
            slackSend(channel:"imocha-amap", attachments: buildSuccess)
          }
      }

      // withAWS(region:'eu-west-1',credentials:'dodokpo-aws-creds') {
      //   sh 'aws ses send-email --from <EMAIL> --to <EMAIL> --subject "Deployment passed" --text "AMAP Report Management Service Deployment passed"'
      // 		}
    }

    unstable {
      echo 'I am unstable :/'
    }

    failure {
    script {
      if (BRANCH_NAME in main_branch) {
          slackSend(channel:"imocha-amap", attachments: buildError)
          }
    }

      // withAWS(region:'eu-west-1',credentials:'dodokpo-aws-creds') {
      //   sh 'aws ses send-email --from <EMAIL> --to <EMAIL> --subject "Deployment failed" --text "AMAP Report Management Service Deployment failed"'
      // }
    }
    
    changed {
      echo 'Things were different before...'
    	}
  }
}